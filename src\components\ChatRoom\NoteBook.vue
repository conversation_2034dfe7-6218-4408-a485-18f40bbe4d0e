<script setup lang="ts">
import { ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import MeetTab from '@/components/ChatRoom/Tabs/MeetTab.vue';
import ImageTab from '@/components/ChatRoom/Tabs/ImageTab.vue';
import FileTab from '@/components/ChatRoom/Tabs/FileTab.vue';
import LinkTab from '@/components/ChatRoom/Tabs/LinkTab.vue';
import NoteTab from '@/components/ChatRoom/Tabs/NoteTab.vue';

const props = defineProps({
  roomId: {
    type: String,
    required: true
  },
  isDesigner: {
    type: Boolean,
    required: true
  }
});
const noteBookRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const noteTabSelect = ref<NoteTabEnum>(NoteTabEnum.NOTE);

const enum NoteTabEnum {
  MEET = 'meet',
  NOTE = 'note',
  IMAGE = 'image',
  FILE = 'file',
  LINK = 'link',
}

const openModal = () => {
  noteBookRef.value?.openModal();
};

const closeModal = () => {
  noteBookRef.value?.closeModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="紀錄本" ref="noteBookRef"
                :click-outside-close="false"
                :showCloseButton="true"
                modal-width="max-w-6xl"
                @closeModal="closeModal">
    <div class="flex-col m-3">
      <div class="justify-between p-4 mb-4 border-b rounded w-full">
        <div class="flex max-md:flex-col max-md:w-full justify-center gap-2">
          <label
            :class="['md:w-1/6 flex gap-x-2 justify-center items-center t p-2 rounded-xl cursor-pointer cus-btn cus-border',noteTabSelect === NoteTabEnum.MEET ? 'border-gray-400':'bg-white text-black']">
            <input type="radio" name="order" :value="NoteTabEnum.MEET" v-model="noteTabSelect" class="hidden">
            會議
          </label>
          <label
            :class="['md:w-1/6 flex gap-x-2 justify-center items-center p-2 rounded-xl cursor-pointer cus-btn cus-border',noteTabSelect === NoteTabEnum.NOTE ? 'border-gray-400':'bg-white text-black']">
            <input type="radio" name="order" :value="NoteTabEnum.NOTE" v-model="noteTabSelect" class="hidden">
            記事
          </label>
          <label
            :class="['md:w-1/6 flex gap-x-2 justify-center items-center p-2 rounded-xl cursor-pointer cus-btn cus-border',noteTabSelect === NoteTabEnum.IMAGE ? 'border-gray-400':'bg-white text-black']">
            <input type="radio" name="order" :value="NoteTabEnum.IMAGE" v-model="noteTabSelect" class="hidden">
            相簿
          </label>
          <label
            :class="['md:w-1/6 flex gap-x-2 justify-center items-center p-2 rounded-xl cursor-pointer cus-btn cus-border',noteTabSelect === NoteTabEnum.FILE ? 'border-gray-400':'bg-white text-black']">
            <input type="radio" name="order" :value="NoteTabEnum.FILE" v-model="noteTabSelect" class="hidden">
            檔案
          </label>
          <label
            :class="['md:w-1/6 flex gap-x-2 justify-center items-center p-2 rounded-xl cursor-pointer cus-btn cus-border',noteTabSelect === NoteTabEnum.LINK ? 'border-gray-400':'bg-white text-black']">
            <input type="radio" name="order" :value="NoteTabEnum.LINK" v-model="noteTabSelect" class="hidden">
            連結
          </label>
        </div>
      </div>

      <!--      TODO 各項目顯示區-->
      <div v-show="noteTabSelect === NoteTabEnum.MEET">
        <MeetTab :room-id="props.roomId" :is-designer="isDesigner" />
      </div>
      <div v-show="noteTabSelect === NoteTabEnum.NOTE">
        <NoteTab :room-id="props.roomId" :is-designer="isDesigner" />
      </div>
      <div v-show="noteTabSelect === NoteTabEnum.IMAGE">
        <ImageTab :room-id="props.roomId" :is-designer="isDesigner" />
      </div>
      <div v-show="noteTabSelect === NoteTabEnum.FILE">
        <FileTab :room-id="props.roomId" :is-designer="isDesigner" />
      </div>
      <div v-show="noteTabSelect === NoteTabEnum.LINK">
        <LinkTab :room-id="props.roomId" :is-designer="isDesigner" />
      </div>

    </div>
  </DefaultModal>
</template>
