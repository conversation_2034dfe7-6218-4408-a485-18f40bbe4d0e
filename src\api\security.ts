import BaseApiService from './baseApiService.ts';
import axios from 'axios';
import { toastError } from '@/utils/toastification.ts';
import type {
  CheckVerifyCodeResponse,
  GetGuestTokenResponse,
  MountEventResponse,
  UserLoginResponse,
  RegisterAndLoginResponse,
  SendVerifyCodeResponse,
  UserLogoutResponse,
  DeleteAccountResponse
} from '@/model/response/securityResponse.ts';

import type {
  CheckVerifyCodeRequest,
  GetGuestTokenRequest,
  MountEventRequest,
  UserLoginRequest,
  RegisterAndLoginRequest,
  SendVerifyCodeRequest,
  UserLogoutRequest,
  DeleteAccountRequest
} from '@/model/request/securityRequest.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

/**
 * @description: 取得 設計師 GuestToken
 */
export const getDesignerGuestToken = async (requestData: GetGuestTokenRequest): Promise<GetGuestTokenResponse> => {
  const baseApiPath = import.meta.env.VITE_API_URL;
  const response = await axios.post(`${baseApiPath}/Security/Designer/GetGuestToken`, requestData);
  if (response.data.status !== APIStatusCodeEnum.Success) {
    toastError('取得設計師端 GuestToken 發生錯誤');
  }
  return response.data as GetGuestTokenResponse;
};

/**
 * @description: 取得 客戶 GuestToken
 */
export const getCustomerGuestToken = async (requestData: GetGuestTokenRequest): Promise<GetGuestTokenResponse> => {
  const baseApiPath = import.meta.env.VITE_API_URL;
  const response = await axios.post(`${baseApiPath}/Security/Customer/GetGuestToken`, requestData);
  if (response.data.status !== APIStatusCodeEnum.Success) {
    toastError('取得客戶端 GuestToken 發生錯誤');
  }
  return response.data as GetGuestTokenResponse;
};

export class SecurityService {
  /**
   * @description: 客戶更新裝置資訊並判斷金鑰是否為登入狀態
   */
  static async mountCheckLoginByCustomer(requestData: MountEventRequest): Promise<MountEventResponse> {
    const response = await BaseApiService.Customer.post(('/Security/MountEvent'), requestData);
    return response.data as MountEventResponse;
  }

  /**
   * @description: 設計師更新裝置資訊並判斷金鑰是否為登入狀態
   */
  static async mountCheckLoginByDesigner(requestData: MountEventRequest): Promise<MountEventResponse> {
    const response = await BaseApiService.Designer.post(('/Security/MountEvent'), requestData);
    return response.data as MountEventResponse;
  }

  /**
   * @description: 客戶發送驗證簡訊
   */
  static async sendVerifyCodeByCustomer(requestData: SendVerifyCodeRequest): Promise<SendVerifyCodeResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/Sms/Send'), requestData);
    return response.data as SendVerifyCodeResponse;
  }

  /**
   * @description: 設計師發送驗證簡訊
   */
  static async sendVerifyCodeByDesigner(requestData: SendVerifyCodeRequest): Promise<SendVerifyCodeResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/Sms/Send'), requestData);
    return response.data as SendVerifyCodeResponse;
  }

  /**
   * @description: 客戶確認驗證碼
   */
  static async checkVerifyCodeByCustomer(requestData: CheckVerifyCodeRequest): Promise<CheckVerifyCodeResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/Sms/Check'), requestData);
    return response.data as CheckVerifyCodeResponse;
  }

  /**
   * @description: 設計師確認驗證碼
   */
  static async checkVerifyCodeByDesigner(requestData: CheckVerifyCodeRequest): Promise<CheckVerifyCodeResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/Sms/Check'), requestData);
    return response.data as CheckVerifyCodeResponse;
  }

  /**
   * @description: 客戶註冊
   */
  static async registerAndLoginByCustomer(requestData: RegisterAndLoginRequest): Promise<RegisterAndLoginResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/Register'), requestData);
    return response.data as RegisterAndLoginResponse;
  }

  /**
   * @description: 設計師註冊
   */
  static async registerAndLoginByDesigner(requestData: RegisterAndLoginRequest): Promise<RegisterAndLoginResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/Register'), requestData);
    return response.data as RegisterAndLoginResponse;
  }

  /**
   * @description: 客戶登入
   */
  static async userLoginByCustomer(requestData: UserLoginRequest): Promise<UserLoginResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/Login'), requestData);
    return response.data as UserLoginResponse;
  }

  /**
   * @description: 設計師登入
   */
  static async userLoginByDesigner(requestData: UserLoginRequest): Promise<UserLoginResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/Login'), requestData);
    return response.data as UserLoginResponse;
  }

  /**
   * @description: 客戶登出，註銷客戶Token
   */
  static async LogoutByCustomer(requestData: UserLogoutRequest): Promise<UserLogoutResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/Logout'), requestData);
    return response.data as UserLogoutResponse;
  }

  /**
   * @description: 客戶登出，註銷客戶Token
   */
  static async LogoutByDesigner(requestData: UserLogoutRequest): Promise<UserLogoutResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/Logout'), requestData);
    return response.data as UserLogoutResponse;
  }

  /**
   * @description: 客戶刪除帳號
   */
  static async deleteAccountByCustomer(requestData: DeleteAccountRequest): Promise<DeleteAccountResponse> {
    const response = await BaseApiService.Customer.post(('/Security/Customer/DeleteAccount'), requestData);
    return response.data as DeleteAccountResponse;
  }

  /**
   * @description: 客戶刪除帳號
   */
  static async deleteAccountByDesigner(requestData: DeleteAccountRequest): Promise<DeleteAccountResponse> {
    const response = await BaseApiService.Designer.post(('/Security/Designer/DeleteAccount'), requestData);
    return response.data as DeleteAccountResponse;
  }


  // /**
  //  * @description: 客戶重設密碼
  //  */
  // static async resetPasswordByCustomer(requestData: ResetPasswordRequest, identity: UserTypeEnum): Promise<ResetPasswordResponse> {
  //   const response = await BaseApiService.Customer.post(('/Security/Customer/ResetPassword'), requestData);
  //   return response.data as ResetPasswordResponse;
  // }
  //
  // /**
  //  * @description: 設計師重設密碼
  //  */
  // static async resetPasswordByDesigner(requestData: ResetPasswordRequest, identity: UserTypeEnum): Promise<ResetPasswordResponse> {
  //   const response = await BaseApiService.Designer.post(('/Security/Designer/ResetPassword'), requestData);
  //   return response.data as ResetPasswordResponse;
  // }
}
