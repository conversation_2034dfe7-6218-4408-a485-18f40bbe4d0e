// import { measureTimeFormatter } from '@/utils/timeFormat.ts';
import { budgetCombineLowToHigh, constructionBudgetFormat, moneyAddCommas } from '@/utils/budgetFormat';
import { constructionCardHint, designCardHint } from '@/utils/cardHintFormat.ts';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import {
  AcceptedOrderCardData,
} from '@/model/response/orderCard/manyOrderCardResponse.ts';
import {
  // MeasureCardData,
  DesignCardData,
  ConstructionCardData,
  AcceptedOrderType
} from '@/model/formatted/orderCard.ts';
import {
  // MeasureOrderStatusEnum,
  DesignOrderStatusEnum,
  ConstructionOrderStatusEnum
} from '@/model/enum/orderStatus.ts';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';

export const acceptedOrderCardFormat = (data: AcceptedOrderCardData[]): AcceptedOrderType[] => {
  const formattedOrders: any = [];
  for (const order of data) {
    let formatResult;
    switch (order.orderType) {
      case OrderTypeEnum.Measure: //排除丈量訂單
        // if (order.measureAccepted.status === MeasureOrderStatusEnum.WaitingUpload || order.measureAccepted.status === MeasureOrderStatusEnum.SurveyorComing) {
        //   formatResult = acceptedMeasureCardFormat(order);
        // } else continue;
        break;
      case OrderTypeEnum.Design:
        if (order.designAccepted.status === DesignOrderStatusEnum.Working) {
          formatResult = acceptedDesignCardFormat(order);
        } else continue;
        break;
      case OrderTypeEnum.Construction:
        if (order.constructionAccepted.status === ConstructionOrderStatusEnum.Working) {
          formatResult = acceptedConstructionCardFormat(order);
        } else continue;
        break;
    }
    if (formatResult !== undefined) {
      formattedOrders.push(formatResult);
    }
  }
  return formattedOrders;
};

// const acceptedMeasureCardFormat = (order: AcceptedOrderCardData): MeasureCardData => {
//   return {
//     orderType: order.orderType,
//     orderId: order.orderId,
//     orderTitle: '空間丈量',
//     customerName: order.customerName,
//     address: order.address,
//     measureTime: measureTimeFormatter(order.measureAccepted.measureTime),
//     cardHint: measureCardHint(order.measureAccepted.measureTime, order.measureAccepted.status)
//   };
// };

const acceptedDesignCardFormat = (order: AcceptedOrderCardData): DesignCardData => {
  const areaPingResult = JSONStringToObject(order.designAccepted.areaPing);
  return {
    orderType: order.orderType,
    orderId: order.orderId,
    orderTitle: '室內設計',
    customerName: order.customerName,
    address: order.address,
    areaPing: `${areaPingResult.content.text}坪`,
    designAmount: moneyAddCommas(order.designAccepted.designAmount),
    constructionBudget: budgetCombineLowToHigh(order.designAccepted.constructionBudget),
    cardHint: designCardHint(order.designAccepted.subStatus)
  };
};

const acceptedConstructionCardFormat = (order: AcceptedOrderCardData): ConstructionCardData => {
  const houseTypeResult = JSONStringToObject(order.constructionAccepted.houseType);
  const areaPingResult = JSONStringToObject(order.constructionAccepted.areaPing);
  return {
    orderType: order.orderType,
    orderId: order.orderId,
    orderTitle: '裝潢施工',
    customerName: order.customerName,
    address: order.address,
    houseType: houseTypeResult.content.text,
    areaPing: `${areaPingResult.content.text}坪`,
    constructionBudget: constructionBudgetFormat(order.constructionAccepted.constructionAmount, order.constructionAccepted.constructionDiscountAmount),
    cardHint: constructionCardHint()
  };
};
