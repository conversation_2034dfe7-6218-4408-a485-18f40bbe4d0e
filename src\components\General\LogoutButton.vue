<script setup lang="ts">
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

const props = defineProps({
  isDesigner: {
    type: Boolean,
    required: true
  }
});

const userInfoStore = props.isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();

const logout = () => {
  userInfoStore.logout();
};

</script>

<template>
  <!--  ml-4 md:ml-6-->
  <div class="flex items-center">
    <button v-if="userInfoStore.loginState" type="button" @click="logout()"
            class=" flex-shrink-0 rounded-md border border-amber-950 px-2 py-1 text-black focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black">
      <a>登出</a>
    </button>
  </div>
</template>
