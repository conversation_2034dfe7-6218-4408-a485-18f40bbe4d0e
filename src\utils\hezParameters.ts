import { OrderSelection } from '@/model/general/orderSelection.ts';
import { RegionEnum } from '@/model/enum/taiwanRegion.ts';

/**
 * 工作縣市常數
 */
export const workRegion = [
  {
    name: '台北市',
    checked: false,
    regionId: RegionEnum.TaipeiCity
  },
  {
    name: '新北市',
    checked: false,
    regionId: RegionEnum.NewTaipeiCity
  },
  {
    name: '桃園市',
    checked: false,
    regionId: RegionEnum.TaoyuanCity
  },
  {
    name: '台中市',
    checked: false,
    regionId: RegionEnum.TaichungCity
  },
  {
    name: '台南市',
    checked: false,
    regionId: RegionEnum.TainanCity
  },
  {
    name: '高雄市',
    checked: false,
    regionId: RegionEnum.KaohsiungCity
  },
  {
    name: '基隆市',
    checked: false,
    regionId: RegionEnum.KeelungCity
  },
  {
    name: '新竹市',
    checked: false,
    regionId: RegionEnum.HsinchuCity
  },
  {
    name: '新竹縣',
    checked: false,
    regionId: RegionEnum.HsinchuCounty
  },
  {
    name: '苗栗縣',
    checked: false,
    regionId: RegionEnum.MiaoliCounty
  },
  {
    name: '彰化縣',
    checked: false,
    regionId: RegionEnum.ChanghuaCounty
  },
  {
    name: '南投縣',
    checked: false,
    regionId: RegionEnum.NantouCounty
  },
  {
    name: '雲林縣',
    checked: false,
    regionId: RegionEnum.YunlinCounty
  },
  {
    name: '嘉義市',
    checked: false,
    regionId: RegionEnum.ChiayiCity
  },
  {
    name: '嘉義縣',
    checked: false,
    regionId: RegionEnum.ChiayiCounty
  },
  {
    name: '屏東縣',
    checked: false,
    regionId: RegionEnum.PingtungCounty
  },
  {
    name: '宜蘭縣',
    checked: false,
    regionId: RegionEnum.YilanCounty
  },
  {
    name: '花蓮縣',
    checked: false,
    regionId: RegionEnum.HualienCounty
  },
  {
    name: '台東縣',
    checked: false,
    regionId: RegionEnum.TaitungCounty
  },
  {
    name: '澎湖縣',
    checked: false,
    regionId: RegionEnum.PenghuCounty
  },
  {
    name: '金門縣',
    checked: false,
    regionId: RegionEnum.KinmenCounty
  },
  {
    name: '連江縣',
    checked: false,
    regionId: RegionEnum.LienchiangCounty
  }
];

/**
 * 訂單用途種類與SVG圖示對應
 */
export const OrderUsage: OrderSelection[] = [
  {
    key: 'living',
    name: '住宅',
    icon: '/vectors/order/usage/living.svg'
  },
  {
    key: 'office',
    name: '辦公',
    icon: '/vectors/order/usage/office.svg'
  },
  {
    key: 'store',
    name: '店面',
    icon: '/vectors/order/usage/store.svg'
  },
  {
    key: 'hotel',
    name: '旅宿',
    icon: '/vectors/order/usage/hotel.svg'
  },
  {
    key: 'otherUsage',
    name: '其他',
    icon: '/vectors/order/other.svg'
  }
];

/**
 * 訂單風格種類與SVG圖示對應
 */
export const OrderStyle: OrderSelection[] = [
  {
    key: 'modern',
    name: '現代',
    icon: '/vectors/order/style/modern.svg'
  },
  {
    key: 'classical',
    name: '古典',
    icon: '/vectors/order/style/classical.svg'
  },
  {
    key: 'minimalist',
    name: '極簡',
    icon: '/vectors/order/style/minimalist.svg'
  },
  {
    key: 'luxury',
    name: '奢華',
    icon: '/vectors/order/style/luxury.svg'
  },
  {
    key: 'internetBeauty',
    name: '網美',
    icon: '/vectors/order/style/internetBeauty.svg'
  },
  {
    key: 'otherStyle',
    name: '其他',
    icon: '/vectors/order/other.svg'

  }
];

/**
 * 訂單主題種類與SVG圖示對應
 */
export const OrderTheme: OrderSelection[] = [
  {
    key: 'japanese',
    name: '日式',
    icon: '/vectors/order/theme/japanese.svg'
  },
  {
    key: 'chinese',
    name: '中式',
    icon: '/vectors/order/theme/chinese.svg'
  },
  {
    key: 'american',
    name: '美式',
    icon: '/vectors/order/theme/american.svg'
  },
  {
    key: 'european',
    name: '歐式',
    icon: '/vectors/order/theme/european.svg'
  },
  {
    key: 'otherTheme',
    name: '其他',
    icon: '/vectors/order/other.svg'

  }
];
export const UploadImageVideoType = 'image/png,image/bmp,image/jpeg,image/gif,video/mp4';
export const UploadImageType = 'image/png,image/bmp,image/jpeg,image/gif';
export const UploadVideoType = 'video/mp4';

export const OneDayMilliseconds = 86400000;

export const IsProduction = import.meta.env.MODE === 'production';

export const IsMobile = /Mobi|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
