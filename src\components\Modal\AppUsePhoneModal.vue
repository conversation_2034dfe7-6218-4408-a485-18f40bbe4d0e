<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import { ref } from 'vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import DesignerAppDownload from '@/components/General/DesignerAppDownload.vue';

defineProps<{ userType: UserTypeEnum }>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="免費語音" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">如果您需要免費語音功能</p>
        <p class="font-bold md:text-lg">請至App端使用</p>
      </div>
      <CustomerAppDownload v-if="userType === UserTypeEnum.Customer" />
      <DesignerAppDownload v-else />
    </div>
  </DefaultModal>
</template>
