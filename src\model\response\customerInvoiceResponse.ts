import { BaseResponse } from '@/model/response/baseResponse.ts';
import { AddressItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { InvoiceOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';

export interface CustomerBasicInvoiceResponse extends BaseResponse {
  invoices: BasicInvoice[];
}

export interface BasicInvoice {
  invoiceId: string;
  createTime: string;
  refreshTime: string;
  status: InvoiceOrderStatusEnum;
  orderCreateTime: string;
  orderType: OrderTypeEnum;
  customerName: string;
  address: AddressItem;
  amount: number;
}

export interface CustomerInvoiceResponse extends BaseResponse {
  invoice: Invoice;
}

export interface Invoice {
  invoiceId: string;
  createTime: string;
  refreshTime: string;
  status: InvoiceOrderStatusEnum;
  orderCreateTime: string;
  orderType: OrderTypeEnum; //本次發票對應的訂單
  customerName: string;
  address: AddressItem;
  amount: number;
  orderId: string;
  designerId: string;
  designerName: string;
  designerAvatar: string;
  application: Application;
  issueResults: IssueResult[];
}

export interface IssueResult {
  createTime: string;
  amount: number;
  fileUrl: string;
}

export interface Application {
  taxId: string;
  title: string;
  note: string;
}
