import BaseApiService from './baseApiService.ts';
import {
  EmailCheckRequest,
  EmailVerifyRequest,
  GetInfoRequest,
  UpdateAvatarRequest,
  UpdateCompanyRequest,
  UpdateTaiwanIdRequest, updateWebsiteRequest,
  UpdateWorkRequest
} from '@/model/request/accountRequest.ts';
import { GetInfoResponse } from '@/model/response/accountResponse.ts';


export class DesignerAccountService {
  /**
   * @description: 取得個人資料
   */
  static async getInfo(requestData: GetInfoRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/GetInfo'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 更新頭像
   */
  static async updateAvatar(requestData: UpdateAvatarRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Update/Avatar'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 更新身分證
   */
  static async updateTaiwanId(requestData: UpdateTaiwanIdRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Update/TaiwanId'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 更新公司證明
   */
  static async updateCompany(requestData: UpdateCompanyRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Update/Company'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 更新工作類型
   */
  static async updateWork(requestData: UpdateWorkRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Update/Work'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 請求 Email 驗證碼
   */
  static async sendEmailCheckCode(requestData: EmailCheckRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Email/Check'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 驗證 Email
   */
  static async emailVerify(requestData: EmailVerifyRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Account/Designer/Email/Verify'), requestData);
    return response.data as GetInfoResponse;
  }

  /**
   * @description: 更新個人資料-個人網站
   */
  static async updateWebsite(requestData: updateWebsiteRequest): Promise<GetInfoResponse> {
    const response = await BaseApiService.Designer.post(('Account/Designer/Update/Website'), requestData);
    return response.data as GetInfoResponse;
  }
}
