<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { MeasureOrderDetailService, OrderDetailService } from '@/api/designerOrder.ts';
import { formatFullDateTimeWithDay, formatFullDateWithDay, formatTime } from '@/utils/timeFormat.ts';
import { useRoute, useRouter } from 'vue-router';
import HezDivider from '@/components/General/HezDivider.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const route = useRoute();
const router = useRouter();
const orderId = route.params.id as string;
const rejectModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const timeConflictModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const stepEnabled = ref<boolean>(true);
const stepStatusMessage = ref<string>('');

const measureDetail = ref<{
  orderId: string;
  customerName: string;
  address: string;
  measureTime: {
    index: number;
    measureTime: string;
    checkTime: string[];
  }[];
}>({
  orderId: '',
  customerName: '',
  address: '',
  measureTime: []
});

const selectedTimeIndex = ref<number>(0);
const getMeasureOrderData = async () => {
  try {
    const detailData = (await OrderDetailService.getMeasureUnaccepted({ orderId: orderId })).result;
    if (detailData) {
      measureDetail.value = {
        orderId: detailData.orderId,
        customerName: detailData.customerName,
        address: detailData.address.fullName,
        measureTime: detailData.measureTime.map((time, index) => {
          const uniqueCheckTimes = Array.from(new Set(time.checkTime));
          return {
            index: index,
            measureTime: time.measureTime,
            checkTime: uniqueCheckTimes.map((checkTime) => formatTime(checkTime))
          };
        })
      };
    }
  } catch (error) {
    console.log(error);
  }
};

const rejectOrder = async () => {
  const res = await MeasureOrderDetailService.RejectOrder({ orderId: orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    //回訂單紀錄
    await router.push({
      name: 'designerOrderList'
    });
  }
};

const processAcceptOrder = async () => {
  if (measureDetail.value.measureTime[selectedTimeIndex.value].checkTime.length > 0) {
    timeConflictModalRef.value?.openModal();
    return;
  }
  await acceptOrder();
};

const acceptOrder = async () => {
  const res = await MeasureOrderDetailService.AcceptOrder({
    orderId: orderId,
    measureTimeIndex: selectedTimeIndex.value
  });
  if (res.status === APIStatusCodeEnum.Success) {
    await router.push({
      name: 'MeasureAcceptedOrderDetail',
      params: { id: orderId }
    });
  } else if (res.status === APIStatusCodeEnum.StepFeatureClosed) {
    stepEnabled.value = false;
    stepStatusMessage.value = '敬請期待功能開放';
  } else if (res.status === APIStatusCodeEnum.DesignerOrderDisabled) {
    stepEnabled.value = false;
    stepStatusMessage.value = '接單權限已關閉';
  }
};

const goBack = () => {
  router.back();
};

onMounted(async () => {
  await getMeasureOrderData();
});
</script>

<template>
  <div v-if="!stepEnabled" class="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
    <div class="flex flex-col items-center rounded-2xl bg-white px-10 py-8 shadow-2xl">
      <span class="mb-4 text-2xl font-bold">{{ stepStatusMessage }}</span>
      <button class="cus-btn w-full" @click="goBack()">確認</button>
    </div>
  </div>

  <div class="my-8 flex w-full flex-col gap-y-8">
    <div class="cus-border text-lg">
      <h2 class="text-center text-2xl font-bold">空間丈量</h2>
      <div class="flex flex-col gap-y-1 p-4">
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">支付方式</p>
          <p class="font-bold">到府收取現金</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">總金額</p>
          <p class="font-bold">NT$ 2,000</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">屋主姓名</p>
          <p class="font-bold">{{ measureDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">丈量地址</p>
          <p class="font-bold">{{ measureDetail.address }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">預約時間</p>
        </div>
        <div v-for="time in measureDetail.measureTime" :key="time.index" class="flex w-full flex-col space-y-4">
          <label :for="time.index.toString()" class="cus-border flex cursor-pointer items-center gap-2 px-4 py-2">
            <input
              :id="time.index.toString()"
              name="time"
              type="radio"
              :value="time.index"
              v-model="selectedTimeIndex"
              class="radio-btn h-4 w-4"
            />
            <span class="text-nowrap text-lg">{{ formatFullDateTimeWithDay(time.measureTime) }}</span>
          </label>
        </div>
      </div>
      <HezDivider />
      <div class="flex flex-col justify-start gap-y-1 p-4">
        <p class="text-nowrap font-bold">您的相關行程安排如下</p>
        <div v-for="time in measureDetail.measureTime" :key="time.index" class="flex w-full justify-start gap-2">
          <template v-if="time.checkTime.length > 0">
            <p class="w-full text-red-600">
              <span class="text-nowrap">{{ formatFullDateWithDay(time.measureTime) }}已排行程：</span>
              <span v-for="(checkTime, index) in time.checkTime" :key="index" class="mr-1 break-words">
                {{ checkTime }}</span
              >
            </p>
          </template>
          <template v-else>
            <p class="text-blue-600">{{ formatFullDateWithDay(time.measureTime) }}暫無行程</p>
          </template>
        </div>
      </div>
      <HezDivider />
      <!-- 按鈕 -->
      <div class="flex justify-evenly gap-2 max-md:flex-col">
        <button
          class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
          @click="rejectModalRef?.openModal"
        >
          無法接單
        </button>
        <button
          class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
          @click="processAcceptOrder()"
        >
          我要接單
        </button>
      </div>
    </div>
  </div>

  <DefaultModal
    title="無法接單"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="rejectModalRef"
    @closeModal="rejectModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">當您選擇拒絕這筆訂單後，</p>
        <p class="font-bold md:text-lg">該訂單將不再於您的訂單列表中顯示。</p>
        <button
          type="button"
          class="button-basic mt-2 w-full bg-red-300 ring-1 ring-inset ring-gray-300 hover:bg-red-400"
          @click="rejectOrder()"
        >
          確定
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal
    title="訂單時間衝突提醒"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="timeConflictModalRef"
    @closeModal="timeConflictModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">預約時間當天已有安排丈量行程，</p>
        <p class="font-bold md:text-lg">請確定時間不會衝突。</p>
        <button
          type="button"
          class="button-basic mt-2 w-full bg-gray-200 ring-1 ring-inset ring-gray-300 hover:bg-gray-300"
          @click="acceptOrder()"
        >
          確定
        </button>
      </div>
    </div>
  </DefaultModal>
</template>

<style scoped></style>
