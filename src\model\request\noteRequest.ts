export interface GetListRequest {
  roomId: string;
  skip: number;
  limit: number;
}

export interface GetOneNoteRequest {
  roomId: string;
  noteId: string;
}

export interface AddNoteRequest {
  roomId: string;
  text: string;
  medias: string[];
}

export interface EditNoteRequest {
  roomId: string;
  noteId: string;
  text: string;
  medias: string[];
}

export interface DeleteNoteRequest {
  roomId: string;
  noteId: string;
}

export interface GetCommentListRequest {
  roomId: string;
  noteId: string;
  skip: number;
  limit: number;
}

export interface AddCommentRequest {
  roomId: string;
  noteId: string;
  text: string;
}

export interface DeleteCommentRequest {
  roomId: string;
  noteId: string;
  commentId: string;
}
