import type { BaseResponse } from '@/model/response/baseResponse';
import { OrderTypeEnum } from '@/model/enum/orderType';
import type { Budget } from '@/model/general/budget';
import {
  AcceptanceProcessStatusEnum,
  AmountStatusEnum,
  ConstructionOrderStatusEnum,
  DesignOrderStatusEnum,
  DesignOrderSubStatusEnum,
  MeasureOrderStatusEnum,
  SigningStatusEnum
} from '@/model/enum/orderStatus.ts';

export interface ManyUnacceptedOrderCardResponse extends BaseResponse {
  count: number;
  result: UnacceptedOrderCardData[];
}

export interface UnacceptedOrderCardData {
  createTime: string;
  refreshTime: string;
  orderType: OrderTypeEnum;
  orderId: string;
  customerName: string;
  address: string;
  measureUnaccepted: MeasureUnacceptedCard;
  designUnaccepted: DesignUnacceptedCard;
  constructionUnaccepted: ConstructionUnacceptedCard;
}

export interface MeasureUnacceptedCard {
  measureTime: {
    index: number;
    measureTime: string;
    checkTime: string[];
  }[];
}

export interface DesignUnacceptedCard {
  areaPing: string; //要額外進行 jsonStringify 處理
  designBudget: Budget;
  constructionBudget: Budget;
  isAssignedDesigner: boolean; //顯示請盡快回覆報價
  quotedDesignersCount: number; //顯示 X位設計師已報價
}

export interface ConstructionUnacceptedCard {
  houseType: string; //要額外進行 jsonStringify 處理
  areaPing: string; //要額外進行 jsonStringify 處理
  constructionBudget: Budget;
  quotedDesignersCount: number; //顯示 X位設計師已報價
}

export interface ManyAcceptedOrderCardResponse extends BaseResponse {
  count: number;
  result: AcceptedOrderCardData[];
}

export interface AcceptedOrderCardData {
  createTime: string;
  refreshTime: string;
  orderType: OrderTypeEnum;
  orderId: string;
  customerName: string;
  address: string;
  measureAccepted: MeasureAcceptedCard;
  designAccepted: DesignAcceptedCard;
  constructionAccepted: ConstructionAcceptedCard;
}

export interface MeasureAcceptedCard {
  measureTime: string;
  status: MeasureOrderStatusEnum;
}

export interface DesignAcceptedCard {
  areaPing: string; //要額外進行 jsonStringify 處理
  designBudget: Budget; //設計預算
  constructionBudget: Budget; //裝潢預算
  isAssignedDesigner: boolean;
  quotedDesignersCount: number;
  designAmount: number;
  constructionEstimate: Budget; //裝潢估價
  status: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  signingStatus: SigningStatusEnum;
  isBankAccountSet: boolean;
}

export interface ConstructionAcceptedCard {
  houseType: string; //要額外進行 jsonStringify 處理
  areaPing: string; //要額外進行 jsonStringify 處理
  constructionBudget: Budget;
  quotedDesignersCount: number; //已報價設計師的數量
  constructionEstimate: Budget; //裝潢估價
  isCustomerAccepted: boolean; //客戶是否已接受初始報價
  amountStatus: AmountStatusEnum; //詳細報價狀態
  constructionAmount: number; //設計師最終裝修報價
  constructionDiscountAmount: number; //設計師最終裝修報價(優惠後)
  status: ConstructionOrderStatusEnum;
  acceptanceProcessStatus: AcceptanceProcessStatusEnum;
  signingStatus: SigningStatusEnum;
  isBankAccountSet: boolean;
}

export interface ManyQuotingOrderCardResponse extends BaseResponse {
  count: number;
  result: QuotingOrderCardData[];
}

export interface QuotingOrderCardData {
  createTime: string;
  refreshTime: string;
  orderType: OrderTypeEnum;
  orderId: string;
  customerName: string;
  address: string;
  designQuoting: DesignQuotingCard;
  constructionQuoting: ConstructionQuotingCard;
}

export interface DesignQuotingCard {
  areaPing: string;
  designBudget: Budget;
  constructionBudget: Budget;
  isAssignedDesigner: boolean;
  quotedDesignersCount: number;
  designAmount: number;
  constructionEstimate: Budget;
}

export interface ConstructionQuotingCard {
  houseType: string;
  areaPing: string;
  constructionBudget: Budget;
  quotedDesignersCount: number;
  constructionEstimate: Budget;
  isCustomerAccepted: boolean;
  amountStatus: AmountStatusEnum;
  constructionAmount: number;
  constructionDiscountAmount: number;
}

export interface ManyHistoricalOrderCardResponse extends BaseResponse {
  count: number;
  result: HistoricalOrderCardData[];
}

export interface HistoricalOrderCardData {
  createTime: string;
  refreshTime: string;
  orderType: OrderTypeEnum;
  orderId: string;
  customerName: string;
  address: string;
  measureHistorical: MeasureHistoricalCard;
  designHistorical: DesignHistoricalCard;
  constructionHistorical: ConstructionHistoricalCard;
}

export interface MeasureHistoricalCard {
  measureTime: string;
  status: MeasureOrderStatusEnum;
  isDeleted: boolean;
}

export interface DesignHistoricalCard {
  areaPing: string;
  designBudget: Budget;
  constructionBudget: Budget;
  isAssignedDesigner: boolean;
  quotedDesignersCount: number;
  designAmount: number;
  constructionEstimate: Budget;
  status: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  signingStatus: SigningStatusEnum;
  isBankAccountSet: boolean;
  isDeleted: boolean;
}

export interface ConstructionHistoricalCard {
  houseType: string;
  areaPing: string;
  constructionBudget: Budget;
  quotedDesignersCount: number;
  constructionEstimate: Budget;
  isCustomerAccepted: boolean;
  amountStatus: AmountStatusEnum;
  constructionAmount: number;
  constructionDiscountAmount: number;
  status: ConstructionOrderStatusEnum;
  acceptanceProcessStatus: AcceptanceProcessStatusEnum;
  signingStatus: SigningStatusEnum;
  isBankAccountSet: boolean;
  isDeleted: boolean;
}
