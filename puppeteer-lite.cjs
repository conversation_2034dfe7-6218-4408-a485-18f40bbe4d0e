const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

const routes = [
  '/',
  '/customer/appdownload',
  '/designer/appdownload',
  '/customer/designcompany',
  '/customer/bigdata',
  '/customer/terms',
  '/customer/register',
  '/customer/simpleregister',
  '/customer/askservice',
  '/customer/measure/publish',
  '/designer/home',
  '/designer/register',
  '/designer/appdownload',
  '/designer/terms'
];

(async () => {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });
  for (const route of routes) {
    const url = `http://localhost:3030${route}`;
    let savePath;
    if (route === '/') {
      savePath = path.join('dist', 'index.html');
    } else {
      const segments = route.split('/').filter(Boolean);
      savePath = path.join('dist', ...segments) + '.html';
    }
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle2' });
    const html = await page.content();
    fs.mkdirSync(path.dirname(savePath), { recursive: true });
    fs.writeFileSync(savePath, html, 'utf8');
    console.log(`✅ 已儲存 ${savePath}`);
    await page.close();
  }
  await browser.close();
  console.log('全部頁面快照完畢！');
})();