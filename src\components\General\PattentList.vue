<script setup lang="ts">

</script>

<template>
  <div class="w-full flex items-center max-lg:flex-col gap-x-2 shadow-md p-2 rounded-lg">
    <img src="/image/patent.jpg" alt="" class="max-h-[500px]">
    <div class="flex flex-col text-xs">
      <p class="font-bold text-xl">台灣和美國發明專利</p>
      <span>1) Augmented reality interactive system and dynamic information interactive display method thereof,
            US20170053444A1 </span>
      <span>2) Image restoration method and image processing apparatus using the same, US9478016 B2</span>
      <span>3) Method for improving image quality, US9396526 B2</span>
      <span>4) Method and system for image haze removal based on hybrid dark channel prior, US9361670 B2</span>
      <span>5) Motion detection method based on grey relational analysis, US9355460 B1</span>
      <span>6) Method and apparatus for moving object detection using principal component analysis based radial
            basis function network, US9349193 B2</span>
      <span>7) Method and apparatus for moving object detection using fisher's linear discriminant based radial
            basis function network, US9286690 B2</span>
      <span>8) Method and image processing apparatus for image visibility restoration using fisher's linear
            discriminant based dual dark channel prior, US9305242 B2</span>
      <span>9) Method and system for vehicle identification, US9208172 B2</span>
      <span>10) Image processing method and image processing apparatus using the same, US9202116 B2</span>
      <span>11) Face annotation method and a face annotation system, US9195912 B1</span>
      <span>12) Face annotation method and face annotation system, US9183464 B1</span>
      <span>13) Method and image processing apparatus for image visibility restoration, US9177363 B1</span>
      <span>14) Probabilistic neural network based moving object detection method and an apparatus using the same,
            US9159137 B2</span>
      <span>15) Probabilistic neural network based moving object detection method and an apparatus using the same,
            US20150104062A1</span>
      <span>16) Method and apparatus for moving object detection based on cerebellar model articulation controller
            network, US9123133 B1</span>
      <span>17) Method for solving carpool matching problem and carpool server using the same, US9074904 B1</span>
      <span>18) High-performance block-matching VLSI architecture with low memory bandwidth for power-efficient
            multimedia devices, US8787461 B2</span>
      <span>19) Video decoding device, US8737471 B2</span>
      <span>20) Image processing apparatus, US20130287299A1 </span>
      <span>21) Image restoration method and image processing apparatus using the same, TW201612851A</span>
      <span>22) Method and image processing apparatus for image visibility restoration, TWI509567B</span>
      <span>23)
            Method and system for image haze removal based on hybrid dark channel prior, TWI514323B</span>
      <span>24)
            Face annotation method and face annotation system, TWI508002B</span>
      <span>25) Method and image processing
            apparatus for image visibility restoration using fisher's linear discriminant based dual dark channel prior,
            TWI501194B</span>
      <span>26) Method and apparatus for moving object detection based on cerebellar model
            articulation controller network, TW201537516A</span>
      <span>27) Method and apparatus for moving object
            detection, TWI512685B</span>
      <span>28) Method of improving image quality for display device,
            TW201308997A</span>
      <span>29) Method and apparatus for moving object detection based on cerebellar model
            articulation controller network, TW201537516A</span>
      <span>30) Motion detection method for complex scenes,
            TW201308254A</span>
    </div>
  </div>
</template>
