import BaseApiService from './baseApiService.ts';
import { AddressToAddressInfoRequest } from '@/model/request/addressRequest.ts';
import { AddressToAddressInfoResponse } from '@/model/response/addressResponse.ts';

export class AddressService {
  /**
   * @description: 地址轉換完整地址資訊
   */
  static async AddressToAddressInfo(requestData: AddressToAddressInfoRequest): Promise<AddressToAddressInfoResponse> {
    const response = await BaseApiService.Customer.post(('/Map/Autocomplete/AddressToAddressInfo'), requestData);
    return response.data as AddressToAddressInfoResponse;
  }
}
