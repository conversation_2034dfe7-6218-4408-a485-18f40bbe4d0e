import { BaseResponse } from '@/model/response/baseResponse.ts';
import {
  DesignOrderStatusEnum,
  DesignOrderSubStatusEnum,
  RemittanceStatusEnum,
  SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import {
  AddressItem, ConstructionAmountItem,
  DesignCustomerPublishInfo
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { Budget } from '@/model/general/budget.ts';
import {
  CustomerImagePdfCadContent,
  CustomerImageVideoContent,
  CustomerMeasureContent
} from '@/model/response/customerMeasureOrderResponse.ts';

export interface CustomerGetOneDesignOrderDetailResponse extends BaseResponse {
  result: CustomerOrderDesignItem;
}

export interface CustomerGetManyDesignOrderDetailResponse extends BaseResponse {
  result: CustomerBasicOrderDesignItem[];
}

export interface CustomerBasicOrderDesignItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  contract: {
    signingStatus: SigningStatusEnum;
    remittanceStatus: RemittanceStatusEnum;
    amount: number;
    discountAmount: number;
  };
  isDeleted: boolean;
  designerName: string;
  designContent: {
    design2D: DesignBasicUploadItem;
    design3D: DesignBasicUploadItem;
  };
  constructionFee: DesignBasicUploadItem;
  quotationCount: number;
  isNextStepUsed: boolean;
  isAssignDesigner: boolean;
  areaPing: number;
}

export interface CustomerOrderDesignItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  contract: {
    signingStatus: SigningStatusEnum;
    remittanceStatus: RemittanceStatusEnum;
    amount: number;
    discountAmount: number;
    remittedAmount: number;
  };
  isDeleted: boolean;
  designerId: string;
  designerName: string;
  designerAvatar: string;
  designerPhone: string;
  customerId: string;
  customerName: string;
  address: AddressItem;
  publishInfo: DesignCustomerPublishInfo;
  measureContent: CustomerMeasureContent;
  designContent: {
    design2D: CustomerImagePdfCadContent;
    design3D: CustomerImageVideoContent;
  };
  constructionFee: CustomerConstructionFee;
  quotationCount: number;
  isNextStepUsed: boolean;
  chatRoomId: string;
  orderRatingId: string;
}

export interface DesignBasicUploadItem {
  updateTime: string;
  updateCount: number;
}

export interface CustomerConstructionFee {
  updateTime: string;
  updateCount: number;
  constructionBudget: Budget; //客戶的預算
  constructionEstimate: Budget; //設計師估價
  bigDataEstimate: Budget;
  constructionAmountDocs: ConstructionAmountItem[];
}
