import type { BaseResponse } from '@/model/response/baseResponse';
import type { MediaItem } from '@/model/general/media.ts';
import { RatingTypeEnum } from '@/model/enum/ratingType.ts';
import { RegionEnum } from '@/model/enum/taiwanRegion.ts';

export interface GetDesignerPortfolioResponse extends BaseResponse {
  count: number;
  portfolios: PortfolioPostItem[];
}

export interface GetDesignCompanyListResponse extends BaseResponse {
  designers: DesignCompanyItem[];
}

export interface DesignCompanyItem {
  refreshTime: string;
  designerId: string;
  designerName: string;
  designerAvatar: string;
  companyName: string;
  companyUnifiedBusinessNumber: string;
  companyDescription: string;
  companyLogo: string;
  companyServiceTime: string;
  companyAddress: string;
  designerWebSite: string;
  workType: {
    surveyor: boolean;
    designer: boolean;
    decorator: boolean;
  };
  workRegion: RegionEnum[];
  portfolioCount: number;
}

export interface GetSingleDesignerPageResponse extends BaseResponse {
  introduction: Introduction;
  portfolios: PortfolioItem[];
}

export interface GetDesignerRateResponse extends BaseResponse {
  result: RatingResult[];
}

export interface SetPortfolioLikeResponse extends BaseResponse {

}

export interface SetPortfolioViewResponse extends BaseResponse {

}

export interface UploadPortfolioResponse extends BaseResponse {
  portfolio: PortfolioItem;
}

export interface DeletePortfolioResponse extends BaseResponse {

}

export interface Introduction {
  designerId: string;
  designerAvatar: string;
  designerName: string;
  // 滿意度，介於 0 ~ 1 之間
  satisfaction: number;
  portfolioCount: number;
  hasConstructionTeam: boolean;
  region: RegionEnum[];
  companyName: string;
  companyUnifiedBusinessNumber: string;
  companyDescription: string;
  companyLogo: string;
  companyServiceTime: string;
  companyAddress: string;
  webSite: string;
}

export interface PortfolioItem {
  usage: string;
  style: string;
  theme: string;
  media: MediaItem[];
  description: string;
  createTime: string;
  refreshTime: string;
  portfolioId: string;
  likes: number;
  isLiked: boolean;
  views: number;
}

export interface PortfolioPostItem {
  usage: string;
  style: string;
  theme: string;
  media: MediaItem[];
  description: string;
  createTime: string;
  refreshTime: string;
  portfolioId: string;
  designerId: string;
  designerName: string;
  designerAvatar: string;
  designerCompanyName: string;
  designerCompanyDescription: string;
  designerCompanyLogo: string;
  designerCompanyServiceTime: string;
  designerCompanyAddress: string;
  designerWebSite: string;
  likes: number;
  isLiked: boolean;
  views: number;
}

export interface RatingResult {
  customerId: string;
  customerName: string;
  customerAvatar: string;
  ratingType: RatingTypeEnum;
  orderId: string;
  address: string;
  areaPing: string;
  houseType: string;
  amount: number;
  startTime: string;
  endTime: string;
  comment: string;
  measureRating: measureRating;
  designRating: designRating;
  constructionRating: constructionRating;
}

export interface measureRating {
  arrivalOnTime: number;
  serviceAttitude: number;
  professionalQuality: number;
  completionTime: number;
}

export interface designRating {
  workQuality: number;
  enthusiasticAttitude: number;
  designEfficiency: number;
}

export interface constructionRating {
  constructionQuality: number;
  serviceAttitude: number;
  completionTime: number;
}
