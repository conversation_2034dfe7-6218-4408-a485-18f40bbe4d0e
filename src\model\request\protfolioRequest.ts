import { MediaItem } from '@/model/general/media.ts';

export interface GetDesignerPortfolioRequest {
  usage: string[];
  style: string[];
  theme: string[];
}

export interface GetDesignCompanyListRequest {
}

export interface GetSingleDesignerPageRequest {
  designerId: string;
}

export interface GetDesignerRateRequest {
  designerId: string;
}

export interface SetPortfolioLikeRequest {
  portfolioId: string;
  isLike: boolean;
}

export interface SetPortfolioViewRequest {
  portfolioId: string;
}

export interface PublishPortfolioRequest {
  description: string;
  usage: string;
  style: string;
  theme: string;
  media: MediaItem[];
}

export interface EditPortfolioRequest {
  portfolioId: string;
  description: string;
  usage: string;
  style: string;
  theme: string;
  media: MediaItem[];
}

export interface DeletePortfolioRequest {
  portfolioId: string;
}
