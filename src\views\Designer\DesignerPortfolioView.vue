<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import { portfolioDataFormat } from '@/utils/portfolioFormat.ts';
import { PortfolioService } from '@/api/portfolio.ts';
import { PortfolioItem } from '@/model/response/portfolioResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { IntroductionFormated } from '@/model/formatted/portolio.ts';
import SatisfactionModal from '@/components/Portfolio/SatisfactionModal.vue';
import UploadPortfolioModal from '@/components/Portfolio/UploadPortfolioModal.vue';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { DesignerAccountService } from '@/api/designerAccount.ts';
import { toastInfo, toastSuccess } from '@/utils/toastification.ts';
import { useDesignerInfoStore, useDesignerVerifyStore } from '@/stores/designerGlobal.ts';
import { IsProduction } from '@/utils/hezParameters.ts';

const designerInfoStore = useDesignerInfoStore();
const route = useRoute();
const router = useRouter();

const isUserFound = ref<boolean>(true);
const modalSatisfactionRef = ref<InstanceType<typeof SatisfactionModal> | null>(null);
const uploadPortfolioModalRef = ref<InstanceType<typeof UploadPortfolioModal> | null>();
const shareLinkModalRef = ref<InstanceType<typeof DefaultModal> | null>();
const portfolioData = ref<{
  introduction: IntroductionFormated,
  portfolios: PortfolioItem[],
}>();
const designerId = ref<string>(route.params.designerId as string);
const isAPILoading = ref(false);
const shareLinkInput = ref<string>('');
const verifyStore = useDesignerVerifyStore();
const goToVerifyModalRef = ref<InstanceType<typeof DefaultModal> | null>();
const VerifyModalTitle = ref('尚未完成身分驗證');
const reFreshSwiper = ref<boolean>(false);

const openGoToVerifyModal = () => {
  if (!isMyPortfolio.value) return;
  if (!verifyStore.taiwanIdVerify) {
    VerifyModalTitle.value = '尚未完成身分驗證';
  } else if (!verifyStore.companyVerify) {
    VerifyModalTitle.value = '尚未完成公司驗證';
  } else {
    VerifyModalTitle.value = '尚未完成email驗證';
  }
  goToVerifyModalRef.value?.openModal();
};

const openSatisfactionModal = () => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  modalSatisfactionRef.value?.openSatisfactionModal();
};

const openPublishModal = () => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  uploadPortfolioModalRef.value?.openModal();
};

const openEditPortfolioModal = (portfolio: PortfolioItem) => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  uploadPortfolioModalRef.value?.openModal(portfolio);
};

const openShareLinkModal = () => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  shareLinkModalRef.value?.openModal();
  shareLinkInput.value = portfolioData.value?.introduction.website || '';
};

const closeShareLinkModal = () => {
  shareLinkModalRef.value?.closeModal();
  shareLinkInput.value = portfolioData.value?.introduction.website || '';
};

const getPortfolioData = async () => {
  designerId.value = route.params.designerId as string;
  const responseData = await PortfolioService.getSingleDesignerPageByDesigner({ designerId: designerId.value });
  if (responseData.status === APIStatusCodeEnum.DataOrFormatError || responseData.status === APIStatusCodeEnum.UserNotFound) {
    isUserFound.value = false;
    return;
  } else {
    isUserFound.value = true;
  }
  portfolioData.value = portfolioDataFormat(responseData); //TODO 轉換器 糞Code
};

const deletePortfolio = async (portfolioId: string) => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  if (isAPILoading.value === true) return;
  isAPILoading.value = true;
  const result = await PortfolioService.deletePortfolio({ portfolioId: portfolioId });
  if (result.status === APIStatusCodeEnum.Success) {
    if (portfolioData.value?.portfolios) {
      // 根據portfolioId 去找出portfolioData.value?.portfolios 的index 然後刪除該項目的Object
      const index = portfolioData.value.portfolios.findIndex(portfolio => portfolio.portfolioId === portfolioId);
      if (index !== -1) {
        portfolioData.value.portfolios.splice(index, 1);
        portfolioData.value.introduction.portfolioCount -= 1;
      }
    }
  }
  isAPILoading.value = false;
};

const uploadPortfolioResult = async (result: PortfolioItem, isEdit: boolean) => {
  if (!portfolioData.value) return;
  if (isEdit) {
    reFreshSwiper.value = true;
    const index = portfolioData.value.portfolios.findIndex(p => p.portfolioId === result.portfolioId);
    await nextTick();
    reFreshSwiper.value = false;
    if (index !== -1) {
      portfolioData.value.portfolios[index] = result;
    }
  } else {
    portfolioData.value.introduction.portfolioCount++;
    portfolioData.value.portfolios.unshift(result);
  }
};

const updateLink = async (linkName: string) => {
  const regex = /^[\w.\-:@\u4e00-\u9fa5()]*$/;
  if (!regex.test(linkName)) {
    toastInfo('作品主頁連結包含特殊字元，請重新輸入');
    return;
  }
  const res = await DesignerAccountService.updateWebsite({ websiteUrl: linkName });
  if (res.status === APIStatusCodeEnum.Success) {
    if (!portfolioData.value) return;
    portfolioData.value.introduction.website = res.result.website.targetUrl;
    if (res.result.website.targetUrl === '') {
      portfolioData.value.introduction.website = designerInfoStore.userId;
    }
    toastSuccess('作品主頁連結更新成功');
  }
};

const goToProfile = () => {
  router.push({ name: 'designerprofile' });
};

const isMyPortfolio = computed(() => {
  return designerInfoStore.userId === designerId.value;
});

onMounted(async () => {
  await getPortfolioData();
});
watch(() => route.params, async () => {
  await getPortfolioData();
});
</script>

<template>
  <div class="w-full">
    <div v-if="!isUserFound" class="max-md:p-4 md:my-12 text-center cus-border">
      <p class="text-lg font-bold">查無設計師</p>
    </div>

    <div v-else class="flex flex-col space-y-12 max-lg:p-4 max-md:p-4 md:my-12 md:text-lg">
      <div class="cus-border flex flex-col md:gap-y-8">
        <div class="flex justify-between gap-x-2 max-md:flex-col md:divide-x max-md:divide-y divide-black">
          <div class="flex flex-col justify-center items-center md:text-lg md:p-4 max-md:py-4 gap-4">
            <div class="h-20 w-20 md:h-40 md:w-40">
              <img v-if="portfolioData?.introduction.avatarUrl" :src="portfolioData?.introduction.avatarUrl" alt=""
                   class="w-full h-full rounded-full">
              <UserCircleIcon v-else class="w-full h-full rounded-full" />
            </div>
            <p>設計師</p>
            <p>{{ portfolioData?.introduction.designerName }}</p>
            <div class="flex items-center">
              <p class="mr-4">通過公司認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
            <div class="flex items-center">
              <p class="mr-4">通過身份認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
          </div>

          <div class="md:w-4/5 flex flex-col justify-center md:text-lg md:p-4 max-md:py-4 gap-2">
            <div class="w-full flex gap-x-2 items-center">
              <p class="flex-shrink-0">滿意度</p>
              <p class="flex-shrink-0">{{ portfolioData?.introduction.satisfaction }}</p>
              <button class="cus-btn rounded-lg md:px-4 px-2 py-1 text-base"
                      v-if="portfolioData?.introduction.satisfaction !=='尚無評價'"
                      @click="openSatisfactionModal()">
                <span class="">查看滿意度</span>
              </button>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">作品數量</p>
              <p class="break-all">{{ portfolioData?.introduction.portfolioCount }}件</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">公司名稱</p>
              <p class="break-all">{{ portfolioData?.introduction.companyName }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">統一編號</p>
              <p class="break-all">{{ portfolioData?.introduction.companyUnifiedBusinessNumber }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">服務時間</p>
              <p class="break-all">{{ portfolioData?.introduction.companyServiceTime }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">施工團隊</p>
              <p class="break-all">{{ portfolioData?.introduction.hasConstructionTeam }}</p>
            </div>
            <p>服務地區</p>
            <div class="w-full flex gap-2 items-center flex-wrap">
              <div v-for="city in portfolioData?.introduction.region" :key="city"
                   class=" p-2 border border-gray-400 rounded-xl text-nowrap">
                <p>{{ city }}</p>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isMyPortfolio"
             class="flex space-x-4 md:space-x-28 justify-between md:text-lg text-nowrap">
          <button
            class="cus-btn w-full"
            @click="openShareLinkModal()"><span>分享主頁</span>
          </button>
          <button
            class="cus-btn w-full"
            @click="openPublishModal()"><span>刊登作品</span>
          </button>
        </div>
      </div>

      <div v-if="portfolioData?.portfolios.length === 0 && isMyPortfolio"
           class="cus-border w-full flex flex-col justify-center text-center">
        <p>您尚未刊登任何作品集，趕緊刊登您的第一件作品吧!</p>
        <p>讓您的作品可以被大量客戶看見</p>
        <p>增加接單機會</p>
        <button class="cus-btn w-full md:text-lg text-nowrap"
                @click="openPublishModal()"><span>刊登作品</span>
        </button>
      </div>

      <div v-for="portfolio in portfolioData?.portfolios" :key="portfolio.portfolioId"
           class="cus-border w-full flex flex-col divide-y divide-black">
        <div class="flex flex-col gap-2">
          <div>
            <swiper-container class="fix-pagination" :pagination="true" space-between="30" v-if="!reFreshSwiper">
              <swiper-slide v-for="images in portfolio.media" :key="images">
                <img :src="images.url" alt="作品集" class="object-contain block mx-auto h-96 max-md:h-64">
              </swiper-slide>
            </swiper-container>
          </div>
          <div class="flex justify-center md:justify-between items-center">
            <div class="flex py-8 space-x-4 ">
              <div class="flex max-md:flex-col items-center gap-4">
                <p style="color: #56440E">用途</p>
                <p class="break-all">{{ portfolio.usage }}</p>
              </div>
              <div class="flex max-md:flex-col items-center gap-4">
                <p style="color: #56440E">風格</p>
                <p class="break-all">{{ portfolio.style }}</p>
              </div>
              <div class="flex max-md:flex-col items-center gap-4">
                <p style="color: #56440E">主題</p>
                <p class="break-all">{{ portfolio.theme }}</p>
              </div>
            </div>
            <Menu as="div" class="relative flex-none"
                  v-if="isMyPortfolio">
              <MenuButton class="block p-2.5 text-gray-500 hover:text-gray-900">
                <span class="sr-only">Open options</span>
                <EllipsisVerticalIcon class="h-8 w-8" aria-hidden="true" />
              </MenuButton>
              <transition enter-active-class="transition ease-out duration-100"
                          enter-from-class="transform opacity-0 scale-95"
                          enter-to-class="transform opacity-100 scale-100"
                          leave-active-class="transition ease-in duration-75"
                          leave-from-class="transform opacity-100 scale-100"
                          leave-to-class="transform opacity-0 scale-95">
                <MenuItems
                  class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                  <MenuItem v-slot="{ active }">
                    <a
                      :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-base leading-6 text-gray-900 cursor-pointer']"
                      @click="openEditPortfolioModal(portfolio)"
                    >修改作品</a>
                  </MenuItem>
                  <MenuItem v-slot="{ active }">
                    <a
                      :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-base leading-6 text-gray-900 cursor-pointer']"
                      @click="deletePortfolio(portfolio.portfolioId)"
                    >刪除作品</a>
                  </MenuItem>
                </MenuItems>
              </transition>
            </Menu>
          </div>
        </div>

        <div class="flex flex-col py-4 gap-2">
          <div class="mb-4">
            <p class="w-full break-all whitespace-pre-line">{{ portfolio.description }}</p>
          </div>
          <!--          <div v-if="isMyPortfolio"-->
          <!--               class="flex space-x-4 md:space-x-28 justify-between md:text-lg text-nowrap">-->
          <!--            <button class="cus-btn w-full"-->
          <!--                    @click="openEditPortfolioModal(portfolio)">修改作品-->
          <!--            </button>-->
          <!--            <button class="cus-btn w-full"-->
          <!--                    @click="openPublishModal()">刊登作品-->
          <!--            </button>-->
          <!--          </div>-->
        </div>
      </div>
    </div>

    <SatisfactionModal ref="modalSatisfactionRef" :designer-id="designerId" :is-designer="true" />
    <UploadPortfolioModal ref="uploadPortfolioModalRef" @upload-result="uploadPortfolioResult" />
  </div>

  <DefaultModal title="分享設計作品" :show-close-button="true" :click-outside-close="false" ref="shareLinkModalRef"
                @closeModal="closeShareLinkModal">
    <div class="flex flex-col gap-2 p-4 px-8 mx-auto">
      <p>您好 {{ portfolioData?.introduction.designerName }}</p>
      <p>您目前的作品主頁網址如下</p>
      <p v-if="IsProduction" class="font-bold break-words">www.homeeasy.app/<span>{{ portfolioData?.introduction.website }}</span></p>
      <p v-else class="font-bold break-words">www-dev.homeeasy.app/<span>{{ portfolioData?.introduction.website }}</span></p>
      <div class="flex max-md:flex-col gap-2 items-center">
        <!--        <label class="block text-black text-nowrap">目前連結名稱</label>-->
        <input
          type="text"
          placeholder="請輸入連結名稱"
          v-model="shareLinkInput"
          class="w-full px-4 py-2 cus-border focus-border-color"
        />
      </div>
      <button class="px-4 py-2 cus-btn"
              @click="updateLink(shareLinkInput)">
        更新連結名稱
      </button>
    </div>
  </DefaultModal>

  <DefaultModal :title="VerifyModalTitle" :click-outside-close="false" :showCloseButton="true"
                ref="goToVerifyModalRef" modal-width="max-w-md"
                @closeModal="goToVerifyModalRef?.closeModal()">
    <div class="flex flex-col gap-2 p-4 mx-auto">
      <p class="font-bold">尚未完成</p>
      <p v-if="!verifyStore.taiwanIdVerify" class="text-red-600 font-bold">身分驗證</p>
      <p v-if="!verifyStore.companyVerify" class="text-red-600 font-bold">公司驗證</p>
      <p v-if="!verifyStore.emailVerify" class="text-red-600 font-bold">email驗證</p>
      <button class="bg-color-button text-black px-4 py-2 rounded-md text-nowrap hover:bg-color-selected"
              @click="goToProfile()">
        前往驗證
      </button>
    </div>
  </DefaultModal>
</template>
