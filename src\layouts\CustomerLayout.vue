<script setup lang="ts">
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline';
import { useRoute, useRouter } from 'vue-router';
import { computed, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

const initNavigation = [
  { title: '設計公司', routeName: 'designcompany', current: false },
  { title: '訂單紀錄', routeName: 'customerorderlist', current: false },
  { title: '大數據估價', routeName: 'bigdata', current: false },
  { title: '使用者App', routeName: 'customerappdownload', current: false },
  { title: '前往設計師端', routeName: 'designerhome', current: false }

];
const route = useRoute();
const router = useRouter();

const updatedNavigation = computed(() => initNavigation.map((item) => {
  item.current = route.name === item.routeName;
  return item;
}));

const switchModal = ref<InstanceType<typeof DefaultModal> | null>(null);


const handleNavigationClick = async (item: { title: string, routeName: string, current: boolean }) => {
  if (item.routeName === 'designerhome') {
    switchModal.value?.openModal();
    return;
  }
  await router.push({ name: item.routeName });
};
</script>

<template>
  <div class="flex flex-col min-h-full text-ch">
    <Disclosure as="nav" class="bg-white md:shadow-md shadow-gray-300 max-md:border-b-2 sticky top-0 z-50"
                v-slot="{ open }">
      <div class="mx-auto max-w-7xl px-4 ">
        <div class="flex justify-between">
          <div class="flex gap-x-2 items-center">
            <div class="flex flex-shrink-0 items-center py-2">
              <router-link to="/">
                <img class="w-60 max-md:max-w-48 object-contain" src="/image/web_logo_Customer.png" alt="" />
              </router-link>
            </div>
            <div class="hidden lg:-my-px lg:ml-6 lg:flex lg:space-x-8">
              <a v-for="item in updatedNavigation" :key="item.title"
                 @click.prevent="handleNavigationClick(item)"
                 :class="[item.current ? 'border-secondary font-bold' :'border-transparent hover:border-secondary font-medium', 'whitespace-nowrap inline-flex items-center border-b-2 px-3 py-2 text-base text-color-primary cursor-pointer']"
                 :aria-current="item.current ? 'page' : undefined ">{{ item.title }}</a>
            </div>
          </div>
          <div class="-mr-2 flex lg:hidden">
            <!-- 行動端 -->
            <DisclosureButton
              class="inline-flex items-center justify-center p-2 text-black">
              <span class="sr-only">Open main menu</span>
              <Bars3Icon v-if="!open" class="block h-6 w-6" aria-hidden="true" />
              <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
            </DisclosureButton>
          </div>
        </div>
      </div>

      <DisclosurePanel class="lg:hidden">
        <!-- 行動端 -->
        <div class="space-y-1 px-2 pb-3 pt-2 sm:px-3">
          <DisclosureButton v-for="item in updatedNavigation" :key="item.title" as="a"
                            @click="handleNavigationClick(item)"
                            :class="[item.current ? 'bg-color-selected' :'hover:bg-color-selected hover:bg-opacity-75', 'block rounded-md px-3 py-2 text-color-secondary text-base font-medium']"
                            :aria-current="item.current ? 'page' : undefined ">{{ item.title }}
          </DisclosureButton>
        </div>
      </DisclosurePanel>
    </Disclosure>

    <main class="flex-grow">
      <div class="max-w-full">
        <div class="flex justify-center mx-auto">
          <div class="xl:max-w-[1248px] max-xl:mx-8 w-full flex flex-col items-center">
            <slot />
          </div>
        </div>
      </div>
    </main>
    <footer>
      <div class="flex items-center justify-center gap-8 px-4 py-4 sm:px-6 lg:px-8 bg-gray-100 max-w-full max-md:flex-col max-md:gap-4">
        <div class="flex flex-col gap-y-2 items-center">
          <p class="text-center text-sm">
            © 2024 HomeEasy. Created by MMSLab.
          </p>
          <div class="flex max-md:flex-col gap-2 justify-center">
            <p class="text-center text-sm">
              <span class="font-bold">聯絡Email：</span>
              <EMAIL>
            </p>
            <p class="text-center text-sm">
              <span class="font-bold">聯絡電話：</span>
              (02)2771-2171分機2232
            </p>
          </div>
        </div>
        <div class="flex md:flex-col flex-row items-center gap-2">
          <a href="https://play.google.com/store/apps/details?id=lab.homeeasy" target="_blank">
            <img src="/vectors/general/AndroidDownload.svg" alt="Android download" class="hover-zoom w-24" />
          </a>
          <a href="https://apps.apple.com/us/app/%E5%AE%B6%E6%98%93-homeeasy-%E8%A3%9D%E6%BD%A2%E6%96%BD%E5%B7%A5%E6%AF%94%E5%83%B9%E5%B9%B3%E5%8F%B0/id6477271187" target="_blank">
            <img src="/vectors/general/IOSDownload.svg" alt="IOS download" class="hover-zoom w-24" />
          </a>
        </div>
      </div>
    </footer>
  </div>

  <DefaultModal title="切換至設計師端" :click-outside-close="true" :show-close-button="true"
                @closeModal="switchModal?.closeModal()" modalWidth="max-w-md" ref="switchModal">
    <div class="flex flex-col space-y-4 m-4 text-black items-center">
      <p>您目前在客戶端</p>
      <p>請問您要切換至設計師端嗎?</p>
      <button type="button"
              class="cus-btn p-2 w-full"
              @click="router.push({name:'designerhome'})">確定切換
      </button>
    </div>
  </DefaultModal>
</template>
