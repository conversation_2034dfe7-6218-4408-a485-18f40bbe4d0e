<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { CameraIcon, BuildingOfficeIcon, UserIcon } from '@heroicons/vue/24/solid';
import { DesignerAccountService } from '@/api/designerAccount.ts';
import { ProfileGenderEnum, ProfileVerifyStatusEnum } from '@/model/enum/profileVerify.ts';
import { RegionEnum } from '@/model/enum/taiwanRegion.ts';
import { UploadImageType, workRegion } from '@/utils/hezParameters.ts';
import type { GetInfoResponse } from '@/model/response/accountResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { downloadFile } from '@/utils/fileDownloader.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { toastError, toastInfo, toastWarning } from '@/utils/toastification.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { useRouter } from 'vue-router';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { useDesignerInfoStore, useDesignerVerifyStore } from '@/stores/designerGlobal.ts';

const emailModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const companyModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const taiwanIdModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const serviceModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const avatarInput = ref<HTMLInputElement | null>(null);
const taiwanIdFrontInput = ref<HTMLInputElement | null>(null);
const taiwanIdBackInput = ref<HTMLInputElement | null>(null);
const companyLogoInput = ref<HTMLInputElement | null>(null);
const companyDocumentInput = ref<HTMLInputElement | null>(null);

const designerInfo = ref<{
  refreshTime: string;
  userId: string;
  phone: string;
  avatar: {
    updateTime: string;
    url: string;
  };
  taiwanId: {
    updateTime: string;
    verifyStatus: ProfileVerifyStatusEnum;
    username: string;
    gender: ProfileGenderEnum;
    idNumber: string;
    frontSideUrl: string;
    backSideUrl: string;
  };
  email: {
    updateTime: string;
    address: string;
    isVerify: boolean;
  };
  company: {
    updateTime: string;
    verifyStatus: ProfileVerifyStatusEnum;
    companyName: string;
    unifiedBusinessNumber: string;
    companyDocumentUrls: string[];
    description: string;
    logo: string;
    serviceTime: string;
    address: string;
  };
  verifyStatus: ProfileVerifyStatusEnum; // 這個要過才可以刊登作品
  work: {
    updateTime: string;
    type: {
      surveyor: boolean;
      designer: boolean;
      decorator: boolean;
    };
    region: {
      name: string;
      checked: boolean;
      regionId: RegionEnum;
    }[];
  };
}>({
  refreshTime: '',
  userId: '',
  phone: '',
  avatar: {
    updateTime: '',
    url: ''
  },
  taiwanId: {
    updateTime: '',
    verifyStatus: ProfileVerifyStatusEnum.Init,
    username: '',
    gender: ProfileGenderEnum.Unknown,
    idNumber: '',
    frontSideUrl: '',
    backSideUrl: ''
  },
  email: {
    updateTime: '',
    address: '',
    isVerify: false
  },
  company: {
    updateTime: '',
    verifyStatus: ProfileVerifyStatusEnum.Init,
    companyName: '',
    unifiedBusinessNumber: '',
    companyDocumentUrls: [] as string[],
    description: '',
    logo: '',
    serviceTime: '',
    address: ''
  },
  verifyStatus: ProfileVerifyStatusEnum.Init, // 這個要過才可以刊登作品
  work: {
    updateTime: '',
    type: {
      surveyor: false,
      designer: false,
      decorator: false
    },
    region: workRegion
  }
});

const tempWorkItem = ref<{
  work: {
    updateTime: string;
    type: {
      surveyor: boolean;
      designer: boolean;
      decorator: boolean;
    };
    region: {
      name: string;
      checked: boolean;
      regionId: RegionEnum;
    }[];
  };
}>({
  work: {
    updateTime: '',
    type: {
      surveyor: false,
      designer: false,
      decorator: false
    },
    region: workRegion
  }
});

const emailVerifyCode = ref('');
const emailVerifyCodeSent = ref(false);
const sentCodeCountDown = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const router = useRouter();
const useDesignerVerify = useDesignerVerifyStore();
const designerInfoStore = useDesignerInfoStore();

const gotoPortfolio = () => {
  if (designerInfo.value.verifyStatus === ProfileVerifyStatusEnum.Success) {
    router.push({ name: 'designerportfolio', params: { designerId: designerInfoStore.userId } });
  }
};

const startCountdown = () => {
  sentCodeCountDown.value = 30;
  countdownInterval.value = window.setInterval(() => {
    if (sentCodeCountDown.value !== null) {
      sentCodeCountDown.value -= 1;
      console.log(sentCodeCountDown.value);
      if (sentCodeCountDown.value <= 0) {
        clearInterval(countdownInterval.value!);
        sentCodeCountDown.value = null;
        emailVerifyCodeSent.value = false;
        console.log('clear');
      }
    }
  }, 1000);
};

const clearCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
    emailVerifyCodeSent.value = false;
  }
};

const openServiceModal = () => {
  tempWorkItem.value.work = JSON.parse(JSON.stringify(designerInfo.value.work));
  serviceModalRef.value?.openModal();
};

const closeServiceModal = () => {
  tempWorkItem.value.work = JSON.parse(JSON.stringify(designerInfo.value.work));
  serviceModalRef.value?.closeModal();
};

const openTaiwanIdModal = () => {
  taiwanIdModalRef.value?.openModal();
};

const closeTaiwanIdModal = async () => {
  taiwanIdModalRef.value?.closeModal();
  const res = await DesignerAccountService.getInfo({});
  updateDesignerInfo(res);
};

const openCompanyModal = () => {
  companyModalRef.value?.openModal();
};

const closeCompanyModal = async () => {
  companyModalRef.value?.closeModal();
  const res = await DesignerAccountService.getInfo({});
  updateDesignerInfo(res);
};

const openEmailModal = () => {
  emailModalRef.value?.openModal();
};

const closeEmailModal = async () => {
  emailModalRef.value?.closeModal();
  emailVerifyCode.value = '';
  const res = await DesignerAccountService.getInfo({});
  updateDesignerInfo(res);
};

const triggerFileInput = (type: string) => {
  if (type === 'avatar') {
    avatarInput.value?.click();
  } else if (type === 'twIdFront') {
    taiwanIdFrontInput.value?.click();
  } else if (type === 'twIdBack') {
    taiwanIdBackInput.value?.click();
  } else if (type === 'companyLogo') {
    companyLogoInput.value?.click();
  } else if (type === 'companyDocument') {
    companyDocumentInput.value?.click();
  }
};

const imageUpload = async (event: Event, type: string, uploadType: UploadFileEnum) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, uploadType)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);

    if (type === 'avatar') {
      await updateAvatar(S3Urls[0]);
    } else if (type === 'twIdFront') {
      await uploadTaiwanIdFrontSide(S3Urls[0]);
    } else if (type === 'twIdBack') {
      await uploadTaiwanIdBackSide(S3Urls[0]);
    } else if (type === 'companyLogo') {
      await uploadCompanyLogo(S3Urls[0]);
    } else if (type === 'companyDocument') {
      await uploadCompanyDocument(S3Urls);
    }
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const updateAvatar = async (url: string) => {
  const res = await DesignerAccountService.updateAvatar({
    avatarUrl: url
  });
  if (res.status === APIStatusCodeEnum.Success) {
    updateDesignerInfo(res);
  }
};

const updateWork = async () => {
  // 驗證 workType.designer 與 workType.decorator 至少有一個為 true
  if (!tempWorkItem.value.work.type.designer && !tempWorkItem.value.work.type.decorator) {
    toastWarning('至少選擇一個服務項目');
    return;
  }
  // 驗證 workRegion 至少有一個為 true
  if (tempWorkItem.value.work.region.filter((region) => region.checked).length === 0) {
    toastWarning('至少選擇一個服務地區');
    return;
  }

  const workType = tempWorkItem.value.work.type;
  const workRegion = tempWorkItem.value.work.region.filter((region) => region.checked).map((region) => region.regionId);
  const res = await DesignerAccountService.updateWork({
    workType: {
      surveyor: workType.surveyor,
      designer: workType.designer,
      decorator: workType.decorator
    },
    workRegion: workRegion
  });
  if (res.status === APIStatusCodeEnum.Success) {
    updateDesignerInfo(res);
    closeServiceModal();
    toastInfo('服務資訊更新成功');
  }
};

const uploadTaiwanIdFrontSide = async (url: string) => {
  designerInfo.value.taiwanId.frontSideUrl = url;
};

const uploadTaiwanIdBackSide = async (url: string) => {
  designerInfo.value.taiwanId.backSideUrl = url;
};

const updateTaiwanId = async () => {
  const res = await DesignerAccountService.updateTaiwanId({
    username: designerInfo.value.taiwanId.username,
    frontSideUrl: designerInfo.value.taiwanId.frontSideUrl,
    backSideUrl: designerInfo.value.taiwanId.backSideUrl
  });
  if (res.status === APIStatusCodeEnum.Success) {
    updateDesignerInfo(res);
    toastInfo('身分驗證提交成功');
  }
};

const uploadCompanyDocument = async (urls: string[]) => {
  // 將url 加入現有的 designerInfo.company.companyDocumentUrls 陣列
  designerInfo.value.company.companyDocumentUrls = designerInfo.value.company.companyDocumentUrls.concat(urls);
};

const uploadCompanyLogo = async (url: string) => {
  designerInfo.value.company.logo = url;
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

const pullCompanyDocument = async (index: number) => {
  designerInfo.value.company.companyDocumentUrls.splice(index, 1);
};

const updateCompany = async () => {
  const res = await DesignerAccountService.updateCompany({
    name: designerInfo.value.company.companyName,
    guiNumber: designerInfo.value.company.unifiedBusinessNumber,
    docUrls: designerInfo.value.company.companyDocumentUrls,
    description: designerInfo.value.company.description,
    logo: designerInfo.value.company.logo,
    serviceTime: designerInfo.value.company.serviceTime,
    address: designerInfo.value.company.address
  });
  if (res.status === APIStatusCodeEnum.Success) {
    updateDesignerInfo(res);
    toastInfo('公司驗證提交成功');
  } else {
    toastError('請完整填寫資訊');
  }
};

const sendEmailCheckCode = async () => {
  const res = await DesignerAccountService.sendEmailCheckCode({
    email: designerInfo.value.email.address
  });
  if (res.status === APIStatusCodeEnum.Success) {
    startCountdown();
    emailVerifyCodeSent.value = true;
  } else {
    toastError('Email格式錯誤');
  }
};

const verifyEmail = async () => {
  const res = await DesignerAccountService.emailVerify({
    verifyCode: emailVerifyCode.value
  });
  if (res.status === APIStatusCodeEnum.Success) {
    updateDesignerInfo(res);
    emailVerifyCode.value = '';
    clearCountdown();
  } else {
    toastError('驗證碼錯誤');
  }
};

const updateDesignerInfo = (res: GetInfoResponse) => {
  designerInfo.value.refreshTime = res.result.refreshTime;
  designerInfo.value.userId = res.result.userId;
  designerInfo.value.phone = res.result.phone;
  designerInfo.value.avatar = res.result.avatar;
  designerInfo.value.taiwanId = res.result.taiwanId;
  designerInfo.value.email = res.result.email;
  designerInfo.value.company = res.result.company;
  designerInfo.value.verifyStatus = res.result.verifyStatus;
  designerInfo.value.work.region = workRegion.map((region) => {
    return {
      name: region.name,
      checked: res.result.work.region.includes(region.regionId),
      regionId: region.regionId
    };
  });
  designerInfo.value.work.type = res.result.work.type;
  designerInfo.value.work.updateTime = res.result.work.updateTime;
  updateDesignerVerifyStore();
};

const updateDesignerVerifyStore = () => {
  useDesignerVerify.setTaiwanIdVerify(designerInfo.value.taiwanId.verifyStatus);
  useDesignerVerify.setCompanyVerify(designerInfo.value.company.verifyStatus);
  useDesignerVerify.setEmailVerify(designerInfo.value.email.isVerify);
};

const selectedRegion = computed(() => {
  //TODO 挑出被選擇的地區
  return designerInfo.value.work.region.filter((region) => region.checked);
});

onMounted(async () => {
  const res = await DesignerAccountService.getInfo({});
  updateDesignerInfo(res);
});
</script>

<template>
  <div class="w-full">
    <div class="cus-border my-8 flex flex-col items-center gap-2 max-md:p-4">
      <!-- Header Section -->
      <div class="flex w-full items-center justify-between max-md:flex-col md:text-lg">
        <div class="flex cursor-pointer items-center gap-x-2 p-2" @click="triggerFileInput('avatar')">
          <div class="relative flex-shrink-0 cursor-pointer p-2">
            <img
              v-if="designerInfo.avatar.url"
              :src="designerInfo.avatar.url"
              alt="User Image"
              class="h-24 w-24 rounded-full"
            />
            <UserIcon v-else class="breathing-light h-24 w-24 rounded-full" />
            <CameraIcon
              class="absolute bottom-2 right-0 h-6 w-6 cursor-pointer rounded bg-gray-300 bg-opacity-65 text-black"
            />
          </div>
          <input
            type="file"
            ref="avatarInput"
            :accept="UploadImageType"
            required
            @change="(event) => imageUpload(event, 'avatar', UploadFileEnum.Picture)"
            class="hidden"
          />
          <div class="cus-border px-4 py-2">
            <p>上傳大頭照</p>
            <p>讓客戶認識您，增加接單機會</p>
          </div>
        </div>
        <div class="cus-border cursor-pointer px-4 py-2" @click="gotoPortfolio()">
          <p>立即前往刊登作品集</p>
          <div v-if="designerInfo.verifyStatus === ProfileVerifyStatusEnum.Success">
            <p>刊登作品能增加您在家易的曝光率，增加接單機會</p>
            <p></p>
          </div>
          <div v-else>
            <p class="text-red-600">完成身分驗證、公司驗證、Email驗證</p>
            <p class="text-red-600">即可刊登作品</p>
          </div>
        </div>
      </div>

      <!-- Basic Info Section -->
      <div class="flex w-full flex-col space-y-4 md:text-lg">
        <p class="px-4 font-bold">基本資訊(合約使用)</p>
        <div class="cus-border flex items-center justify-between gap-2 p-4 max-md:flex-col">
          <div class="">
            <p>
              姓名 <span>{{ designerInfo.taiwanId.username }}</span>
            </p>
            <p>
              公司 <span>{{ designerInfo.company.unifiedBusinessNumber }}</span>
            </p>
            <p>
              Email <span>{{ designerInfo.email.address }}</span>
            </p>
          </div>
          <div class="flex gap-4 max-md:gap-2">
            <button
              class="hover-zoom flex flex-col items-center gap-1 rounded-md bg-white p-2"
              @click="openTaiwanIdModal()"
            >
              <img
                v-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Init"
                src="/vectors/verify/person_init.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img
                v-else-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Loading"
                src="/vectors/verify/person_loading.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img
                v-else-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Fail"
                src="/vectors/verify/person_fail.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img v-else src="/vectors/verify/person_success.svg" alt="" class="h-8 w-12 md:h-12 md:w-16" />
              <span class="text-nowrap max-md:text-sm">身份認證</span>
            </button>
            <button
              class="hover-zoom flex flex-col items-center gap-1 rounded-md bg-white p-2"
              @click="openCompanyModal()"
            >
              <img
                v-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Init"
                src="/vectors/verify/company_init.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img
                v-else-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Loading"
                src="/vectors/verify/company_loading.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img
                v-else-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Fail"
                src="/vectors/verify/company_fail.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img v-else src="/vectors/verify/company_success.svg" alt="" class="h-8 w-12 md:h-12 md:w-16" />
              <span class="text-nowrap max-md:text-sm">公司認證</span>
            </button>
            <button
              class="hover-zoom flex flex-col items-center gap-1 rounded-md bg-white p-2"
              @click="openEmailModal()"
            >
              <img
                v-if="designerInfo.email.isVerify"
                src="/vectors/verify/email_success.svg"
                alt=""
                class="h-8 w-12 md:h-12 md:w-16"
              />
              <img v-else src="/vectors/verify/email_fail.svg" alt="" class="h-8 w-12 md:h-12 md:w-16" />
              <span class="text-nowrap max-md:text-sm">Email認證</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Services Section -->
      <div class="flex w-full flex-col gap-2 md:text-lg">
        <div class="flex flex-col space-y-4">
          <p class="px-4 text-lg font-bold">服務資訊</p>
          <div class="cus-border space-y-4 p-4">
            <div class="flex flex-col items-start gap-2">
              <div class="flex w-full items-center justify-between">
                <p class="text-nowrap">服務項目</p>
                <Menu as="div" class="relative flex-none">
                  <MenuButton class="block p-2.5 text-gray-500 hover:text-gray-900">
                    <span class="sr-only">Open options</span>
                    <EllipsisVerticalIcon class="h-8 w-8" aria-hidden="true" />
                  </MenuButton>
                  <transition
                    enter-active-class="transition ease-out duration-100"
                    enter-from-class="transform opacity-0 scale-95"
                    enter-to-class="transform opacity-100 scale-100"
                    leave-active-class="transition ease-in duration-75"
                    leave-from-class="transform opacity-100 scale-100"
                    leave-to-class="transform opacity-0 scale-95"
                  >
                    <MenuItems
                      class="absolute right-0 z-10 mt-2 w-36 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none"
                    >
                      <MenuItem v-slot="{ active }">
                        <a
                          :class="[
                            active ? 'bg-gray-50' : '',
                            'block cursor-pointer px-3 py-1 text-base leading-6 text-gray-900'
                          ]"
                          @click="openServiceModal()"
                          >修改服務資訊</a
                        >
                      </MenuItem>
                    </MenuItems>
                  </transition>
                </Menu>
              </div>
              <div class="flex flex-wrap gap-2">
                <div v-if="designerInfo.work.type.surveyor" class="cus-border check-btn-selected px-4 py-2 text-base">
                  房屋丈量服務
                </div>
                <div v-if="designerInfo.work.type.designer" class="cus-border check-btn-selected px-4 py-2 text-base">
                  室內設計服務
                </div>
                <div v-if="designerInfo.work.type.decorator" class="cus-border check-btn-selected px-4 py-2 text-base">
                  裝潢施工服務
                </div>
              </div>
            </div>
            <div class="flex flex-col items-start gap-2">
              <p class="text-nowrap">服務地區</p>
              <div class="flex flex-wrap gap-2">
                <div v-for="(region, index) in selectedRegion" :key="index" class="flex items-center gap-x-2">
                  <div v-if="region.checked" class="cus-border check-btn-selected p-2 text-base">
                    {{ region.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--        <div class="flex justify-end">-->
        <!--          <button class="hover-zoom bg-color-selected font-bold py-2 px-4 rounded" @click="updateWork()">提交服務資訊-->
        <!--          </button>-->
        <!--        </div>-->
      </div>
    </div>
  </div>

  <DefaultModal
    title="修改服務資訊"
    :click-outside-close="false"
    :show-close-button="true"
    ref="serviceModalRef"
    @close-modal="closeServiceModal()"
  >
    <div class="flex w-full flex-col gap-2 space-y-4 p-8 md:text-lg">
      <div class="space-y-4 p-4">
        <div class="flex flex-col items-start gap-2">
          <p class="text-nowrap">服務項目</p>
          <div class="flex flex-wrap gap-2">
            <label
              :class="[
                'cus-border flex cursor-pointer items-center gap-x-2 px-4 py-2 text-base',
                tempWorkItem.work.type.designer ? 'check-btn-selected' : 'bg-white'
              ]"
            >
              <input type="checkbox" v-model="tempWorkItem.work.type.designer" class="hidden" />
              室內設計服務
            </label>
            <label
              :class="[
                'cus-border flex cursor-pointer items-center gap-x-2 px-4 py-2 text-base',
                tempWorkItem.work.type.decorator ? 'check-btn-selected' : 'bg-white'
              ]"
            >
              <input type="checkbox" v-model="tempWorkItem.work.type.decorator" class="hidden" />
              裝潢施工服務
            </label>
          </div>
        </div>
        <div class="flex flex-col items-start gap-2">
          <p class="text-nowrap">服務地區</p>
          <div class="flex flex-wrap gap-2">
            <label
              v-for="(region, index) in tempWorkItem.work.region"
              :key="index"
              :class="[
                'cus-border flex cursor-pointer items-center gap-x-2 p-2 text-base',
                region.checked ? 'check-btn-selected' : 'bg-white'
              ]"
            >
              <input type="checkbox" v-model="region.checked" class="hidden" />
              {{ region.name }}
            </label>
          </div>
        </div>
      </div>
      <button class="cus-btn w-full" @click="updateWork()">提交服務資訊</button>
    </div>
  </DefaultModal>

  <DefaultModal
    title="身分驗證"
    :click-outside-close="false"
    :show-close-button="true"
    ref="taiwanIdModalRef"
    @close-modal="closeTaiwanIdModal()"
  >
    <div class="flex flex-col space-y-4 px-12 py-6 text-start md:text-lg">
      <!-- Header -->
      <p class="text-center font-bold">
        驗證狀態
        <span v-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Init" class="text-red-500"
          >未驗證</span
        >
        <span v-else-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Loading" class="text-blue-600"
          >審核中</span
        >
        <span v-else-if="designerInfo.taiwanId.verifyStatus === ProfileVerifyStatusEnum.Fail" class="text-red-500"
          >驗證失敗</span
        >
        <span v-else class="text-blue-600">已驗證</span>
      </p>
      <p>1. 認證資料僅供家易認證審核使用，您的資料不會被顯示於任何頁面。</p>
      <p>2. 請上傳正確的資料，方便訂單合約簽訂。</p>
      <p class="text-red-500">本公司絕不會將任何隱私資料洩漏於第三方。</p>

      <label class="block text-nowrap text-black">姓名</label>
      <input
        type="text"
        placeholder="請輸入與身分證資料相同之姓名"
        v-model="designerInfo.taiwanId.username"
        class="cus-border focus-border-color w-full rounded-lg px-4 py-2 placeholder-gray-400"
      />
      <!-- Upload Section -->
      <p class="text-nowrap text-black">身分證上傳</p>
      <div class="flex gap-4 max-md:flex-col">
        <div
          class="cus-border flex cursor-pointer flex-col items-center p-4 md:w-1/2"
          @click="triggerFileInput('twIdFront')"
        >
          <img
            v-if="designerInfo.taiwanId.frontSideUrl"
            :src="designerInfo.taiwanId.frontSideUrl"
            alt=""
            class="h-36 w-52 object-scale-down"
          />
          <div v-else class="flex flex-col items-center">
            <img src="/vectors/general/upload.svg" alt="" class="h-8 w-8" />
            <span class="mt-2">身分證正面</span>
          </div>
        </div>
        <input
          type="file"
          ref="taiwanIdFrontInput"
          :accept="UploadImageType"
          required
          @change="(event) => imageUpload(event, 'twIdFront', UploadFileEnum.Picture)"
          class="hidden"
        />
        <div
          class="cus-border flex cursor-pointer flex-col items-center p-4 md:w-1/2"
          @click="triggerFileInput('twIdBack')"
        >
          <img
            v-if="designerInfo.taiwanId.backSideUrl"
            :src="designerInfo.taiwanId.backSideUrl"
            alt=""
            class="h-36 w-52 object-scale-down"
          />
          <div v-else class="flex flex-col items-center">
            <img src="/vectors/general/upload.svg" alt="" class="h-8 w-8" />
            <span class="mt-2">身分證反面</span>
          </div>
        </div>
        <input
          type="file"
          ref="taiwanIdBackInput"
          :accept="UploadImageType"
          required
          @change="(event) => imageUpload(event, 'twIdBack', UploadFileEnum.Picture)"
          class="hidden"
        />
      </div>

      <button class="cus-btn p-4" @click="updateTaiwanId()">提交身分驗證</button>
    </div>
  </DefaultModal>

  <DefaultModal
    title="公司驗證"
    :click-outside-close="false"
    :show-close-button="true"
    ref="companyModalRef"
    @close-modal="closeCompanyModal()"
  >
    <div class="flex flex-col space-y-4 px-12 py-6 text-start md:text-lg">
      <!-- Header -->
      <p class="text-center font-bold">
        驗證狀態
        <span v-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Init" class="text-red-500"
          >未驗證</span
        >
        <span v-else-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Loading" class="text-red-500"
          >審核中</span
        >
        <span v-else-if="designerInfo.company.verifyStatus === ProfileVerifyStatusEnum.Fail" class="text-red-500"
          >驗證失敗</span
        >
        <span v-else class="text-blue-600">已驗證</span>
      </p>
      <p>1.認證資料僅供家易認證審核使用，您的資料不會被顯示於任何頁面。</p>
      <p>2.請上傳正確的資料，方便訂單合約簽訂。</p>
      <p class="text-red-500">本公司絕不會將任何隱私資料洩漏於第三方。</p>
      <!-- Upload Section -->
      <div
        class="flex w-fit cursor-pointer items-center justify-start gap-x-4"
        @click="triggerFileInput('companyLogo')"
      >
        <div class="flex h-20 w-20 flex-shrink-0 items-center rounded-full">
          <div class="relative p-2">
            <img
              v-if="designerInfo.company.logo"
              :src="designerInfo.company.logo"
              alt="User Image"
              class="h-16 w-16 rounded-full"
            />
            <BuildingOfficeIcon v-else class="breathing-light h-12 w-12 text-gray-500" />
            <CameraIcon
              class="absolute -bottom-1 right-0 h-6 w-6 cursor-pointer rounded bg-gray-300 bg-opacity-65 text-black"
            />
          </div>
        </div>
        <input
          type="file"
          ref="companyLogoInput"
          :accept="UploadImageType"
          required
          @change="(event) => imageUpload(event, 'companyLogo', UploadFileEnum.Picture)"
          class="hidden"
        />
        <div class="cus-border flex flex-col items-start p-2">
          <p>上傳公司Logo</p>
          <p>讓客戶認識您，增加接單機會</p>
        </div>
      </div>

      <div class="flex flex-col gap-2">
        <label class="block text-nowrap text-black">公司名稱</label>
        <input
          type="text"
          placeholder="請輸入公司名稱"
          v-model="designerInfo.company.companyName"
          class="cus-border focus-border-color w-full rounded-lg px-4 py-2"
        />

        <label class="block text-nowrap text-black">公司地址</label>
        <input
          type="text"
          placeholder="請輸入公司地址"
          v-model="designerInfo.company.address"
          class="cus-border focus-border-color w-full rounded-lg px-4 py-2"
        />

        <label class="block text-nowrap text-black">公司服務時間</label>
        <input
          type="text"
          placeholder="請輸入服務時間"
          v-model="designerInfo.company.serviceTime"
          class="cus-border focus-border-color w-full rounded-lg px-4 py-2"
        />

        <label class="block text-nowrap text-black">公司統一編號</label>
        <input
          type="text"
          placeholder="請輸入公司統一編號"
          v-model="designerInfo.company.unifiedBusinessNumber"
          class="cus-border focus-border-color w-full rounded-lg px-4 py-2"
        />

        <p class="text-black">上傳公司登記證明文件或報稅等相關文件</p>
        <div
          v-for="(fileUrl, index) in designerInfo.company.companyDocumentUrls"
          :key="index"
          class="cus-border relative flex cursor-pointer items-center gap-2 rounded-none py-2"
        >
          <div class="w-full text-center" @click="handleDownloadFile(fileUrl)">
            <p>
              公司證明文件<span>{{ index + 1 }}</span>
            </p>
          </div>
          <img
            src="/vectors/general/close.svg"
            alt=""
            class="absolute right-2 h-6 w-6"
            @click="pullCompanyDocument(index)"
          />
        </div>
        <button
          class="mb-4 flex items-center justify-center gap-2 rounded-lg bg-gray-300 px-4 py-2 tracking-widest text-black"
          @click="triggerFileInput('companyDocument')"
        >
          <img src="/vectors/general/upload.svg" alt="" class="h-6 w-6" />
          新增相關文件
        </button>
        <input
          type="file"
          ref="companyDocumentInput"
          :accept="UploadImageType"
          required
          @change="(event) => imageUpload(event, 'companyDocument', UploadFileEnum.File)"
          class="hidden"
        />
      </div>
      <button class="cus-btn p-4" @click="updateCompany()">提交公司驗證</button>
    </div>
  </DefaultModal>

  <DefaultModal
    title="Email驗證"
    :click-outside-close="false"
    :show-close-button="true"
    ref="emailModalRef"
    @close-modal="closeEmailModal()"
  >
    <div class="mx-auto flex flex-col space-y-4 px-4 py-6 text-start md:px-24 md:text-lg">
      <p class="text-center font-bold">
        驗證狀態
        <span v-if="emailVerifyCodeSent" class="text-red-500">驗證碼已傳送 ({{ sentCodeCountDown }})</span>
        <span v-else-if="designerInfo.email.isVerify" class="text-blue-600">已驗證</span>
        <span v-else class="text-red-500">未驗證</span>
      </p>

      <label>請輸入Email</label>
      <div class="flex w-full items-center gap-2">
        <input
          type="email"
          v-model="designerInfo.email.address"
          :disabled="emailVerifyCodeSent"
          class="cus-border focus-border-color w-3/4 rounded-lg px-4 py-2"
        />
        <button
          class="cus-btn w-1/4 text-nowrap rounded-lg px-4 py-2"
          @click="sendEmailCheckCode()"
          :disabled="emailVerifyCodeSent"
        >
          驗證
        </button>
      </div>

      <label>請輸入驗證碼</label>
      <div class="flex w-full items-center gap-2">
        <input type="text" v-model="emailVerifyCode" class="cus-border focus-border-color w-3/4 rounded-lg px-4 py-2" />
        <button class="cus-btn w-1/4 text-nowrap rounded-lg px-4 py-2" @click="verifyEmail()">確認</button>
      </div>

      <div class="mx-auto items-center text-start">
        <p>
          若未收到驗證碼，請檢查您的<span class="text-red-500">垃圾郵件</span>
          <br />
          如果無法收到驗證碼或驗證相關問題
          <br />
          請聯絡客服：<a href="mailto:<EMAIL>" class="text-red-500"><EMAIL></a>
        </p>
      </div>
    </div>
  </DefaultModal>
</template>
