import { DeviceTypeEnum } from '@/model/enum/deviceType.ts';
import packageJson from '../../package.json';

export const getBrowserName = (navigator: Navigator): string => {
  const browserName = navigator.userAgent.toLowerCase();
  if (/msie/i.test(browserName) && !/opera/.test(browserName)) return 'IE';
  if (/firefox/i.test(browserName)) return 'Firefox';
  if (/chrome/i.test(browserName) && /webkit/i.test(browserName) && /mozilla/i.test(browserName)) return 'Chrome';
  if (/opera/i.test(browserName)) return 'Opera';
  if (/webkit/i.test(browserName) && !(/chrome/i.test(browserName) && /webkit/i.test(browserName) && /mozilla/i.test(browserName))) return 'Safari';
  return '';
};

/**
 * 裝置資訊
 */
export const deviceInfo = {
  deviceType: DeviceTypeEnum.Web,
  deviceBrand: getBrowserName(navigator),
  deviceModel: /Mobi|Android|iPhone/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
  deviceVersion: navigator.userAgent,
  deviceRegion: navigator.language,
  appVersion: packageJson.version,
  fcmToken: ''
};
