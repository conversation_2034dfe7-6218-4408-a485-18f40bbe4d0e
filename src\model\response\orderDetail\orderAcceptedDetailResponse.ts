import { BaseResponse } from '@/model/response/baseResponse.ts';
import {
  AcceptanceProcessStatusEnum,
  AmountStatusEnum,
  AppendProcessStatusEnum,
  ConstructionOrderStatusEnum,
  ConstructionProcessStatusEnum,
  DesignOrderStatusEnum,
  DesignOrderSubStatusEnum,
  MeasureOrderStatusEnum,
  SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import { Budget } from '@/model/general/budget.ts';

export interface MeasureOrderAcceptedDetailResponse extends BaseResponse {
  result: MeasureOrderAcceptedDetailItem;
}

export interface DesignOrderAcceptedDetailResponse extends BaseResponse {
  result: DesignOrderAcceptedDetailItem;
}

export interface ConstructionOrderAcceptedDetailResponse extends BaseResponse {
  result: ConstructionOrderAcceptedDetailItem;
}

export interface MeasureOrderAcceptedDetailItem {
  designerId: string;
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: AddressItem;
  measureTime: string;
  status: MeasureOrderStatusEnum;
  customerId: string;
  isDeleted: boolean;
  customerAvatarUrl: string;
  customerPhone: string;
  content: MeasureContentKeyItem;
  chatRoomId: string;
}

export interface DesignOrderAcceptedDetailItem {
  designerId: string;
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: AddressItem;
  publishInfo: DesignCustomerPublishInfo;
  measureContent: MeasureContentKeyItem;
  designerQuote: DesignerQuoteItem;
  chatRoomId: string;
  customerId: string;
  customerAvatarUrl: string;
  customerPhone: string;
  isDeleted: boolean;
  status: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  contract: DesignContractItem;
  designContent: DesignContentKeyItem;
}

export interface ConstructionOrderAcceptedDetailItem {
  designerId: string;
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: AddressItem;
  publishInfo: ConstructionCustomerPublishInfo;
  measureContent: MeasureContentKeyItem;
  designContent: DesignContentKeyItem;
  customerId: string;
  customerAvatarUrl: string;
  customerPhone: string;
  contract: ConstructionAcceptedContractItem;
  chatRoomId: string;
  isDeleted: boolean;
  status: ConstructionOrderStatusEnum;
  constructionContent: ConstructionContentItem;
}

export interface MeasureContentKeyItem {
  houseInfo: TextKeyContent;
  photos: ImageVideoKeyContent;
  houseCheck: ImageVideoKeyContent;
  floorPlan: ImagePdfCadKeyContent;
  note: TextKeyContent;
  constructionRequest: TextKeyContent;
  waterQuality: TextKeyContent;
  airQuality: TextKeyContent;
  noise: TextKeyContent;
  humidity: TextKeyContent;
  radiation: TextKeyContent;
}

export interface UnacceptedMeasureContentKeyItem extends MeasureContentKeyItem {
  houseDetection: TextKeyContent;
}

export interface DesignContentKeyItem {
  design2D: ImagePdfCadKeyContent;
  design3D: ImageVideoKeyContent;
  constructionAmountDocs: ConstructionAmountItem[];
}

export interface ConstructionContentItem {
  process: ProcessContent;
}

export interface DesignContractItem {
  signingStatus: SigningStatusEnum;
  isRemittanceInformationSet: boolean; //匯款資訊是否已設定
  amount: number; //設計金額(報價金額)
  customerRemittanceAmount: number;
}

export interface ConstructionAcceptedContractItem {
  constructionEstimate: Budget;
  isDetailQuoted: boolean;
  amountStatus: AmountStatusEnum;
  constructionAmountDocs: ConstructionAmountItem[];
  constructionAmount: number;
  constructionDiscountAmount: number;
  acceptanceProcessStatus: AcceptanceProcessStatusEnum;
  signingStatus: SigningStatusEnum;
  isRemittanceInformationSet: boolean; //匯款資訊是否已設定
  customerRemittanceAmount: number;
  appendProcess: AppendProcessContent;
  constructionDays: number;
  constructionRemainingDays: number;
  constructionWorkingEndDate :string;
  constructionPenaltyAmount: number;
}

export interface AppendProcessContent {
  updateTime: string;
  status: AppendProcessStatusEnum;
  times: number; //第幾次追加工程
  process: AppendProcessItem;
}

export interface TextKeyContent {
  updateTime: string;
  updateCount: number;
  content: TextKeyItem[];
}

export interface ImagePdfCadKeyContent {
  updateTime: string;
  updateCount: number;
  content: ImagePdfCadKeyItem[];
}

export interface ImageVideoKeyContent {
  updateTime: string;
  updateCount: number;
  content: ImageVideoKeyItem[];
}

export interface ProcessContent {
  updateTime: string;
  updateCount: number;
  content: ProcessKeyItem[];
}

export interface DesignCustomerPublishInfo {
  spaceUsage: string;
  designStyle: string;
  designTheme: string;
  designBudget: Budget;
  constructionBudget: Budget;
  discussionFrequency: number; // 每周討論頻率，單位次/周
  keyDesignDetail: string;
  referenceImages: ImageItem[];
  assignDesignerId?: string; //設計師端
  isAssignDesigner?: boolean; //客戶端
}

export interface ConstructionCustomerPublishInfo {
  isContinuedDesigner: boolean;
  constructionBudget: Budget;
  //備註細節
  constructionNote: string;
  spaceUsage: string;
  designStyle: string;
  designTheme: string;
  //Step3用不到
  keyDesignDetail: string;
  referenceImages: ImageItem[];
}

export interface AddressItem {
  fullName: string;
  simpleName: string;
  location: LocationItem;
}

export interface ImagePdfCadKeyItem {
  key: string;
  updateTime: string;
  name: string;
  images: MediaUrlKeyItem[];
  pdf: FileUrlItem;
  cad: FileUrlItem;
}

export interface ImageVideoKeyItem {
  key: string;
  updateTime: string;
  name: string;
  type: MediaTypeEnum;
  media: MediaUrlKeyItem[];
}

export interface TextKeyItem {
  key: string;
  updateTime: string;
  name: string; //項目欄位名稱
  text: string; //在HouseInfo的情況下是JsonString，但在note就是正常String
}

export interface FileUrlItem {
  updateTime: string;
  url: string;
}

export interface MediaUrlKeyItem {
  key: string;
  updateTime: string;
  url: string;
  description: string;
}

export interface LocationItem {
  lat: number;
  lng: number;
}

export interface ImageItem {
  updateTime: string;
  url: string;
}

export interface DesignerQuoteItem {
  designAmount: number;
  constructionEstimate: Budget;
}

export interface AppendProcessItem {
  name: string;
  amount: number;
  amountDocUrl: string;
}

export interface ConstructionAmountItem {
  createTime: string;
  amount: number;
  documentUrl: string;
}

export interface ProcessKeyItem {
  key: string;
  updateTime: string;
  name: string;
  updateCount: number;
  status: ConstructionProcessStatusEnum;
  amount: number;
  media: MediaUrlKeyItem[];
  isAppend: boolean;
  amountDocUrl: string;
}