import { ChatHubClass } from '@/utils/SignalRService.ts';
import { InvokeSignalRMethodEnum, OnSignalRMethodEnum, WorkerEventEnum } from '@/model/enum/sharedWorkerEnum.ts';

const ports: MessagePort[] = [];

const signalRHub = import.meta.env.VITE_SIGNALR_URL;
const signalrOnMethods = [
  OnSignalRMethodEnum.Test,
  OnSignalRMethodEnum.MessageRead,
  OnSignalRMethodEnum.MessageUnsend,
  OnSignalRMethodEnum.Message,
  OnSignalRMethodEnum.Room,
  OnSignalRMethodEnum.SyncSelfSentMsg
];
const customerChatHub = new ChatHubClass(signalRHub);
const designerChatHub = new ChatHubClass(signalRHub);
//isConnected儲存第一層連線(真實的網路連線)結果 交給ChatHubClass維護
//isXXXConnectionAuthed儲存第二層連線(自定義的伺服器驗證)結果 需要手動維護
let isCustomerConnectionAuthed: boolean = false;
let isDesignerConnectionAuthed: boolean = false;

//建立Worker實例時 就進行以下signalR連線
designerSignalRConnect();
customerSignalRConnect();

self.onconnect = async (e: MessageEvent) => {
  console.log('[Shared Worker] connected');
  const port = e.ports[0];
  ports.push(port);//每當有新頁面連入時 加入ports
  console.log('[Shared Worker] ports.length:', ports.length);

  port.onmessage = async (e: MessageEvent) => {//Worker監聽來自每個分頁面的事件
    console.log('接到事件', e.data);
    const payload = e.data.payload;
    if (e.data.isDesigner) {
      switch (e.data.type) {
        case WorkerEventEnum.SyncSelfSentMsg: {
          console.log('[Shared Worker] SyncSelfSentMsg', payload);
          const args = payload.args;
          for (const port of ports) {
            port.postMessage({
              isDesigner: true,
              type: WorkerEventEnum.OnSignalR,
              payload: { method: OnSignalRMethodEnum.SyncSelfSentMsg, args: args }
            });
          }
          break;
        }

        case WorkerEventEnum.SyncSelfUnsendMsg: {
          console.log('[Shared Worker] SyncSelfUnsendMsg', payload);
          const args = payload.args;
          for (const port of ports) {
            port.postMessage({
              isDesigner: true,
              type: WorkerEventEnum.OnSignalR,
              payload: { method: OnSignalRMethodEnum.MessageUnsend, args: args }
            });
          }
          break;
        }

        case WorkerEventEnum.SetSignalRAuthFalse: {
          console.log('[Shared Worker] InvokeConnectToSignalR', 'isDesignerConnectionAuthed', isDesignerConnectionAuthed);
          if (!designerChatHub.isConnected) port.postMessage({
            isDesigner: true,
            type: WorkerEventEnum.SignalRConnectionError,
            payload: 'SignalR not connected -> SetSignalRAuthFalse'
          });
          isDesignerConnectionAuthed = false;
          break;
        }

        case WorkerEventEnum.InvokeConnectToSignalR: {
          if (isDesignerConnectionAuthed) return; //避免重複對 SignalR 發送連線事件
          console.log('[Shared Worker] InvokeConnectToSignalR', 'isDesignerConnectionAuthed', isDesignerConnectionAuthed);
          console.log('[Shared Worker] DesignerchatHub.isConnected:', payload);
          if (!designerChatHub.isConnected) {
            port.postMessage({
              isDesigner: true,
              type: WorkerEventEnum.SignalRConnectionError,
              payload: 'SignalR not connected -> InvokeConnectToSignalR'
            });
            console.log('waiting designerChatHub connect', designerChatHub.isConnected, 'count 0');
          }
          let waitCount = 0;
          while (!designerChatHub.isConnected && waitCount < 10) {//等待上限五秒
            await new Promise((resolve) => setTimeout(resolve, 500));
            waitCount++;
            console.log('waiting designerChatHub connect', designerChatHub.isConnected, 'count ', waitCount);
          }
          if (!designerChatHub.isConnected) {//嘗試重新連線
            designerSignalRConnect();
          }
          await designerChatHub.invoke(InvokeSignalRMethodEnum.Connect, payload.args); //要傳Token
          break;
        }

        case WorkerEventEnum.WindowClose: {
          console.log('[Shared Worker] windowUnload');
          const index = ports.indexOf(port);
          if (index > -1) ports.splice(index, 1);
          console.log('[Shared Worker] ports.length:', ports.length);
          break;
        }

        case WorkerEventEnum.InvokeToSignalR: {
          if (!designerChatHub.isConnected) port.postMessage({
            isDesigner: true,
            type: WorkerEventEnum.SignalRConnectionError,
            payload: 'Designer SignalR not connected -> InvokeToSignalR'
          });
          await designerChatHub.invoke(payload.method, ...payload.args);
          break;
        }
        case WorkerEventEnum.SignalRConnectionError: {//API接到status300
          isDesignerConnectionAuthed = false;//第二層連線設為False
          console.log('[Shared Worker] Designer', payload);
          if(!designerChatHub.isConnected) {//檢查第一層連線並重連
            designerSignalRConnect();
            let waitCount = 0;
            while (!designerChatHub.isConnected && waitCount < 10) {
              await new Promise((resolve) => setTimeout(resolve, 500));
              waitCount++;
              console.log('waiting designerChatHub connect', designerChatHub.isConnected, 'count ', waitCount);
            }
          }
          if (!designerChatHub.isConnected) {//第一層連線失敗
            console.log('[Shared Worker] designer connect error');
            break;
          }
          await designerChatHub.invoke(InvokeSignalRMethodEnum.Connect, payload.args[1]); //傳Token 重新建立第二層連線
          break;
        }
        default: {
          console.error('[Shared Worker] Designer unknown message type:', e.data.type);
          break;
        }
      }

    } else {
      switch (e.data.type) {
        case WorkerEventEnum.SyncSelfSentMsg: {
          console.log('[Shared Worker] SyncSelfSentMsg', payload);
          const args = payload.args;
          for (const port of ports) {
            port.postMessage({
              isDesigner: false,
              type: WorkerEventEnum.OnSignalR,
              payload: { method: OnSignalRMethodEnum.SyncSelfSentMsg, args: args }
            });
          }
          break;
        }

        case WorkerEventEnum.SyncSelfUnsendMsg: {
          console.log('[Shared Worker] SyncSelfUnsendMsg', payload);
          const args = payload.args;
          for (const port of ports) {
            port.postMessage({
              isDesigner: false,
              type: WorkerEventEnum.OnSignalR,
              payload: { method: OnSignalRMethodEnum.MessageUnsend, args: args }
            });
          }
          break;
        }

        case WorkerEventEnum.SetSignalRAuthFalse: {
          console.log('[Shared Worker] InvokeConnectToSignalR', 'isCustomerConnectionAuthed', isCustomerConnectionAuthed);
          if (!customerChatHub.isConnected) port.postMessage({
            type: WorkerEventEnum.SignalRConnectionError,
            payload: 'CustomerSignalR not connected -> SetSignalRAuthFalse'
          });
          isCustomerConnectionAuthed = false;
          break;
        }

        case WorkerEventEnum.InvokeConnectToSignalR: {
          if (isCustomerConnectionAuthed) return; //避免重複對 SignalR 發送連線事件
          console.log('[Shared Worker] InvokeConnectToSignalR', 'isCustomerConnectionAuthed', isCustomerConnectionAuthed);
          console.log('[Shared Worker] CustomerchatHub.isConnected:', payload);
          if (!customerChatHub.isConnected) {
            port.postMessage({
              type: WorkerEventEnum.SignalRConnectionError,
              payload: 'CustomerSignalR not connected -> InvokeConnectToSignalR'
            });
            console.log('waiting customerChatHub connect', customerChatHub.isConnected, 'count 0');
          }
          let waitCount = 0;
          while (!customerChatHub.isConnected && waitCount < 10) {//等待上限五秒
            await new Promise((resolve) => setTimeout(resolve, 500));
            waitCount++;
          }
          if (!customerChatHub.isConnected) {//嘗試重新連線
            customerSignalRConnect();
          }
          await customerChatHub.invoke(InvokeSignalRMethodEnum.Connect, payload.args); //要傳Token
          break;
        }

        case WorkerEventEnum.WindowClose: {
          console.log('[Shared Worker] windowUnload');
          const index = ports.indexOf(port);
          if (index > -1) ports.splice(index, 1);
          console.log('[Shared Worker] ports.length:', ports.length);
          break;
        }

        case WorkerEventEnum.InvokeToSignalR: {
          if (!customerChatHub.isConnected) port.postMessage({
            type: WorkerEventEnum.SignalRConnectionError,
            payload: 'Customer SignalR not connected -> InvokeToSignalR'
          });
          await customerChatHub.invoke(payload.method, ...payload.args);
          break;
        }
        case WorkerEventEnum.SignalRConnectionError: {//API接到status300
          isCustomerConnectionAuthed = false;
          console.log('[Shared Worker] Customer ', payload);
          if(!customerChatHub.isConnected) {
            customerSignalRConnect();
            let waitCount = 0;
            while (!customerChatHub.isConnected && waitCount < 10) {
              await new Promise((resolve) => setTimeout(resolve, 500));
              waitCount++;
            console.log('waiting customerChatHub connect', customerChatHub.isConnected, 'count ', waitCount);
            }
          }
          if (!customerChatHub.isConnected) {//第一層連線失敗
            console.log('[Shared Worker] customer connect error');
            break;
          }
          await customerChatHub.invoke(InvokeSignalRMethodEnum.Connect, payload.args[1]); //要傳Token 重新建立第二層連線
          break;
        }
        default: {
          console.error('[Shared Worker] Customer unknown message type:', e.data.type);
          break;
        }
      }
    }

  };
};

interface SharedWorkerGlobalScope extends Window {
  onconnect: (event: MessageEvent) => void;
}

declare let self: SharedWorkerGlobalScope;

async function designerSignalRConnect() {
  designerChatHub.connection().then(async () => {
    console.log('[Shared Worker] Designer SignalR connection success');
  
    designerChatHub.onclose(() => {
      console.log('[Shared Worker] Designer SignalR connection closed');
    });
  
    signalrOnMethods.forEach((method) => {//開啟 SignalR 的監聽 
      designerChatHub.on(method, (...args: any[]) => {
        for (const port of ports) {
          port.postMessage({//接到事件 Worker轉發給每個分頁面
            isDesigner: true,
            type: WorkerEventEnum.OnSignalR,
            payload: { method, args }
          });
        }
      });
    });
  
    designerChatHub.on(OnSignalRMethodEnum.Connect, (isSuccess: boolean) => {
      if (!isSuccess) {
        isDesignerConnectionAuthed = false;
        throw new Error('Failed to connect to Designer SignalR hub');
      } else {
        console.log('[Shared Worker] Designer SignalR connection authed');
        isDesignerConnectionAuthed = true;
      }
    });
  });
}
async function customerSignalRConnect() {
  customerChatHub.connection().then(async () => {
    console.log('[Shared Worker] Customer SignalR connection success');
    console.log('[Shared Worker] isConnect:',customerChatHub.isConnected);
    customerChatHub.onclose(() => {
      console.log('[Shared Worker] Customer SignalR connection closed');
    });
  
    signalrOnMethods.forEach((method) => {
      customerChatHub.on(method, (...args: any[]) => {
        for (const port of ports) {
          port.postMessage({
            isDesigner: false,
            type: WorkerEventEnum.OnSignalR,
            payload: { method, args }
          });
        }
      });
    });
  
    customerChatHub.on(OnSignalRMethodEnum.Connect, (isSuccess: boolean) => {
      if (!isSuccess) {
        isCustomerConnectionAuthed = false;
        throw new Error('Failed to connect to Customer SignalR hub');
      } else {
        console.log('[Shared Worker] Customer SignalR connection authed');
        isCustomerConnectionAuthed = true;
      }
    });
  });
}