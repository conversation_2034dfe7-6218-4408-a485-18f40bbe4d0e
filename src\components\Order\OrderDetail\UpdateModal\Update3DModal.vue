<script setup lang="ts">
import { ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ImageVideoKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import Upload3DModalContent from '@/components/Order/OrderDetail/UpdateModal/ModalContent/Upload3DModalContent.vue';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps<{ title: string; orderId: string; }>();
const design3D = defineModel<ImageVideoKeyContent>('design3D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus', { required: true });
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus', { required: true });

const emits = defineEmits(['refreshData']);

const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const isApiLoading = ref(false);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  if (isApiLoading.value) {
    toastInfo('請等待檔案上傳完成');
  } else {
    updateModalRef.value?.closeModal();
  }
};

const changeApiLoading = (isLoading: boolean) => {
  isApiLoading.value = isLoading;
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" ref="updateModalRef"
                :click-outside-close="false"
                :showCloseButton="true"
                @closeModal="closeModal">
    <Upload3DModalContent :order-id="props.orderId"
                          v-model:design3-d="design3D"
                          v-model:order-status="orderStatus"
                          :sub-status="subStatus"
                          @refresh-data="emits('refreshData')"
                          @change-api-loading="changeApiLoading" />
  </DefaultModal>
</template>
