<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import <PERSON>zDivider from '@/components/General/HezDivider.vue';
import {
  ImagePdfCadKeyItem,
  ImageVideoKeyItem,
  TextKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { DesignOrderUnacceptedDetailItem } from '@/model/response/orderDetail/orderUnacceptedDetailResponse.ts';
import { DesignOrderDetailService, OrderDetailService } from '@/api/designerOrder.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import HousePhoto from '@/components/Order/OrderDetail/SubComponets/HousePhoto.vue';
import HouseCheck from '@/components/Order/OrderDetail/SubComponets/HouseCheck.vue';
import FloorPlan from '@/components/Order/OrderDetail/SubComponets/FloorPlan.vue';
import HouseNote from '@/components/Order/OrderDetail/SubComponets/HouseNote.vue';
import TextContent from '@/components/Order/OrderDetail/SubComponets/TextContent.vue';
import { budgetCombineLowToHigh, moneyAddCommas } from '@/utils/budgetFormat.ts';
import DesignHouseInfoWithSixPack from '@/components/Order/OrderDetail/SubComponets/DesignHouseInfoWithSixPack.vue';
import { parseJsonString } from '@/utils/JsonStringFormat.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import {
  constructionLowerAmountMax,
  constructionLowerAmountMin,
  constructionUpperAmountMin,
  initConstructionLowerAmountInput,
  initConstructionUpperAmountInput,
  initDesignAmountInput
} from '@/utils/orderQuoteService.ts';
import { formatInteger } from '@/utils/numberFormat.ts';
import { toastWarning } from '@/utils/toastification.ts';

const orderData = ref<DesignOrderUnacceptedDetailItem>({
  orderId: '',
  createTime: '',
  refreshTime: '',
  customerName: '',
  address: '',
  publishInfo: {
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    designBudget: {
      upper: 0,
      lower: 0
    },
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    discussionFrequency: 0, // 每周討論頻率，單位次/周
    keyDesignDetail: '',
    referenceImages: [],
    assignDesignerId: '' //設計師端
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    houseDetection: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    }
  },
  designerQuote: {
    designAmount: -1,
    constructionEstimate: {
      upper: -1,
      lower: -1
    }
  },
  chatRoomId: ''
});

const route = useRoute();
const router = useRouter();
const orderId = route.params.id as string;
const rejectOrderModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const updateQuoteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const stepEnabled = ref<boolean>(true);
const stepStatusMessage = ref<string>('');

const getOrderData = async () => {
  const res = await OrderDetailService.getDesignUnaccepted({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    orderData.value = res.result;
  } else if (res.status === APIStatusCodeEnum.OrderStatusError) {
    // 跳轉至已接單的路由
    await router.push({ name: 'DesignAcceptedOrderDetail', params: { id: orderId } });
  }
};

const orderDataDetail = computed(() => {
  const mapJsonString = orderData.value.measureContent.houseInfo.content.map(parseJsonString);
  const sixPackHousePing = mapJsonString.find((item) => item.name === '室內坪數')?.text.content;
  const sixPackHouseAge = mapJsonString.find((item) => item.name === '房屋年齡')?.text.content;
  const sixPackHouseType = mapJsonString.find((item) => item.name === '房屋類型')?.text.content;
  return {
    orderId: orderData.value.orderId,
    customerName: orderData.value.customerName,
    address:
      orderData.value.designerQuote.designAmount === -1 ? `${orderData.value.address}***` : orderData.value.address,
    publishInfoForHouseInfo: orderData.value.publishInfo,
    publishInfo: {
      spaceUsage: orderData.value.publishInfo.spaceUsage,
      designStyle: orderData.value.publishInfo.designStyle,
      designTheme: orderData.value.publishInfo.designTheme,
      designBudget: budgetCombineLowToHigh(orderData.value.publishInfo.designBudget),
      constructionBudget: budgetCombineLowToHigh(orderData.value.publishInfo.constructionBudget),
      discussionFrequency: orderData.value.publishInfo.discussionFrequency,
      keyDesignDetail: orderData.value.publishInfo.keyDesignDetail,
      referenceImages: orderData.value.publishInfo.referenceImages,
      assignDesignerId: orderData.value.publishInfo.assignDesignerId
    },
    measureContent: orderData.value.measureContent,
    designerQuote: {
      designAmount: moneyAddCommas(orderData.value.designerQuote.designAmount),
      constructionEstimate: budgetCombineLowToHigh(orderData.value.designerQuote.constructionEstimate)
    },
    hasQuote: orderData.value.designerQuote.designAmount !== -1,
    sixPack: {
      ping: `${sixPackHousePing?.text} ${sixPackHousePing?.unit}`,
      age: `${sixPackHouseAge?.text} ${sixPackHouseAge?.unit}`,
      houseType: sixPackHouseType?.text,
      spaceUsage: orderData.value.publishInfo.spaceUsage,
      designStyle: orderData.value.publishInfo.designStyle,
      designTheme: orderData.value.publishInfo.designTheme
    },
    areaPing: parseFloat(sixPackHousePing ? sixPackHousePing.text : '1')
  };
});

const designAmountInput = ref<string>('1');
const constructionUpperAmountInput = ref<string>('2');
const constructionLowerAmountInput = ref<string>('1');

const initQuotesInput = () => {
  designAmountInput.value = initDesignAmountInput(
    orderData.value.designerQuote.designAmount,
    orderDataDetail.value.areaPing
  ).toString();
  constructionUpperAmountInput.value = initConstructionUpperAmountInput(
    orderData.value.designerQuote.constructionEstimate.upper,
    orderDataDetail.value.areaPing
  ).toString();
  constructionLowerAmountInput.value = initConstructionLowerAmountInput(
    orderData.value.designerQuote.constructionEstimate.lower,
    orderDataDetail.value.areaPing
  ).toString();
};

const openQuoteModal = () => {
  initQuotesInput();
  updateQuoteModalRef.value?.openModal();
};

const closeQuoteModal = () => {
  updateQuoteModalRef.value?.closeModal();
};

const designAmountChange = () => {
  designAmountInput.value = formatInteger(designAmountInput.value); // 資料清理
  if (designAmountInput.value === '') {
    designAmountInput.value = designAmountInput.value = '1';
  }
  // 設計報價下限->1萬
  if (parseInt(designAmountInput.value) < 1) designAmountInput.value = '1';
};

const constructionUpperAmountChange = () => {
  constructionUpperAmountInput.value = formatInteger(constructionUpperAmountInput.value); // 資料清理
  if (constructionUpperAmountInput.value === '') {
    constructionUpperAmountInput.value = initConstructionUpperAmountInput(
      orderData.value.designerQuote.constructionEstimate.upper,
      orderDataDetail.value.areaPing
    ).toString();
  }
  // if (parseInt(constructionUpperAmountInput.value) < constructionUpperAmountMin(orderDataDetail.value.areaPing)) {
  //   constructionUpperAmountInput.value = constructionUpperAmountMin(orderDataDetail.value.areaPing).toString();
  // }
  // // 因 裝潢報價(最低)下限->裝潢報價(最高) *0.7 無條件進位 所以會需要重新判斷裝潢報價(最低)是否符合條件
  // constructionLowerAmountChange();
};

const constructionLowerAmountChange = () => {
  constructionLowerAmountInput.value = formatInteger(constructionLowerAmountInput.value); // 資料清理
  if (constructionLowerAmountInput.value === '') {
    constructionLowerAmountInput.value = initConstructionLowerAmountInput(
      orderData.value.designerQuote.constructionEstimate.lower,
      orderDataDetail.value.areaPing
    ).toString();
  }
  // if (parseInt(constructionLowerAmountInput.value) > constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value))) {
  //   constructionLowerAmountInput.value = constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value)).toString();
  // }
  //
  // if (parseInt(constructionLowerAmountInput.value) < constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value))) {
  //   constructionLowerAmountInput.value = constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value)).toString();
  // }
};

const cancelQuote = async () => {
  const res = await DesignOrderDetailService.quoteCancel({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    rejectOrderModalRef.value?.closeModal();
    await router.push({ name: 'designerOrderList' });
  }
};

const rejectOrder = async () => {
  const res = await DesignOrderDetailService.rejectOrder({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    rejectOrderModalRef.value?.closeModal();
    await router.push({ name: 'designerOrderList' });
  }
};

const updateQuote = async () => {
  if (parseInt(constructionUpperAmountInput.value) < constructionUpperAmountMin(orderDataDetail.value.areaPing)) {
    toastWarning('裝潢報價的最高金額不得低於坪數乘以9');
    return;
  }

  if (
    parseInt(constructionLowerAmountInput.value) >
    constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value))
  ) {
    toastWarning('裝潢報價的最低金額不得高於最高金額');
    return;
  }

  if (
    parseInt(constructionLowerAmountInput.value) <
    constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value))
  ) {
    toastWarning('裝潢報價的最低金額需為最高金額的70%以上');
    return;
  }

  console.log(
    'updateQuote',
    designAmountInput.value,
    constructionUpperAmountInput.value,
    constructionLowerAmountInput.value
  );
  const res = await DesignOrderDetailService.quote({
    orderId,
    designAmount: parseInt(designAmountInput.value),
    constructionEstimate: {
      upper: parseInt(constructionUpperAmountInput.value),
      lower: parseInt(constructionLowerAmountInput.value)
    }
  });
  if (res.status === APIStatusCodeEnum.Success) {
    await getOrderData();
    updateQuoteModalRef.value?.closeModal();
  } else if (res.status === APIStatusCodeEnum.StepFeatureClosed) {
    updateQuoteModalRef.value?.closeModal();
    stepEnabled.value = false;
    stepStatusMessage.value = '敬請期待功能開放';
  } else if (res.status === APIStatusCodeEnum.DesignerOrderDisabled) {
    updateQuoteModalRef.value?.closeModal();
    stepEnabled.value = false;
    stepStatusMessage.value = '接單權限已關閉';
  }
};

const goBack = () => {
  router.back();
};

onMounted(async () => {
  await getOrderData();
});
</script>

<template>
  <div v-if="!stepEnabled" class="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
    <div class="flex flex-col items-center rounded-2xl bg-white px-10 py-8 shadow-2xl">
      <span class="mb-4 text-2xl font-bold">{{ stepStatusMessage }}</span>
      <button class="cus-btn w-full" @click="goBack()">確認</button>
    </div>
  </div>

  <div class="my-8 flex w-full flex-col gap-y-8">
    <div class="cus-border text-lg">
      <h2 class="text-center text-2xl font-bold">室內設計</h2>
      <div class="flex flex-col gap-y-1 p-4">
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">屋主姓名</p>
          <p class="font-bold">{{ orderDataDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">設計地址</p>
          <p class="font-bold">{{ orderDataDetail.address }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">設計預算</p>
          <p class="font-bold">{{ orderDataDetail.publishInfo.designBudget }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">裝潢預算</p>
          <p class="font-bold">{{ orderDataDetail.publishInfo.constructionBudget }}</p>
        </div>
        <div class="flex gap-x-2" v-if="orderDataDetail.hasQuote">
          <p class="text-nowrap font-bold">設計報價</p>
          <p class="font-bold text-red-600">{{ orderDataDetail.designerQuote.designAmount }}</p>
        </div>
        <div class="flex gap-x-2" v-if="orderDataDetail.hasQuote">
          <p class="text-nowrap font-bold">裝潢報價</p>
          <p class="font-bold text-red-600">{{ orderDataDetail.designerQuote.constructionEstimate }}</p>
        </div>
      </div>
      <HezDivider />
      <div class="rounded-lg p-4">
        <h3 class="mb-4 text-lg font-bold">設計重點</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">室內坪數</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.ping }}</p>
          </div>
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">裝修用途</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.spaceUsage }}</p>
          </div>
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">設計風格</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.designStyle }}</p>
          </div>
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">設計主題</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.designTheme }}</p>
          </div>
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">房屋年齡</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.age }}</p>
          </div>
          <div class="rounded-lg bg-gray-100 p-4 shadow-md">
            <p class="text-gray-600">房屋類型</p>
            <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.houseType }}</p>
          </div>
        </div>
      </div>
      <HezDivider />
      <!-- 按鈕 -->
      <div class="flex justify-evenly gap-2 max-md:flex-col">
        <button
          class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
          @click="rejectOrderModalRef?.openModal"
        >
          <span v-if="orderDataDetail.hasQuote">取消報價</span>
          <span v-else>無法接單</span>
        </button>
        <button
          class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
          @click="openQuoteModal()"
        >
          <span v-if="orderDataDetail.hasQuote">更改報價</span>
          <span v-else>我要接單</span>
        </button>
      </div>
    </div>

    <DesignHouseInfoWithSixPack
      v-model:house-info="orderDataDetail.measureContent.houseInfo"
      v-model:publish-info="orderDataDetail.publishInfoForHouseInfo"
    />
    <HousePhoto v-model="orderDataDetail.measureContent.photos" :orderId="orderId" :disabled="true" />
    <HouseCheck v-model="orderDataDetail.measureContent.houseCheck" :orderId="orderId" :disabled="true" />
    <FloorPlan v-model="orderDataDetail.measureContent.floorPlan" :orderId="orderId" :disabled="true" />
    <TextContent
      v-model="orderDataDetail.measureContent.constructionRequest"
      :orderId="orderId"
      title="裝潢需求"
      contentName="constructionRequest"
      :canNotDeleteCount="16"
      :disabled="true"
      :canAddUnit="false"
    />
    <TextContent
      v-model="orderDataDetail.measureContent.houseDetection"
      :orderId="orderId"
      title="房屋檢測"
      contentName="houseDetection"
      :canNotDeleteCount="16"
      :disabled="true"
      :canAddUnit="false"
    />
    <HouseNote v-model="orderDataDetail.measureContent.note" :orderId="orderId" :disabled="true" />
  </div>

  <DefaultModal
    :title="orderDataDetail.hasQuote ? '取消報價' : '無法接單'"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="rejectOrderModalRef"
    @closeModal="rejectOrderModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">
          <span v-if="orderDataDetail.hasQuote"> 當您選擇取消報價後 </span>
          <span v-else>當您選擇拒絕這筆訂單後</span>
        </p>
        <p class="font-bold md:text-lg">該訂單將不再於您的訂單列表中顯示</p>
        <button
          type="button"
          v-if="orderDataDetail.hasQuote"
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="cancelQuote()"
        >
          確定
        </button>
        <button
          type="button"
          v-else
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="rejectOrder()"
        >
          確定
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal
    :title="orderDataDetail.hasQuote ? '更改報價' : '確定報價'"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="updateQuoteModalRef"
    @closeModal="closeQuoteModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <!-- 設計報價 -->
        <div class="mb-4 w-full">
          <label class="mb-2 block font-bold text-gray-700">設計報價</label>
          <div class="flex items-center">
            <span class="mr-2">$</span>
            <input
              v-model="designAmountInput"
              type="text"
              inputmode="numeric"
              class="input-basic flex-1 rounded-md"
              @change="designAmountChange()"
            />
            <span class="ml-2">萬</span>
          </div>
          <p class="mt-2 text-start text-black">委託人預算：{{ orderDataDetail.publishInfo.designBudget }}</p>
        </div>

        <!-- 裝潢報價 -->
        <div class="mb-4 w-full">
          <label class="mb-2 block font-bold text-gray-700">裝潢報價</label>
          <div class="mb-2 flex items-center">
            <span class="mr-2">最高$</span>
            <input
              v-model="constructionUpperAmountInput"
              type="text"
              inputmode="numeric"
              class="input-basic flex-1 rounded-md"
              @change="constructionUpperAmountChange()"
            />
            <span class="ml-2">萬</span>
          </div>
          <div class="flex items-center">
            <span class="mr-2">最低$</span>
            <input
              v-model="constructionLowerAmountInput"
              type="text"
              inputmode="numeric"
              class="input-basic flex-1 rounded-md"
              @change="constructionLowerAmountChange()"
            />
            <span class="ml-2">萬</span>
          </div>
          <p class="mt-2 text-start text-black">委託人預算：{{ orderDataDetail.publishInfo.constructionBudget }}</p>
        </div>

        <!-- 注意事項 -->
        <div class="mb-4 w-full">
          <p class="text-start text-sm text-black">
            註1：裝潢報價可以超過委託人預算，需包含廚具及空調等。裝潢報價的最高金額將來作為裝潢施工的最高金額，未經委託人同意不得任意更改，否則將以設計費用50%作為補償給客戶。
          </p>
          <p class="mt-2 text-start text-sm text-black">
            註2：為了能確保裝潢品質，在平台報價後，請勿私底下接單，如有違反，需賠償金額為100倍的設計報價和裝潢報價。
          </p>
        </div>

        <button
          type="button"
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="updateQuote()"
        >
          確定報價
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
