<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import { toastInfo, toastWarning } from '@/utils/toastification.ts';
import { CustomerMeasureOrderDetailService, CustomerMeasureOrderService } from '@/api/customerOrder.ts';
import { MeasureOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import { CustomerOrderMeasureItem } from '@/model/response/customerMeasureOrderResponse.ts';
import CustomerStep1TextContent from '@/components/customer/OrderDetail/CustomerStep1TextContent.vue';
import CustomerHousePhoto from '@/components/customer/OrderDetail/CustomerHousePhoto.vue';
import CustomerHouseCheck from '@/components/customer/OrderDetail/CustomerHouseCheck.vue';
import CustomerFloorPlan from '@/components/customer/OrderDetail/CustomerFloorPlan.vue';
import CustomerHouseNote from '@/components/customer/OrderDetail/CustomerHouseNote.vue';
import { DatePickerToFullDateTimeWithDay, formatFullDateTimeWithDay, TimeToEmptyString } from '@/utils/timeFormat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  UserCircleIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import HezDivider from '@/components/General/HezDivider.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import GoToAppModal from '@/components/Modal/GoToAppModal.vue';
import VueDatePicker, { type DatePickerInstance } from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { isSameDay } from 'date-fns';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import CustomerRateModal from '@/components/Modal/CustomerRateModal.vue';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';

const route = useRoute();
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const chatHubStore = useChatHubStore();
const orderId = route.params.id as string;
const measureNotAssignModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const deleteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const goToAppModalRef = ref<InstanceType<typeof GoToAppModal> | null>(null);
const goToAppModalProps = ref<{ title: string; content: string; content2?: string }>({
  title: '',
  content: '',
  content2: ''
});
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal> | null>(null);
const completeModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const rateModalRef = ref<InstanceType<typeof CustomerRateModal> | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const refillTime = ref<string[]>([]);
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);

const orderData = ref<CustomerOrderMeasureItem>({
  orderId: '',
  createTime: '',
  refreshTime: '',
  status: MeasureOrderStatusEnum.WaitingUpload,
  customerId: '',
  customerName: '',
  isDeleted: false,
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  designerId: '',
  designerName: '',
  designerAvatarUrl: '',
  designerPhone: '',
  measureDate: {
    refreshTime: '',
    isConfirm: false,
    measureTime: '',
    reserveTimes: []
  },
  content: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
  },
  isNextStepUsed: false,
  chatRoomId: '',
  orderRatingId: ''
});

const goHome = () => {
  router.push({
    name: 'customerhome'
  });
};

const measureDetail = computed(() => {
  return {
    orderId: orderData.value.orderId,
    createTime: orderData.value.createTime,
    refreshTime: orderData.value.refreshTime,
    status: orderData.value.status,
    customerId: orderData.value.customerId,
    customerName: orderData.value.customerName,
    isDeleted: orderData.value.isDeleted,
    address: orderData.value.address,
    designerId: orderData.value.designerId,
    designerName: setDesignerName(orderData.value),
    designerAvatarUrl: orderData.value.designerAvatarUrl,
    designerPhone: orderData.value.designerPhone,
    measureDate: {
      refreshTime: orderData.value.measureDate.refreshTime,
      isConfirm: orderData.value.measureDate.isConfirm,
      measureTime: formatFullDateTimeWithDay(orderData.value.measureDate.measureTime),
      reserveTimes: orderData.value.measureDate.reserveTimes.map((item, index) => {
        return {
          index: index,
          createTime: item.createTime,
          measureTime: formatFullDateTimeWithDay(item.measureTime),
          isDeleted: item.isDeleted,
          deletedTime: item.deletedTime
        };
      })
    },
    content: {
      houseInfo: orderData.value.content.houseInfo,
      photos: orderData.value.content.photos,
      houseCheck: orderData.value.content.houseCheck,
      floorPlan: orderData.value.content.floorPlan,
      note: orderData.value.content.note,
      constructionRequest: orderData.value.content.constructionRequest,
      waterQuality: orderData.value.content.waterQuality,
      airQuality: orderData.value.content.airQuality,
      noise: orderData.value.content.noise,
      humidity: orderData.value.content.humidity,
      radiation: orderData.value.content.radiation,
    },
    isNextStepUsed: orderData.value.isNextStepUsed,
    chatRoomId: orderData.value.chatRoomId
  };
});

const setDesignerName = (data: CustomerOrderMeasureItem): string => {
  switch (data.status) {
    case MeasureOrderStatusEnum.WaitingUpload:
    case MeasureOrderStatusEnum.SurveyDone:
    case MeasureOrderStatusEnum.SurveyorComing:
      return data.designerName;
    default:
      return '(安排丈量師中)';
  }
};

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      switch (measureDetail.value.status) {
        case MeasureOrderStatusEnum.WaitingSurveyor:
        case MeasureOrderStatusEnum.RefillTime:
          goToAppModalProps.value = { title: '免費語音', content: '目前丈量師尚未安排' };
          goToAppModalRef.value?.openModal();
          break;
        case MeasureOrderStatusEnum.SurveyorComing:
        case MeasureOrderStatusEnum.WaitingUpload:
        case MeasureOrderStatusEnum.SurveyDone:
          appUsePhoneModalRef.value?.openModal();
          break;
      }
      break;
    case contactItemEnum.Meet:
      switch (measureDetail.value.status) {
        case MeasureOrderStatusEnum.WaitingSurveyor:
        case MeasureOrderStatusEnum.RefillTime:
          goToAppModalProps.value = { title: '視訊會議', content: '目前丈量師尚未安排' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          createMeet(measureDetail.value.chatRoomId, false, customerInfoStore.userId);
      }
      break;
    case contactItemEnum.Chat:
      switch (measureDetail.value.status) {
        case MeasureOrderStatusEnum.WaitingSurveyor:
        case MeasureOrderStatusEnum.RefillTime:
          goToAppModalProps.value = { title: '聊天室', content: '目前丈量師尚未安排' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          if (chatHubStore._worker === null) {
            sharedWorkerNotSupportModalRef.value?.openModal();
          } else {
            chatRoomRef.value?.showChatRoom();
          }
      }
      break;
    case contactItemEnum.Note:
      switch (measureDetail.value.status) {
        case MeasureOrderStatusEnum.WaitingSurveyor:
        case MeasureOrderStatusEnum.RefillTime:
          goToAppModalProps.value = { title: '紀錄本', content: '目前丈量師尚未安排' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          noteBookRef.value?.openModal();
      }
  }
};

const topInfoClicked = () => {
  switch (measureDetail.value.status) {
    case MeasureOrderStatusEnum.WaitingSurveyor:
      measureNotAssignModalRef.value?.openModal();
      break;
    case MeasureOrderStatusEnum.SurveyorComing:
      goToAppModalProps.value = { title: '家易App', content: '丈量師已指派' };
      goToAppModalRef.value?.openModal();
      break;
    case MeasureOrderStatusEnum.WaitingUpload:
    case MeasureOrderStatusEnum.SurveyDone:
      break;
  }
};

const bookInfoClicked = () => {
  switch (measureDetail.value.status) {
    case MeasureOrderStatusEnum.WaitingSurveyor:
    case MeasureOrderStatusEnum.SurveyorComing:
      deleteModalRef.value?.openModal();
      break;
  }
};

const handleClick = (title:string) => {
  let content;
  switch (measureDetail.value.status) {
    case MeasureOrderStatusEnum.WaitingSurveyor:
      content = '目前丈量師尚未安排';
      break;
    case MeasureOrderStatusEnum.SurveyorComing:
      content = '目前丈量師尚未丈量';
      break;
    case MeasureOrderStatusEnum.RefillTime:
      content = '目前丈量師尚未安排';
      break;
    default:
      return;
  }
  goToAppModalProps.value = { title, content };
  goToAppModalRef.value?.openModal();
};

const backupDelete = () => {
  if (orderData.value.status === MeasureOrderStatusEnum.RefillTime) {
    deleteModalRef.value?.openModal();
  }
};

const deleteOrder = async () => {
  await CustomerMeasureOrderService.deleteOrder({ orderId: orderId });
  toastInfo('訂單已刪除');
  //回訂單紀錄
  await router.push({
    name: 'customerorderlist'
  });
};

const refillOrderTime = async () => {
  if (!refillTime.value.some(time => time !== '')) {
    toastWarning('請填寫至少一個丈量時間');
    return;
  }
  const result = await CustomerMeasureOrderService.refillOrderTime({
    orderId: orderId,
    measureTimes: refillTime.value
  });
  if (result.status === APIStatusCodeEnum.Success) {
    toastInfo('預約成功');
    refillTime.value = [];
    await getOrderData();
  }
};

const timeAdd = (newTime: Date) => {
  setTomorrowISOString();
  // 檢查新時間跟之前預約的時間是否為同一天 是就不加入
  const newTimeISO = newTime.toISOString();
  const newTimeDate = new Date(newTimeISO);
  const isDuplicate = orderData.value.measureDate.reserveTimes.some((time) => {
    const reserveTimeDate = new Date(time.measureTime);
    return isSameDay(newTimeDate, reserveTimeDate);
  });
  if (isDuplicate) {
    toastWarning('無法選擇已預約過的日期');
    return;
  }

  if (isValidTimeSlot(newTime)) {
    refillTime.value.push(newTime.toISOString());
  } else {
    toastWarning('間隔時間不得少於六小時。');
  }
};

const removeTimeSlot = (index: number) => {
  refillTime.value.splice(index, 1);
};

const handleTimeUpdate = (newTime: Date | null, index: number) => {
  //這是按下時間選擇器的X會觸發的事件 就直接刪除該筆選擇器
  if (newTime === null) {
    removeTimeSlot(index);
    return;
  }

  const isDuplicate = orderData.value.measureDate.reserveTimes.some((time) => {
    const reserveTimeDate = new Date(time.measureTime);
    return isSameDay(newTime, reserveTimeDate);
  });
  if (isDuplicate) {
    removeTimeSlot(index);
    toastWarning('無法選擇已預約過的日期');
    return;
  }

  if (isValidTimeSlot(newTime, index)) {
    refillTime.value[index] = newTime.toISOString();
  } else {
    refillTime.value[index] = '';
    removeTimeSlot(index); // 時間檢查不通過就直接刪除該筆資料
    toastWarning('間隔時間不得少於六小時。');
  }
};

const isValidTimeSlot = (newTime: Date, newIndex?: number): boolean => {
  for (let dataIndex = 0; dataIndex < refillTime.value.length; dataIndex++) {
    // 忽略當前更新的元素和空字串的元素
    if (newIndex !== undefined) {
      if (dataIndex === newIndex || refillTime.value[dataIndex] === '') continue;
    }
    const existingTime = new Date(refillTime.value[dataIndex]);
    const diff = Math.abs(existingTime.getTime() - newTime.getTime());
    const sixHoursInMilliseconds = 6 * 60 * 60 * 1000;
    if (diff < sixHoursInMilliseconds) {
      return false;
    }
  }
  return true;
};

const getOrderData = async () => {
  const response = await CustomerMeasureOrderDetailService.getOneOrderDetail({ orderId: orderId });
  if (response.status !== APIStatusCodeEnum.Success) {
    toastInfo('找不到訂單資料');
    goHome();
  } else {
    orderData.value = response.result;
    if (orderData.value.status === MeasureOrderStatusEnum.SurveyDone) {
      completeModalRef.value?.openModal();
    }
  }
};

const closeCompleteModal = () => {
  completeModalRef.value?.closeModal();
  if (orderData.value.orderRatingId) rateModalRef.value?.openModal();
};


const datePickAdd = ref<DatePickerInstance>(null);
//因為已選擇的時間段可能有多個 所以用陣列 不然觸發事件時會不知道要觸發哪一個DatePickerInstance
const datePickSelected = ref<DatePickerInstance[]>([]);

const selectedEditDate = (index: number) => {
  datePickSelected.value[index]?.selectDate();
};

const addSelectDate = () => {
  datePickAdd.value?.selectDate();
};

const selectedCloseMenu = (index: number) => {
  console.log('selected', datePickSelected.value);
  datePickSelected.value[index]?.closeMenu();
};

const addCloseMenu = () => {
  console.log('add', datePickAdd.value);
  datePickAdd.value?.closeMenu();
};

const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const tomorrowISOString = ref<string>('');

const setTomorrowISOString = () => {
  const tomorrowPreSelect = new Date();
  tomorrowPreSelect.setDate(tomorrowPreSelect.getDate() + 1);
  tomorrowPreSelect.setHours(14, 0, 0, 0); // 設定時間為14:00:00.000
  const offset = tomorrowPreSelect.getTimezoneOffset();
  const correctedTime = new Date(tomorrowPreSelect.getTime() - (offset * 60 * 1000));
  tomorrowISOString.value = correctedTime.toISOString().slice(0, -1);
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

onMounted(async () => {
  if (!customerInfoStore.loginState) {
    goHome();
    return;
  }
  setTomorrowISOString();
  await getOrderData();
});
</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <div class="cus-border text-lg">
      <div class="flex flex-row justify-center ">
        <p class="text-2xl font-bold text-black">丈量進度</p>
      </div>
      <div class="flex p-2 gap-x-4 justify-start items-start max-md:flex-col" @click="topInfoClicked()">
        <img v-if="measureDetail.designerAvatarUrl" class="w-20 h-20 rounded-full"
          :src="measureDetail.designerAvatarUrl" alt="designerAvatar" />
        <UserCircleIcon v-else class="h-24 max-md:h-12 w-24 max-md:w-12 flex-none" />
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class=" font-medium text-nowrap">丈量師</p>
            <p class=" font-bold ">{{ measureDetail.designerName }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-medium text-nowrap">丈量時間</p>
            <p v-if="measureDetail.status === MeasureOrderStatusEnum.SurveyorComing" class=" font-bold">
              {{ measureDetail.measureDate.measureTime }}</p>
            <p v-else-if="measureDetail.status === MeasureOrderStatusEnum.SurveyDone || measureDetail.status === MeasureOrderStatusEnum.WaitingUpload"
              class=" font-bold">
              已到府</p>
            <p v-else-if="measureDetail.status === MeasureOrderStatusEnum.RefillTime" class=" font-bold">
              (需要重新填寫)</p>
            <p v-else class=" font-bold">(安排中)</p>
          </div>
          <div
            v-if="measureDetail.status === MeasureOrderStatusEnum.WaitingUpload || measureDetail.status === MeasureOrderStatusEnum.SurveyDone"
            class="flex gap-3">
            <p class=" font-medium ">狀態進度</p>
            <p v-if="measureDetail.status === MeasureOrderStatusEnum.WaitingUpload" class=" font-bold">資料上傳中</p>
            <p v-else class=" font-bold">資料已上傳</p>
          </div>
          <div v-else class="flex gap-3">
            <p class=" font-medium text-nowrap">服務費用</p>
            <p class=" font-bold">NT$ 2,000 (原價2萬)</p>
          </div>
        </div>
      </div>
      <HezDivider />
      <div class="flex gap-2 justify-evenly">
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
          @click="contactItemClicked(contactItemEnum.Phone)">
          <PhoneIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">免費語音</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
          @click="contactItemClicked(contactItemEnum.Meet)">
          <VideoCameraIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">視訊會議</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
          @click="contactItemClicked(contactItemEnum.Chat)">
          <ChatBubbleLeftEllipsisIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">聊天室</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
          @click="contactItemClicked(contactItemEnum.Note)">
          <ClipboardDocumentListIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">紀錄本</p>
        </div>
      </div>
    </div>

    <div v-if="orderData.chatRoomId && chatHubStore._worker !== null"
      class="fixed flex flex-col justify-end bottom-0 right-0 w-80 z-40" :class="isChatRoomShow ? 'h-3/5' : 'h-auto'">
      <ChatRoom :is-for-meet="false" :room-id="orderData.chatRoomId" :is-designer="false"
        @show-chat-room="changeChatRoomShow" ref="chatRoomRef" />
    </div>

    <div class="flex flex-col cus-border my-1 gap-4" @click="bookInfoClicked()">
      <div class="flex justify-evenly text-2xl items-center" @click="backupDelete()">
        <p class="font-bold text-color-primary text-center">預約資訊</p>
      </div>
      <HezDivider />
      <div class="flex justify-start">
        <div class="flex flex-col">
          <div class="flex justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">屋主姓名</p>
              <p class="text-black font-bold">{{ measureDetail.customerName }}</p>
            </div>
          </div>
          <div class="flex justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">丈量地址</p>
              <p class="text-black font-bold">{{ measureDetail.address.fullName }}</p>
            </div>
          </div>
          <div v-if="orderData.status <= MeasureOrderStatusEnum.WaitingSurveyor"
            class="flex justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">預約時間</p>
              <div class="flex flex-col">
                <div v-for="reserveTime in measureDetail.measureDate.reserveTimes" :key="reserveTime.index"
                  class="flex flex-col">
                  <p v-if="!reserveTime.isDeleted" class="text-black font-bold">{{ reserveTime.measureTime }}</p>
                </div>
                <!--              <p v-else class="font-bold text-red-600">(點此重新填寫)</p>-->
              </div>
            </div>
          </div>
          <div v-if="orderData.status === MeasureOrderStatusEnum.RefillTime"
            class="flex flex-col justify-start text-lg text-color-primary mb-4">
            <p class="font-bold text-nowrap">已預約過的時間</p>
            <div v-for="reserveTime in measureDetail.measureDate.reserveTimes" :key="reserveTime.index">
              <p v-if="reserveTime.isDeleted">{{ reserveTime.measureTime }}</p>
            </div>
          </div>
          <div v-if="orderData.status === MeasureOrderStatusEnum.RefillTime"
            class="flex flex-col space-y-4 justify-start text-lg text-color-primary mb-4">
            <p class="font-bold text-red-600 text-nowrap">請重新預約時間</p>
            <p class="font-bold text-start">可預約時間為9:00~21:30 每組預約時間間隔至少6小時</p>
            <div v-for="(_, index) in refillTime" :key="index" class="flex gap-x-2 items-center">
              <VueDatePicker v-model="refillTime[index]" no-today no-hours-overlay no-minutes-overlay
                :format="DatePickerToFullDateTimeWithDay" :preview-format="TimeToEmptyString" :min-date="tomorrow"
                placeholder="請選擇丈量時間" :enableTimePicker="true" :is24="true" locale="zh-TW" minutes-increment="30"
                :start-time="{ hours: 14, minutes: 0, seconds: 0 }" :min-time="{ hours: 9, minutes: 0, seconds: 0 }"
                :max-time="{ hours: 21, minutes: 30, seconds: 0 }" class="border border-gray-500 p-2 rounded"
                @update:model-value="(newTime: Date | null) => handleTimeUpdate(newTime, index)" ref="datePickSelected">
                <template #action-row>
                  <div class="flex w-full justify-evenly space-x-3">
                    <p class="px-4 py-2 text-center font-bold cursor-pointer w-1/2 cus-border hover:bg-gray-200"
                      @click="selectedCloseMenu(index)">取消</p>
                    <p class="px-4 py-2 text-center font-bold cursor-pointer w-1/2 cus-border hover:bg-gray-200"
                      @click="selectedEditDate(index)">確定</p>
                  </div>
                </template>
                <template #clock-icon>
                  <p class="font-bold text-black p-1.5 bg-color-selected cus-border rounded-md w-full">選擇時間</p>
                </template>
                <template #calendar-icon>
                  <p class="font-bold text-black p-1.5 bg-color-selected cus-border rounded-md w-full">選擇日期</p>
                </template>
              </VueDatePicker>
            </div>
            <VueDatePicker v-if="refillTime.length < 3" v-model="tomorrowISOString" no-today no-hours-overlay
              no-minutes-overlay :format="TimeToEmptyString" :preview-format="TimeToEmptyString" :min-date="tomorrow"
              placeholder="請選擇新的丈量時間" :enableTimePicker="true" :is24="true" locale="zh-TW" minutes-increment="30"
              :start-time="{ hours: 14, minutes: 0, seconds: 0 }" :min-time="{ hours: 9, minutes: 0, seconds: 0 }"
              :max-time="{ hours: 21, minutes: 30, seconds: 0 }" class="border border-gray-500 p-2 rounded"
              @update:model-value="(newTime: Date) => timeAdd(newTime)" ref="datePickAdd">
              <template #action-row>
                <div class="flex w-full justify-evenly space-x-3">
                  <p class="px-4 py-2 text-center font-bold cursor-pointer w-1/2 cus-border hover:bg-gray-200"
                    @click="addCloseMenu">取消</p>
                  <p class="px-4 py-2 text-center font-bold cursor-pointer w-1/2 cus-border hover:bg-gray-200"
                    @click="addSelectDate">確定</p>
                </div>
              </template>
              <template #clock-icon>
                <p class="font-bold text-black p-1.5 bg-color-selected cus-border rounded-md w-full">選擇時間</p>
              </template>
              <template #calendar-icon>
                <p class="font-bold text-black p-1.5 bg-color-selected cus-border rounded-md w-full">選擇日期</p>
              </template>
            </VueDatePicker>
          </div>
        </div>
      </div>
      <div v-if="orderData.status === MeasureOrderStatusEnum.RefillTime" class="flex gap-x-2">
        <!--        <button type="button"-->
        <!--                class="button-basic w-full ring-1  ring-inset ring-gray-300 hover:bg-gray-50"-->
        <!--                @click="deleteOrder()">刪除預約-->
        <!--        </button>-->
        <button type="button" class="button-padding w-full cus-btn" @click="refillOrderTime()">確定預約
        </button>
      </div>
    </div>
    <div @click="handleClick('房屋資訊')">
      <CustomerStep1TextContent title="房屋資訊" :data="measureDetail.content.houseInfo" />
    </div>
    <div @click="handleClick('房屋照片')">
      <CustomerHousePhoto :model-value="measureDetail.content.photos" />
    </div>
    <div @click="handleClick('屋況紀錄(非專業技師)')">
      <CustomerHouseCheck :model-value="measureDetail.content.houseCheck" />
    </div>
    <div @click="handleClick('丈量規劃')">
      <CustomerFloorPlan :model-value="measureDetail.content.floorPlan" />
    </div>
    <div v-if="measureDetail.content.constructionRequest.content.length > 0" @click="handleClick('裝潢需求')">
      <CustomerStep1TextContent title="裝潢需求" :data="measureDetail.content.constructionRequest" />
    </div>
    <div v-if="measureDetail.content.waterQuality.content.length > 0" @click="handleClick('水質檢測')">
      <CustomerStep1TextContent title="水質檢測" :data="measureDetail.content.waterQuality" />
    </div>
    <div v-if="measureDetail.content.airQuality.content.length > 0" @click="handleClick('空氣檢測')">
      <CustomerStep1TextContent title="空氣檢測" :data="measureDetail.content.airQuality" />
    </div>
    <div v-if="measureDetail.content.noise.content.length > 0" @click="handleClick('噪音檢測')">
      <CustomerStep1TextContent title="噪音檢測" subTitle="(安靜:50dB，吵雜:60dB)" :data="measureDetail.content.noise" />
    </div>
    <div v-if="measureDetail.content.humidity.content.length > 0" @click="handleClick('濕度檢測')">
      <CustomerStep1TextContent title="濕度檢測" subTitle="(正常:40~60%，潮濕:70%)" :data="measureDetail.content.humidity" />
    </div>
    <div v-if="measureDetail.content.radiation.content.length > 0" @click="handleClick('電磁輻射')">
      <CustomerStep1TextContent title="電磁輻射" :data="measureDetail.content.radiation" />
    </div>
    <div @click="handleClick('房屋/屋主備註')">
      <CustomerHouseNote :model-value="measureDetail.content.note" />
    </div>
  </div>

  <DefaultModal title="家易App" :show-close-button="true" :click-outside-close="true" ref="measureNotAssignModalRef"
    @closeModal="measureNotAssignModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">因網頁無法自動刷新</p>
        <p class="font-bold md:text-lg">如果要確認丈量師是否即時被指派</p>
        <p class="font-bold md:text-lg">建議手動刷新，或是下載App後操作</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <DefaultModal title="刪除訂單" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
    ref="deleteModalRef" @closeModal="deleteModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p v-if="orderData.status === MeasureOrderStatusEnum.SurveyorComing" class="font-bold md:text-lg">
          目前已指派丈量師</p>
        <p class="font-bold md:text-lg">確認刪除此訂單?</p>
        <p class="font-bold md:text-lg">
          若超過三次會被封鎖帳號!</p>
        <button type="button"
          class="button-basic w-full mt-2 ring-1 bg-red-300 ring-inset ring-gray-300 hover:bg-red-400"
          @click="deleteOrder()">確認刪除訂單
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="Step1 丈量訂單已完成" :show-close-button="true" :click-outside-close="true" ref="completeModalRef"
    @close-modal="closeCompleteModal()">
    <div class="flex flex-col items-center text-black">
      <img src="/vectors/customerStep/step1.svg" alt="Step1" class="w-32 h-32 md:w-40 md:h-40 mb-2">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">請下載家易App</p>
        <p class="font-bold md:text-lg">刊登室內設計比價</p>
        <p class="font-bold md:text-lg">為您即時觸發更多設計師</p>
        <p class="font-bold md:text-lg">提供優惠的報價</p>
        <p class="font-bold md:text-lg">設計師會根據此丈量資訊來設計</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <GoToAppModal :title="goToAppModalProps.title" :content="goToAppModalProps.content" ref="goToAppModalRef" />
  <AppUsePhoneModal :user-type="UserTypeEnum.Customer" ref="appUsePhoneModalRef" />
  <NoteBook :room-id="orderData.chatRoomId" :is-designer="false" ref="noteBookRef" />
  <AndroidNotSupportSharedWorkerModal :is-designer="false" ref="sharedWorkerNotSupportModalRef" />
  <CustomerRateModal :order-type="OrderTypeEnum.Measure" :deisgner-name="orderData.designerName"
    :deisgner-avatar="orderData.designerAvatarUrl" :order-rating-id="orderData.orderRatingId" ref="rateModalRef" />
</template>
