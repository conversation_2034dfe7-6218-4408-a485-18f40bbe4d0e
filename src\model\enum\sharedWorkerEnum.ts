export enum WorkerEventEnum {
  /**
   *   分頁關閉事件
   */
  WindowClose = 'windowUnload',

  /**
   *   接收SignalR傳過來的事件
   */
  OnSignalR = 'signalr/message/on',

  /**
   *   傳送事件給SignalR
   */
  InvokeToSignalR = 'signalr/message/invoke',

  /**
   *   傳送'連線'事件給SignalR
   */
  InvokeConnectToSignalR = 'signalr/connect',

  /**
   *   SignalR連線錯誤
   */
  SignalRConnectionError = 'signalr/error',

  /**
   *   設定SiganlR驗證為false
   */
  SetSignalRAuthFalse = 'signalr/auth/false',

  /**
   *   同步自己透過Api發送的訊息
   */
  SyncSelfSentMsg = 'sync/self/msg',

  /**
   *   同步自己透過Api收回的訊息
   */
  SyncSelfUnsendMsg = 'sync/self/unsend/msg',
}

export enum OnSignalRMethodEnum {
  /**
   *   測試
   */
  Test = 'test',

  /**
   *   連線結果
   */
  Connect = 'connect',

  /**
   *   接收'訊息傳入'事件
   */
  Message = 'message',

  /**
   *   接收'同步聊天室'事件
   */
  Room = 'room',

  /**
   *   接收'已讀'事件
   */
  MessageRead = 'message/read',

  /**
   *   接收'收回'事件
   */
  MessageUnsend = 'message/unsend',

  /**
   *   同步自己透過Api發送的訊息
   */
  SyncSelfSentMsg = 'sync/self/msg',
}

export enum InvokeSignalRMethodEnum {
  /**
   *   測試
   */
  Test = 'test',

  /**
   *   發送連線驗證
   */
  Connect = 'connect',

  /**
   *   發送已讀事件
   */
  ReadMessage = 'message/read',
}

