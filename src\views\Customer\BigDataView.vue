<script setup lang="ts">

import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';
import { bigData } from '@/model/general/bigDataItem.ts';
import { BigDataService } from '@/api/bigData.ts';
import { toastWarning } from '@/utils/toastification.ts';
import { budgetCombineLowToHigh } from '@/utils/budgetFormat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { formatFloat, formatInteger } from '@/utils/numberFormat.ts';

const bigDataResultModal = ref<InstanceType<typeof DefaultModal> | null>(null);
const bigDataAreaPing = ref<string>('');
const bigDataBathrooms = ref<string>('');
const bigDataBudget = ref<string>('');

const areaPinaRange = {
  min: 0,
  max: 9999
};

const bathroomsRange = {
  min: 0,
  max: 100
};

const cleanAreaPinaInput = () => {
  bigDataAreaPing.value = formatFloat(bigDataAreaPing.value); //清理的方法
  if (parseFloat(bigDataAreaPing.value) > areaPinaRange.max) bigDataAreaPing.value = '9999';
};

const cleanBathroomsInput = () => {
  bigDataBathrooms.value = formatInteger(bigDataBathrooms.value); //清理的方法
  if (parseInt(bigDataBathrooms.value) > bathroomsRange.max) bigDataBathrooms.value = '100';
};

const closeBigDataModal = () => {
  bigDataResultModal.value?.closeModal();
};

const bigDataForm = ref(bigData.map(e => ({
  selectValue: e.label === '施工地區' ? 0 : e.options.length - 1,
  ...e
})));

const getEstimate = async () => {
  if (parseFloat(bigDataAreaPing.value) <= 0) {
    toastWarning('請輸入裝潢坪數');
    return;
  } else if (parseInt(bigDataBathrooms.value) < 0) {
    toastWarning('請輸入衛浴間數');
    return;
  }
  // 處理表單提交邏輯
  console.log(bigDataForm.value);
  const response = await BigDataService.BigDataEstimate({
    orderId: '',
    areaPing: parseFloat(bigDataAreaPing.value),
    bathroomCount: parseInt(bigDataBathrooms.value),
    constructionArea: bigDataForm.value[0].selectValue,
    demolitionProject: bigDataForm.value[1].selectValue,
    drainagePipeline: bigDataForm.value[2].selectValue,
    bathroomFacilities: bigDataForm.value[3].selectValue,
    powerPipeline: bigDataForm.value[4].selectValue,
    partitionProject: bigDataForm.value[5].selectValue,
    windowCurtain: bigDataForm.value[6].selectValue,
    doorSet: bigDataForm.value[7].selectValue,
    tiles: bigDataForm.value[8].selectValue,
    ceiling: bigDataForm.value[9].selectValue,
    paintLevel: bigDataForm.value[10].selectValue,
    livingRoomFloor: bigDataForm.value[11].selectValue,
    kitchenCabinet: bigDataForm.value[12].selectValue,
    livingRoomCabinet: bigDataForm.value[13].selectValue,
    wardrobe: bigDataForm.value[14].selectValue
  });
  if (response.status === APIStatusCodeEnum.Success) {
    bigDataBudget.value = budgetCombineLowToHigh(response.result);
    bigDataResultModal.value?.openModal();
  }
};

</script>

<template>
  <h2 class="text-center md:text-2xl text-lg font-bold mt-8">AI大數據自動估價系統</h2>
  <div class="w-full cus-border my-8">
    <div class="flex flex-col gap-4 mb-4">
      <div class="flex flex-col gap-4 justify-start">
        <span class="">裝潢坪數</span>
        <div class="flex flex-row w-full md:w-auto items-center">
          <input type="text" inputmode="decimal"
                 v-model="bigDataAreaPing"
                 class="p-2 w-full cus-border focus-border-color"
                 @change="cleanAreaPinaInput()"
                 placeholder="請輸入坪數">
          <span class="ml-2">坪</span>
        </div>
      </div>
      <div class="flex flex-col gap-4 justify-start">
        <span class="">衛浴間數</span>
        <div class="flex flex-row w-full md:w-auto items-center">
          <input type="text" inputmode="numeric"
                 v-model="bigDataBathrooms"
                 class="p-2 w-full cus-border focus-border-color"
                 @change="cleanBathroomsInput()"
                 placeholder="請輸入衛浴間數">
          <span class="ml-2">間</span>
        </div>
      </div>
    </div>
    <div class="flex flex-col justify-start gap-4">
      <div v-for="(item, index) in bigDataForm" :key="index" class="flex flex-col gap-4 items-start">
        <span class="w-full">{{ item.label }}</span>
        <select v-model="item.selectValue" class=" p-2 w-full cus-border focus-border-color">
          <option v-for="(choice, index) in item.options" :key="index" :value="choice.value">{{ choice.name }}
          </option>
        </select>
      </div>
    </div>
  </div>
  <button class="px-8 py-4 cus-btn mb-8" @click="getEstimate()">取得計算結果</button>

  <DefaultModal title="大數據估算結果" :click-outside-close="true" :show-close-button="true" modal-width="max-w-lg"
                @closeModal="closeBigDataModal()" ref="bigDataResultModal">
    <div class="p-4">
      <div class="mb-4">
        <h1 class="text-xl font-bold">施工坪數：<span>{{ bigDataAreaPing }}</span>坪</h1>
        <p class="text-lg">裝潢總價：<span>{{ bigDataBudget }}</span></p>
      </div>
      <div class="space-y-4">
        <div>
          <h2 class="text-lg font-semibold">拆除工程</h2>
          <ul class="list-disc list-inside">
            <li>拆除清運：2,000元~4,000元/坪</li>
          </ul>
        </div>
        <div>
          <h2 class="text-lg font-semibold">隔間工程</h2>
          <ul class="list-disc list-inside">
            <li>輕鋼架水泥牆：5,000元~6,000元/坪</li>
            <li>輕鋼架隔音牆：4,000元~6,000元/坪</li>
            <li>磚牆隔間：6,000元~9,000元/坪</li>
            <li>木作隔間：3,000元~6,000元/坪</li>
          </ul>
        </div>
        <div>
          <h2 class="text-lg font-semibold">磁磚</h2>
          <ul class="list-disc list-inside">
            <li>石質磁磚：5,000元~12,000元/坪</li>
            <li>馬賽克磚：4,000元~30,000元/坪</li>
            <li>木紋磁磚：2,000元~10,000元/坪</li>
            <li>木質地板：1,000元~10,000元/坪</li>
            <li>拋光石英磚：1,000元~20,000元/坪</li>
          </ul>
        </div>
        <div>
          <h2 class="text-lg font-semibold">窗戶窗簾</h2>
          <ul class="list-disc list-inside">
            <li>電動窗簾：10,000元~30,000元/組</li>
            <li>手動窗簾：5,000元~10,000元/組</li>
          </ul>
        </div>
        <div>
          <h2 class="text-lg font-semibold">衛浴設備</h2>
          <ul class="list-disc list-inside">
            <li>免治馬桶：5,000元~80,000元/個</li>
            <li>恆溫出水：3,000元~70,000元/組</li>
          </ul>
        </div>
      </div>
    </div>
  </DefaultModal>
</template>
