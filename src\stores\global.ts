import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';

export const useMeetTokenLogStore = defineStore('MeetTokenLog', () => {
    const meetTokenLog = ref<{
      meetToken: string;
      userId: string;
      userType: UserTypeEnum;
    }[]>([]);

    const updateMeetTokenLog = (meetToken: string, userId: string, userType: UserTypeEnum) => {
      meetTokenLog.value.push({
        meetToken,
        userId,
        userType
      });
    };
    return { meetTokenLog, updateMeetTokenLog };
  },
  {
    persist: true
  }
);

export const useChatHubStore = defineStore('chatHub', () => {
    const _worker = ref<SharedWorker | null>(null);

    const worker = computed((): SharedWorker => {
      if (_worker.value === null) {
        throw new Error('Shared Worker not initialized');
      }
      return _worker.value;
    });

    const setWorker = (worker: SharedWorker) => {
      _worker.value = worker;
    };

    return { _worker, worker, setWorker };
  }
);
