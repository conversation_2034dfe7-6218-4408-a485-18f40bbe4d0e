<script setup lang="ts">
import { ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ImagePdfCadKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import Upload2DModalContent from '@/components/Order/OrderDetail/UpdateModal/ModalContent/Upload2DModalContent.vue';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps<{ title: string; orderId: string; }>();
const design2D = defineModel<ImagePdfCadKeyContent>('design2D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus', { required: true });
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus', { required: true });
const isApiLoading = ref(false);

const emits = defineEmits(['refreshData']);
const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  if (isApiLoading.value) {
    toastInfo('請等待檔案上傳完成');
  } else {
    updateModalRef.value?.closeModal();
  }
};

const changeApiLoading = (isLoading: boolean) => {
  isApiLoading.value = isLoading;
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" ref="updateModalRef"
                :click-outside-close="false"
                :showCloseButton="true"
                @closeModal="closeModal">
    <Upload2DModalContent :order-id="props.orderId"
                          v-model:design2-d="design2D"
                          v-model:sub-status="subStatus"
                          v-model:order-status="orderStatus"
                          @refresh-data="emits('refreshData')"
                          @change-api-loading="changeApiLoading" />
  </DefaultModal>
</template>
