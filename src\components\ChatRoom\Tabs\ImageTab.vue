<script setup lang="ts">

import { computed, onMounted, ref } from 'vue';
import { GetImageListResponse, ImageRecordItem } from '@/model/response/noteResponse.ts';
import { NoteListService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import ImageModal from '@/components/ChatRoom/ImageModal.vue';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const data = ref<ImageRecordItem[]>([]);
const skip = ref<number>(0);
const imageRef = ref<InstanceType<typeof ImageModal> | null>(null);
const isFirstTime = ref<boolean>(true);
const isMoreData = ref<boolean>(true);

const getImageList = async () => {
  let res: GetImageListResponse;
  if (props.isDesigner) {
    res = await NoteListService.GetImageListByDesigner(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  } else {
    res = await NoteListService.GetImageListByCustomer(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    if (res.images.length < 20) {
      isMoreData.value = false;
    }

    if (res.images.length === 0) {
      if (!isFirstTime.value) toastInfo('已無更多資料');
      return;
    } else {
      data.value.push(...res.images);
      skip.value += 20;
    }
  }
};

const openImageModal = (url: string) => {
  imageRef.value?.openModal(url);
};

const showData = computed(() => {
  return data.value.reduce((acc, item) => {
    // 將 createTime 轉換為當地時間的日期字串
    const date = new Date(item.createTime).toLocaleDateString();

    // 如果 acc 中不存在該日期，則初始化為空陣列
    if (!acc[date]) {
      acc[date] = [];
    }

    // 將 item 添加到對應日期的陣列中
    acc[date].push(item);

    return acc;
  }, {} as Record<string, ImageRecordItem[]>);
});


onMounted(async () => {
  await getImageList();
  isFirstTime.value = false;
});
</script>

<template>
  <div class="w-full text-black" v-if="data.length === 0">
    <p>尚無任何照片</p>
  </div>
  <div class="w-full flex flex-col gap-y-1" v-else>
    <div v-for="(images,date) in showData" :key="date" class="w-full">
      <p class="text-start mb-2 font-bold">{{ date }}</p>
      <div class="flex flex-wrap">
        <div v-for="image in images" :key="image.imageUrl" class="">
          <img :src="image.imageUrl" alt="image" class="w-20 h-20 object-cover cursor-pointer"
               @click="openImageModal(image.imageUrl)" />
        </div>
      </div>
    </div>
    <p class="cursor-pointer" v-if="isMoreData" @click="getImageList()">查看更多</p>
  </div>

  <ImageModal ref="imageRef" />
</template>
