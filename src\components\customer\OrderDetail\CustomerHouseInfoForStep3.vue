<script setup lang="ts">
import { CustomerTextContent } from '@/model/response/customerMeasureOrderResponse.ts';
import { computed, ref } from 'vue';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';
import <PERSON>z<PERSON>ivider from '@/components/General/HezDivider.vue';
import { budgetCombineLowToHigh } from '@/utils/budgetFormat.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { CustomerConstructionPublishInfo } from '@/model/response/customerConstructionOrderResponse.ts';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const houseInfo = defineModel<CustomerTextContent>('houseInfo', { required: true });
const publishInfo = defineModel<CustomerConstructionPublishInfo>('publishInfo', { required: true });
const nameAddress = defineModel<{
  name: string,
  address: string
}>('nameAddress', { required: true });
const houseInfoData = computed(() => {
  let infoData: {
    updateTime: string,
    name: string,
    text: { content: { hint: string, text: string, unit?: string }, mode: string }
  }[] = [];
  houseInfo.value.content.forEach((item) => {
    const JsonStringContent = JSONStringToObject(item.text);
    infoData.push({
      updateTime: item.updateTime,
      name: item.name,
      text: {
        content: {
          hint: JsonStringContent.content.hint,
          text: JsonStringContent.content.text,
          ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
        },
        mode: JsonStringContent.mode
      }
    });
  });
  return infoData;
});

const publishInfoData = computed(() => {
  return {
    usage: publishInfo.value.spaceUsage,
    style: publishInfo.value.designStyle,
    theme: publishInfo.value.designTheme,
    constructionBudget: budgetCombineLowToHigh(publishInfo.value.constructionBudget),
    keyDesignDetail: publishInfo.value.keyDesignDetail,
    constructionNote: publishInfo.value.constructionNote, //未接續原設計師用這個
    referenceImages: publishInfo.value.referenceImages,
  };
});
</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">房屋資訊</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div class="flex flex-col">
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">屋主姓名</p>
            <p class="text-black font-bold">{{ nameAddress.name }}</p>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">裝潢地址</p>
            <p class="text-black font-bold">{{ nameAddress.address }}</p>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">裝潢預算</p>
            <p class="text-black font-bold">{{ publishInfoData.constructionBudget }}</p>
          </div>
        </div>
        <!--        houseInfo部分-->
        <div v-for="(infoData,index) in houseInfoData" :key="index"
             class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold">{{ infoData.name }}</p>
            <div v-if="infoData.text.content.text === '' " class="flex">
              <p class="text-black">(丈量師未上傳資料)</p>
            </div>
            <div v-else class="flex">
              <p class="text-black font-bold">{{ infoData.text.content.text }}</p>
              <p class="text-black font-bold">{{ infoData.text.content.unit }}</p>
            </div>
          </div>
        </div>

        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">空間用途</p>
            <p class="text-black font-bold">{{ publishInfoData.usage }}</p>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">設計風格</p>
            <p class="text-black font-bold">{{ publishInfoData.style }}</p>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">設計主題</p>
            <p class="text-black font-bold">{{ publishInfoData.theme }}</p>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold text-nowrap">設計參考</p>
            <div v-if="publishInfoData.referenceImages.length === 0" class="flex">
              <p class="text-black font-bold">未上傳</p>
            </div>
            <div v-else class="flex">
              <button class="button-padding cus-btn text-base"
                      @click="modalRef?.openModal()"> 查看圖片
              </button>
            </div>
          </div>
        </div>
        <div class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5">
            <p class="font-bold text-nowrap">備註細節</p>
            <p v-if="!publishInfoData.constructionNote" class="text-black font-bold">未上傳</p>
            <p v-else class="text-black font-bold whitespace-pre-line">
              <span >{{ publishInfoData.constructionNote }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DefaultModal title="設計參考" :show-close-button="true" :click-outside-close="true" ref="modalRef"
                @closeModal="modalRef?.closeModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true">
          <swiper-slide v-for="(media,index) in [...publishInfoData.referenceImages].reverse()" :key="index"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ formatFullDateTimeWithDay(media.updateTime) }}</p>
            <img :src="media.url" alt="作品集"
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>
</template>
