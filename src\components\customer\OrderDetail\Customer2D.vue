<script setup lang="ts">
import { ref } from 'vue';
import { downloadFile } from '@/utils/fileDownloader.ts';
import { CustomerImagePdfCadContent, CustomerImagePdfCadItem } from '@/model/response/customerMeasureOrderResponse.ts';
import CustomerPDFCADImageMediaModal from '@/components/customer/CustomerPDFCADImageMediaModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';

const design2D = defineModel<CustomerImagePdfCadContent>({ required: true });
const customerPDFCADImageMediaModalModalRef = ref<InstanceType<typeof CustomerPDFCADImageMediaModal> | null>(null);

const openMediaModal = (imagePdfCadItem: CustomerImagePdfCadItem) => {
  customerPDFCADImageMediaModalModalRef.value?.openMediaModal(imagePdfCadItem);
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">2D室內設計</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div v-if="design2D.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col gap-y-2" v-else>
        <div v-for="(ImagePdfCadItem,index) in design2D.content" :key="index"
             class="flex justify-start text-lg text-color-primary">
          <div class="flex md:gap-2 gap-1 items-center max-md:flex-wrap max-md:items-start">
            <p class="font-bold p-2">{{ ImagePdfCadItem.name }}</p>
            <div class="flex max-md:flex-col gap-1">
              <div>
                <div v-if="ImagePdfCadItem.images.length === 0" class="flex">
                  <p class="font-bold text-black">(設計師未上傳資料)</p>
                </div>
                <div v-else class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="openMediaModal(ImagePdfCadItem)">
                    查看圖片
                  </button>
                </div>
              </div>
              <div v-if="ImagePdfCadItem.images.length !== 0">
                <div v-if="ImagePdfCadItem.pdf.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(ImagePdfCadItem.pdf.url)">
                    下載 PDF
                  </button>
                </div>
              </div>
              <div v-if="ImagePdfCadItem.images.length !== 0">
                <div v-if="ImagePdfCadItem.cad.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(ImagePdfCadItem.cad.url)">
                    下載 CAD
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <CustomerPDFCADImageMediaModal ref="customerPDFCADImageMediaModalModalRef" />
</template>
