<script setup lang="ts">
import DesignerAppDownload from '@/components/General/DesignerAppDownload.vue';
import PattentList from '@/components/General/PattentList.vue';
import LogoutButton from '@/components/General/LogoutButton.vue';
</script>

<template>
  <div class="flex flex-col space-y-8 my-8">

    <div class="cus-border flex-col flex gap-2 space-y-8 md:text-lg">
      <div class="flex-col flex gap-2 font-bold">
        <p>設計師免費接單APP下載</p>
        <div class="shadow-lg rounded-lg">
          <DesignerAppDownload />
        </div>
      </div>

      <div class="flex gap-2 items-center">
        <!--        <img src="/designerHezLogo.svg" alt="" class="w-20 h-20 shadow-md rounded-full">-->
        <p class="font-bold">設計師端APP介紹</p>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerApp/get_Order.png" alt=""
             class="md:w-1/2 max-h-[600px]">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>手機隨時查看接收</p>
          <p>客戶室內設計訂單</p>
          <p>免費接單提供報價</p>
        </div>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerApp/contract.png" alt=""
             class="md:w-1/2 max-h-[600px]">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>客戶匯款資訊合約簽署</p>
          <p>客戶匯款後再開始工作</p>
          <p>有效避免做白工的情況</p>
        </div>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerApp/upload_file_app.png" alt=""
             class="md:w-1/2 max-h-[600px]">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>上傳2D和3D設計成果</p>
          <p>即時有效和業主溝通</p>
        </div>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerApp/step3_work_flow.png" alt=""
             class="md:w-1/2 max-h-[600px]">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>量化裝潢施工驗收流程</p>
          <p>履約保證立即收工程款</p>
        </div>
      </div>
    </div>

    <div class="cus-border flex flex-col gap-2 space-y-8 md:text-lg">
      <div class="flex gap-2 items-center">
        <!--        <img src="/designerHezLogo.svg" alt="" class="w-20 h-20 shadow-md rounded-full">-->
        <p class="font-bold">設計師端網頁介紹</p>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerWeb/designer_portfolio.png" alt=""
             class="md:w-1/2 max-h-[500px] rounded-lg">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>設計師的作品專屬網頁</p>
          <p>客戶快速了解您的設計</p>
        </div>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerWeb/liveKit.png" alt=""
             class="md:w-1/2 max-h-[500px] rounded-lg">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>視訊會議和桌面分享</p>
          <p>讓設計作品有效溝通</p>
        </div>
      </div>
      <div class="shadow-lg p-2 rounded-lg flex items-center max-md:flex-col gap-2 md:text-lg">
        <img src="/appDownload/designerWeb/upload_file.png" alt=""
             class="md:w-1/2 max-h-[500px] rounded-lg">
        <div class="md:w-1/2 text-start flex flex-col font-bold md:text-xl md:ml-4">
          <p>上傳室內設計工作檔案</p>
          <p>包含CAD、圖片、PDF</p>
          <p>即時交付設計作品給業主</p>
        </div>
      </div>
    </div>

    <div class="cus-border flex-col flex gap-2 space-y-8 md:text-lg">
      <div class="flex flex-col gap-2 items-start">
        <p class="md:text-xl font-bold mb-2">客服中心</p>
        <p>聯絡郵件：<EMAIL></p>
        <p>聯絡電話：(02)2771-2171分機 2232</p>
        <p>聯絡地址：台北市大安區忠孝東路三段1號綜合科館406室</p>
      </div>
    </div>

    <div class="cus-border flex-col flex gap-2 space-y-8 md:text-lg">
      <PattentList />
    </div>

    <LogoutButton :isDesigner="true" />
    <a href="/designer/terms"
       class="text-sm text-gray-600 cursor-pointer hover:underline">服務條款</a>
  </div>
</template>
