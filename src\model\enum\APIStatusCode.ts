export enum APIStatusCodeEnum {
  /**
   * 成功
   */
  Success = 0,

  /**
   * 無效的Token
   */
  TokenIllegal = 1,

  /**
   * 資料或格式錯誤
   */
  DataOrFormatError = 2,

  /**
   * 取得網路資源錯誤
   */
  NetworkError = 3,

  /**
   * 找不到物件
   */
  ObjectNotFound = 4,

  /**
   * 無效的用戶類型
   */
  UserTypeIllegal = 5,

  /**
   * 物件已存在
   */
  ObjectAlreadyExist = 6,

  /**
   * 無效的操作
   */
  OperationIllegal = 7,

  /**
   * 電話格式錯誤
   */
  PhoneFormatError = 100,

  /**
   * 驗證信發送次數過多
   */
  PhoneVerifySendExceeded = 102,

  /**
   * 查無電話驗證
   */
  PhoneNotFound = 103,

  /**
   * 電話驗證失敗
   */
  PhoneVerifyFail = 104,

  /**
   * 電話驗證已失效
   */
  PhoneVerifyExpired = 105,

  /**
   * 電話或密碼錯誤
   */
  PhoneOrPasswordError = 106,

  /**
   * Email格式錯誤
   */
  EmailFormatError = 200,

  /**
   * Email發送次數過多
   */
  EmailVerifySendExceeded = 202,

  /**
   * 查無Email驗證
   */
  EmailNotFound = 203,

  /**
   * Email驗證失敗
   */
  EmailVerifyFail = 204,

  /**
   * Email驗證已失效
   */
  EmailVerifyExpired = 205,

  /**
   * 與伺服器連線失敗
   */
  ServerConnectFail = 300,

  /**
   * 聊天室不存在
   */
  RoomNotFound = 301,

  /**
   * 訊息不存在
   */
  MessageNotFound = 302,

  /**
   * 會議人數至少2人以上
   */
  RoomMemberAtLeastTwo = 303,

  /**
   * 自己忙碌中
   */
  SelfBusy = 304,

  /**
   * 對方忙碌中
   */
  OpponentBusy = 305,

  /**
   * 通話不存在
   */
  CallNotFound = 306,

  /**
   * 會議已存在
   */
  MeetingAlreadyExist = 307,

  /**
   * 記事本不存在
   */
  NoteNotFound = 308,

  /**
   * 連線未初始化
   */
  ConnectionNotInitialized = 309,

  /**
   * 查無重定向的目標
   */
  RedirectTargetNotFound = 400,

  /**
   * 此網址已被使用
   */
  UrlAlreadyUsed = 401,

  /**
   * 查無使用者資料
   */
  UserNotFound = 500,

  /**
   * 身分尚未驗證
   */
  IdentityNotVerified = 501,

  /**
   * 不可操作的狀態
   */
  UserStatusError = 502,

  /**
   * 無法重複發送好友邀請或好友已存在
   */
  FriendAlreadyExist = 503,

  /**
   * 用戶已被封鎖
   */
  UserIsBlocked = 504,

  /**
   * 訂單進行中無法刪除帳號
   */
  OrderInProgressCannotDelete = 505,

  /**
   * 客戶發單權限已關閉
   */
  CustomerOrderDisabled = 600,

  /**
   * 匯款資訊尚未設定
   */
  BankAccountNotSet = 700,

  /**
   * 公司資訊尚未驗證
   */
  CompanyInfoNotVerified = 701,

  /**
   * 設計師接單權限已關閉
   */
  DesignerOrderDisabled = 702,

  /**
   * 帳號或密碼錯誤
   */
  AccountOrPasswordError = 800,

  /**
   * 不支援此影片
   */
  VideoNotSupport = 801,

  /**
   * 影片不存在
   */
  VideoNotFound = 802,

  /**
   * 找不到訂單資料
   */
  OrderNotFound = 1000,

  /**
   * 此訂單無法被刪除
   */
  OrderCannotDelete = 1001,

  /**
   * 訂單已完成
   */
  OrderIsFinished = 1002,

  /**
   * 此訂單已被客戶或設計師拒接
   */
  OrderIsRejected = 1003,

  /**
   * 訂單已被使用過
   */
  OrderAlreadyUsed = 1004,

  /**
   * 訂單已完成匯款
   */
  OrderIsRemitted = 1005,

  /**
   * 不可操作的訂單狀態
   */
  OrderStatusError = 1006,

  /**
   * 觸發過於頻繁
   */
  TriggerTooFrequent = 1007,

  /**
   * 訂單還不能夠完成
   */
  OrderContentNotFinish = 1008,

  /**
   * 匯款金額超過訂單金額
   */
  RemittanceAmountExceed = 1009,

  /**
   * 訂單驗收中無法刪除資料
   */
  OrderAcceptanceInProgressCannotDelete = 1010,

  /**
   * 訂單驗收完成無法刪除資料
   */
  OrderAlreadyAcceptedCannotDelete = 1011,

  /**
   * step功能已關閉
   */
  StepFeatureClosed = 1012,

  /**
   * 無丈量師身分
   */
  UserNotSurveyor = 1100,

  /**
   * 無效的丈量時間
   */
  MeasureTimeIllegal = 1101,

  /**
   * 無設計師身分
   */
  UserNotDesigner = 1200,

  /**
   * 無裝修師身分
   */
  UserNotDecorator = 1300,

  /**
   * 設計訂單無裝潢報價
   */
  NoConstructionEstimate = 1301,

  /**
   * 設計師無裝修團隊
   */
  DesignerNotConstructionTeam = 1302,

  /**
   * 設計師無報價無法接續
   */
  DesignerNoQuoteCanNotContinue = 1303,

  /**
   * 發票已開立完成或進行中
   */
  OrderAlreadyIssued = 1400,

  /**
   * 找不到地點資料
   */
  PlaceNotFound = 2000,

  /**
   * Client中斷HTTP請求
   */
  ClientAbortHttp = -2,

  /**
   * 未知的錯誤
   */
  Unknown = -1
}
