<script setup lang="ts">

</script>

<template>
  <div class="flex justify-center p-2 rounded-lg">
    <div class="flex flex-col gap-y-4 md:m-3">
      <div class="flex max-md:flex-col mt-1 justify-center items-center text-color-secondary gap-4">
        <div>
          <a href="https://play.google.com/store/apps/details?id=lab.homeeasy"
             target="_blank">
            <img src="/vectors/general/AndroidDownload.svg" alt="Android download" class="hover-zoom w-60" />
          </a>
        </div>
        <div>
          <a href="https://apps.apple.com/us/app/%E5%AE%B6%E6%98%93-homeeasy-%E8%A3%9D%E6%BD%A2%E6%96%BD%E5%B7%A5%E6%AF%94%E5%83%B9%E5%B9%B3%E5%8F%B0/id6477271187"
             target="_blank">
            <img src="/vectors/general/IOSDownload.svg" alt="IOS download" class="hover-zoom w-60" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
