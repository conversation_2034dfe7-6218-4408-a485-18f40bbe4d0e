<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useCustomerInfoStore, customerStep1PublishStore } from '@/stores/customerGlobal.ts';
import { SecurityService } from '@/api/security.ts';
import { CustomerMeasureOrderDetailService } from '@/api/customerOrder.ts';
import { APIStatusCodeEnum, PhoneVerifyStatusEnum } from '@/enums/api.ts';
import { phoneNumberValid, passwordValid, hashPassword } from '@/utils/validation.ts';
import { toastError, toastSuccess } from '@/utils/toast.ts';
import type { RegisterData, LoginData } from '@/types/auth.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

// Props 定義登入頁面的配置
interface LoginConfig {
  mode: 'simple' | 'full'; // 簡化版或完整版
  redirectRoute?: string; // 登入成功後的重定向路由
  showOrderMessage?: boolean; // 是否顯示訂單相關訊息
}

const props = withDefaults(defineProps<LoginConfig>(), {
  mode: 'full',
  redirectRoute: 'customerhome',
  showOrderMessage: false
});

// 響應式數據
const phone = ref<string>('');
const verifyCode = ref<string>('');
const phoneVerifyStatus = ref<PhoneVerifyStatusEnum>(PhoneVerifyStatusEnum.Unverified);
const registerInput = ref<RegisterData>({
  phoneVerifyId: '',
  password: '',
  rePassword: '',
  agreeTerm: false
});
const sentCodeCountDown = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const showPassword = ref(false);
const showRePassword = ref(false);
const isNowDoingLogin = ref(false);

const loginInput = ref<LoginData>({
  phone: '',
  password: '',
  rememberForShow: true
});
const multiAddressModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const showLoginPassword = ref(false);

const route = useRoute();
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const step1Data = customerStep1PublishStore();

// 根據模式決定標題和訊息
const getPageTitle = () => {
  if (props.showOrderMessage) {
    return '您的訂單已經成功送出';
  }
  return '為了確保到府服務的真實性以及方便聯絡';
};

const getPageSubtitle = () => {
  return '請驗證您的手機號碼及設定密碼';
};

// 發送驗證碼
const sendCode = async (phoneNumber: string) => {
  const validResult = phoneNumberValid(phoneNumber);
  if (!validResult) {
    toastError('請輸入正確的手機號碼');
    return;
  }
  
  const sendVerifyCodeResult = await SecurityService.sendVerifyCodeByCustomer({ phone: phoneNumber });
  if (sendVerifyCodeResult.status === APIStatusCodeEnum.Success) {
    toastSuccess('驗證碼已傳送');
    phoneVerifyStatus.value = PhoneVerifyStatusEnum.VerifyCodeSent;
    startCountdown();
  } else {
    if (sendVerifyCodeResult.status === APIStatusCodeEnum.PhoneVerifySendExceeded) {
      toastError('驗證碼傳送次數過多');
      return;
    }
    toastError('驗證碼傳送失敗');
  }
};

// 驗證手機號碼
const verifyPhone = async () => {
  const verifyResult = await SecurityService.verifyCodeByCustomer({
    phone: phone.value,
    verifyCode: verifyCode.value
  });
  
  if (verifyResult.status === APIStatusCodeEnum.Success) {
    phoneVerifyStatus.value = PhoneVerifyStatusEnum.Verified;
    registerInput.value.phoneVerifyId = verifyResult.phoneVerifyId;
    toastSuccess('手機號碼驗證成功');
    clearCountdown();
  } else {
    toastError('驗證碼錯誤');
  }
};

// 註冊
const register = async () => {
  if (phoneVerifyStatus.value !== PhoneVerifyStatusEnum.Verified) {
    toastError('請先完成手機號碼驗證');
    return;
  }

  if (!registerInput.value.agreeTerm) {
    toastError('請勾選同意服務條款');
    return;
  }

  const { password, rePassword } = registerInput.value;
  if (password !== rePassword) {
    toastError('密碼不一致');
    return;
  }

  if (!passwordValid(password)) {
    toastError('密碼格式錯誤');
    return;
  }

  const ciphertext = await hashPassword(password);
  const registerResult = await SecurityService.registerAndLoginByCustomer({
    phoneVerifyId: registerInput.value.phoneVerifyId,
    password: ciphertext
  });
  
  if (registerResult.status !== APIStatusCodeEnum.Success) {
    toastError('註冊失敗');
    return;
  }

  // 根據模式決定註冊成功後的行為
  if (props.mode === 'full') {
    await submitCustomerMeasureData();
  }
  
  customerInfoStore.registerSuccess(registerResult.userId, phone.value, registerInput.value.password);
  toastSuccess('註冊成功');
  resetRegister();
  
  // 重定向到指定路由
  await router.push({ name: props.redirectRoute });
};

// 登入
const processLogin = async () => {
  if (!loginInput.value.phone || !loginInput.value.password) {
    toastError('請輸入完整登入資訊');
    return;
  }
  
  if (!(phoneNumberValid(loginInput.value.phone)) || !(passwordValid(loginInput.value.password))) {
    toastError('請輸入正確的登入資訊格式');
    return;
  }
  
  const loginSuccess = await customerInfoStore.login(loginInput.value);
  if (loginSuccess) {
    resetLoginInput();
    
    // 根據模式決定登入成功後的行為
    if (props.mode === 'full') {
      // 檢查重複地址邏輯
      const response = await CustomerMeasureOrderDetailService.getManyOrderDetail({});
      if (response.status === APIStatusCodeEnum.Success) {
        const found = response.result.some((item) => {
          if (item.address === step1Data.measureData.address.name) {
            openMultiAddressModal();
            return true;
          }
          return false;
        });
        if (!found) {
          await submitCustomerMeasureData();
        }
      }
    } else {
      // 簡化版直接重定向
      await router.push({ name: props.redirectRoute });
    }
  }
};

// 其他輔助函數...
const startCountdown = () => {
  sentCodeCountDown.value = 60;
  countdownInterval.value = setInterval(() => {
    if (sentCodeCountDown.value !== null && sentCodeCountDown.value > 0) {
      sentCodeCountDown.value--;
    } else {
      clearCountdown();
    }
  }, 1000);
};

const clearCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
  }
  sentCodeCountDown.value = null;
};

const resetRegister = () => {
  phone.value = '';
  verifyCode.value = '';
  phoneVerifyStatus.value = PhoneVerifyStatusEnum.Unverified;
  registerInput.value = {
    phoneVerifyId: '',
    password: '',
    rePassword: '',
    agreeTerm: false
  };
};

const resetLoginInput = () => {
  loginInput.value = {
    phone: '',
    password: '',
    rememberForShow: true
  };
};

// 模擬現有的函數（需要從原組件中複製）
const submitCustomerMeasureData = async () => {
  // 實現訂單提交邏輯
};

const openMultiAddressModal = () => {
  multiAddressModalRef.value?.openModal();
};

onMounted(() => {
  if (customerInfoStore.loginState) {
    router.push({ name: props.redirectRoute });
  }
  if (customerInfoStore.hasEverLogin) {
    isNowDoingLogin.value = true;
  }
});

onBeforeUnmount(() => {
  resetRegister();
  resetLoginInput();
  clearCountdown();
});
</script>

<template>
  <div class="flex cus-border space-x-4 my-8 lg:h-[650px]">
    <!-- 左側區域 -->
    <div class="w-1/2 relative max-md:hidden">
      <img src="/image/login_beauty_pic.jpg" alt="" class="object-cover h-full w-full relative left-0 rounded-xl">
      <div class="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 rounded-xl">
      </div>
    </div>

    <!-- 右側區域 -->
    <!-- 註冊 -->
    <div v-if="!isNowDoingLogin"
         class="w-1/2 max-md:w-full flex flex-col justify-center items-center max-lg:items-start p-8">
      <h2 v-if="showOrderMessage" class="text-lg font-bold mb-4">{{ getPageTitle() }}</h2>
      <h2 class="text-lg font-bold mb-4">{{ getPageSubtitle() }}</h2>
      <p class="text-black mb-4">已經註冊過家易帳號？ 
        <span class="text-blue-700 font-bold cursor-pointer" @click="isNowDoingLogin = true">立即登入！</span>
      </p>
      
      <!-- 註冊表單內容 -->
      <!-- 這裡需要複製原有的註冊表單 HTML -->
    </div>

    <!-- 登入 -->
    <div v-else class="w-1/2 max-md:w-full flex flex-col justify-center items-center max-lg:items-start p-8">
      <h2 class="text-lg font-bold mb-4">登入家易帳號</h2>
      <p class="text-black mb-4">還沒有家易帳號？ 
        <span class="text-blue-700 font-bold cursor-pointer" @click="isNowDoingLogin = false">立即註冊！</span>
      </p>
      
      <!-- 登入表單內容 -->
      <!-- 這裡需要複製原有的登入表單 HTML -->
    </div>
  </div>

  <!-- 重複地址確認Modal（僅在 full 模式顯示） -->
  <DefaultModal v-if="mode === 'full'" title="重複地址預約" :click-outside-close="false" :show-close-button="false"
                ref="multiAddressModalRef">
    <!-- Modal 內容 -->
  </DefaultModal>
</template>
