<script setup lang="ts">
import { computed, nextTick, reactive, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import {
  ImagePdfCadKeyContent,
  ImagePdfCadKeyItem,
  MediaUrlKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { toastError, toastInfo, toastSuccess, toastWarning } from '@/utils/toastification.ts';
import { DesignOrderDetailService } from '@/api/designerOrder.ts';
import { DesignOrderItemEnum } from '@/model/enum/orderUpdateType.ts';
import { EllipsisVerticalIcon, TrashIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import HezDivider from '@/components/General/HezDivider.vue';
import { UploadImageType } from '@/utils/hezParameters.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { PdfCadTypeEnum, UploadFileEnum } from '@/model/enum/fileType.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { downloadFile } from '@/utils/fileDownloader.ts';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import DesignerRemitModal from '@/components/Modal/DesignerRemitModal.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const design2D = defineModel<ImagePdfCadKeyContent>('design2D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus', { required: true });
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus', { required: true });
const props = defineProps<{ orderId: string; }>();
const emits = defineEmits(['refreshData', 'changeApiLoading']);

const addItemModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const editItemTitleModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const imageDetailEditModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const remitModal = ref<InstanceType<typeof DesignerRemitModal> | null>(null);
const addItemTitle = ref<string>('');
const editItemTitleData = ref<{ key: string, title: string }>({ key: '', title: '' });
const imageDetailEditData = ref<ImagePdfCadKeyItem>({
  key: '',
  name: '',
  images: [] as MediaUrlKeyItem[],
  pdf: {
    updateTime: '',
    url: ''
  },
  cad: {
    updateTime: '',
    url: ''
  },
  updateTime: ''
});
const isAPILoading = ref<boolean>(false);
const reFreshSwiper = ref<boolean>(false);
//因為我用了v-for產生多個 檔案input 所以需要搞一個用media.key去便是他們各自獨立性的 reactive，不然所有input ref都是同一個會衝突
const imageInputElement = reactive<{ [key: string]: HTMLInputElement | null; }>({});
const pdfInputElement = reactive<{ [key: string]: HTMLInputElement | null; }>({});
const cadInputElement = reactive<{ [key: string]: HTMLInputElement | null; }>({});

const clearImageDetailEditData = () => {
  imageDetailEditData.value = {
    key: '',
    name: '',
    images: [] as MediaUrlKeyItem[],
    pdf: {
      updateTime: '',
      url: ''
    },
    cad: {
      updateTime: '',
      url: ''
    },
    updateTime: ''
  };
};

const openAddItemModal = () => {
  addItemModalRef.value?.openModal();
};
const closeAddItemModal = () => {
  addItemTitle.value = '';
  addItemModalRef.value?.closeModal();
};
const openEditTitleModal = (key: string, title: string) => {
  editItemTitleData.value = { key, title };
  editItemTitleModalRef.value?.openModal();
};
const closeEditTitleModal = () => {
  editItemTitleModalRef.value?.closeModal();
  editItemTitleData.value = { key: '', title: '' };
};
const openImageDetailEditModal = (mediaItem: ImagePdfCadKeyItem) => {
  if (mediaItem.images.length === 0) {
    toastInfo('請先上傳圖片');
    return;
  }
  imageDetailEditData.value = mediaItem;
  imageDetailEditModalRef.value?.openModal();
};
const closeImageDetailEditModal = () => {
  imageDetailEditModalRef.value?.closeModal();
  setTimeout(() => {
    clearImageDetailEditData();
  }, 300);
};

const addItem = async () => {
  if (isAPILoading.value === false) {
    if (addItemTitle.value === '') {
      toastInfo('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.add2DItem({
      orderId: props.orderId,
      names: [addItemTitle.value]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      closeAddItemModal();
    }
    isAPILoading.value = false;
  }
};

const editItemTitle = async () => {
  if (isAPILoading.value === false) {
    if (editItemTitleData.value.title === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update2D({
      orderId: props.orderId,
      builders: [{
        key: editItemTitleData.value.key,
        setName: editItemTitleData.value.title,
        pushImage: [],
        pullImage: [],
        setImageDescription: [],
        setPdf: '',
        setCad: ''
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      closeEditTitleModal();
    }
    isAPILoading.value = false;
  }
};

const deleteItem = async (itemKey: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.deleteItem({
      orderId: props.orderId,
      keys: [itemKey]
    }, DesignOrderItemEnum.Design2D);
    switch (result.status) {
      case APIStatusCodeEnum.Success:
        emits('refreshData'); // 叫父元件刷新資料
        break;
      case APIStatusCodeEnum.OrderAcceptanceInProgressCannotDelete:
        toastInfo('客戶驗收中，無法刪除欄位');
        break;
      case APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete:
        toastInfo('客戶驗收完成，無法刪除欄位');
        break;
    }
    isAPILoading.value = false;
  }
};

const finish2D = async () => {
  if (orderStatus.value === DesignOrderStatusEnum.Contracting) {
    remitModal.value?.openModal();
    return;
  }

  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.finish2D({ orderId: props.orderId });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
    }
    isAPILoading.value = false;
  }
};

const deleteMediaDetail = async (mediaItemKey: string, mediaDetailKey: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update2D({
      orderId: props.orderId,
      builders: [{
        key: mediaItemKey,
        setName: '',
        pushImage: [],
        pullImage: [mediaDetailKey],
        setImageDescription: [],
        setPdf: '',
        setCad: ''
      }]
    });

    let resultMedia, resultMediaLength;
    switch (result.status) {
      case APIStatusCodeEnum.Success:
        emits('refreshData'); // 叫父元件刷新資料
        resultMedia = result.content.design2D.content.find(item => item.key === mediaItemKey);
        resultMediaLength = resultMedia ? resultMedia.images.length : 0;
        // 如果刪掉項目最後一張照片就直接退出Modal
        if (resultMediaLength === 0) {
          closeImageDetailEditModal();
          setTimeout(() => {
            clearImageDetailEditData();
          }, 300);
        } else {
          reFreshSwiper.value = true;
          imageDetailEditData.value = resultMedia as ImagePdfCadKeyItem;
          await nextTick();
          reFreshSwiper.value = false;
        }
        break;
      case APIStatusCodeEnum.OrderAcceptanceInProgressCannotDelete:
        toastInfo('客戶驗收中，無法刪除檔案');
        break;
      case APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete:
        toastInfo('客戶驗收完成，無法刪除檔案');
        break;
    }
    isAPILoading.value = false;
  }
};

const editImageDetailDescription = async (imagelKey: string, description: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update2D({
      orderId: props.orderId,
      builders: [{
        key: imageDetailEditData.value.key,
        setName: '',
        pushImage: [],
        pullImage: [],
        setImageDescription: [{
          key: imagelKey,
          description: description
        }],
        setPdf: '',
        setCad: ''
      }]
    });
    //TODO API 太慢的情況下 接到res之前 把資料上傳/修改Modal關掉 會導致父元件資料不會更新
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      imageDetailEditData.value = result.content.design2D.content.find(item => item.key === imageDetailEditData.value.key) as ImagePdfCadKeyItem;
    }
    isAPILoading.value = false;
  }
};

const triggerFileInput = (key: string, type: string) => {
  if (type === 'image') {
    imageInputElement[key]?.click();
  } else if (type === 'pdf') {
    pdfInputElement[key]?.click();
  } else if (type === 'cad') {
    cadInputElement[key]?.click();
  }
};

// 上傳 圖片用
const imageUpload = async (event: Event, imagePdfCadItem: ImagePdfCadKeyItem) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    emits('changeApiLoading', true)
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.Picture)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    const pushImage = S3Urls.map(url => {
      return {
        url: url,
        description: ''
      };
    });
    const result = await DesignOrderDetailService.update2D({
      orderId: props.orderId,
      builders: [{
        key: imagePdfCadItem.key,
        setName: '',
        pushImage: pushImage,
        pullImage: [],
        setImageDescription: [],
        setPdf: '',
        setCad: ''
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      toastSuccess('檔案上傳成功');
      emits('refreshData'); // 叫父元件刷新資料
    }
    emits('changeApiLoading', false)
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

//上傳 PDF & CAD 用
const fileUpload = async (event: Event, imagePdfCadItem: ImagePdfCadKeyItem, fileType: PdfCadTypeEnum) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    emits('changeApiLoading', true)
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.File)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      emits('changeApiLoading', false)
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    const setFile = S3Urls.map(url => {
      return {
        url: url
      };
    });
    if (fileType === PdfCadTypeEnum.Pdf) {
      // 更新 Pdf
      const result = await DesignOrderDetailService.update2D({
        orderId: props.orderId,
        builders: [{
          key: imagePdfCadItem.key,
          setName: '',
          pushImage: [],
          pullImage: [],
          setImageDescription: [],
          setPdf: setFile[0].url,
          setCad: ''
        }]
      });
      if (result.status === APIStatusCodeEnum.Success) {
        toastSuccess('檔案上傳成功');
        emits('refreshData'); // 叫父元件刷新資料
      }
    } else if (fileType === PdfCadTypeEnum.Cad) {
      // 更新 Cad
      const result = await DesignOrderDetailService.update2D({
        orderId: props.orderId,
        builders: [{
          key: imagePdfCadItem.key,
          setName: '',
          pushImage: [],
          pullImage: [],
          setImageDescription: [],
          setPdf: '',
          setCad: setFile[0].url
        }]
      });
      if (result.status === APIStatusCodeEnum.Success) {
        toastSuccess('檔案上傳成功');
        emits('refreshData'); // 叫父元件刷新資料
      }
    }
  }
  emits('changeApiLoading', false)
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const previewFile = async (imagePdfCadItem: ImagePdfCadKeyItem, type: PdfCadTypeEnum) => {
  if (type === PdfCadTypeEnum.Pdf) {
    if (imagePdfCadItem.pdf.url !== '') {
      await downloadFile(imagePdfCadItem.pdf.url);
    } else {
      toastInfo('尚未上傳 PDF');
    }
  } else if (type === PdfCadTypeEnum.Cad) {
    if (imagePdfCadItem.cad.url !== '') {
      await downloadFile(imagePdfCadItem.cad.url);
    } else {
      toastInfo('尚未上傳 CAD');
    }
  }
};

const isDesign2DValid = computed((): boolean => {
  if (design2D.value.content.length === 0) return false;

  if (!design2D.value || !design2D.value.content) return false;

  return design2D.value.content.every(item =>
    item.pdf.url !== '' &&
    item.cad.url !== '' &&
    item.images.length > 0
  );
});


const imageDetailInfo = computed(() => {
  return {
    key: imageDetailEditData.value.key,
    name: imageDetailEditData.value.name,
    images: imageDetailEditData.value.images.map((image) => {
      return {
        key: image.key,
        url: image.url,
        description: image.description,
        updateTime: formatFullDateTimeWithDay(image.updateTime)
      };
    }),
    pdf: {
      url: imageDetailEditData.value.pdf.url,
      updateTime: formatFullDateTimeWithDay(imageDetailEditData.value.pdf.updateTime)
    },
    cad: {
      url: imageDetailEditData.value.cad.url,
      updateTime: formatFullDateTimeWithDay(imageDetailEditData.value.cad.updateTime)
    },
    updateTime: formatFullDateTimeWithDay(imageDetailEditData.value.updateTime)
  };
});
</script>

<template>
  <div class="flex-col m-3">
    <div v-for="imagePdfCadItem in design2D.content" :key="imagePdfCadItem.key" class="flex flex-col">
      <div class="flex justify-between px-5 mb-2">
        <p class="text-xl">{{ imagePdfCadItem.name }}</p>
        <Menu as="div" class="relative flex-none">
          <MenuButton class="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
            <span class="sr-only">Open options</span>
            <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
          </MenuButton>
          <transition enter-active-class="transition ease-out duration-100"
                      enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                      leave-active-class="transition ease-in duration-75"
                      leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
            <MenuItems
              class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
              <MenuItem v-slot="{ active }">
                <a
                  :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                  @click="openEditTitleModal(imagePdfCadItem.key, imagePdfCadItem.name)"
                >編輯項目名稱</a>
              </MenuItem>
              <MenuItem v-slot="{ active }">
                <a
                  :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                  @click="deleteItem(imagePdfCadItem.key)"
                >移除項目</a>
              </MenuItem>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <div class="flex gap-x-2 justify-evenly">
        <div class="flex flex-col gap-y-2 w-1/3">
          <div
            @click="imagePdfCadItem.images.length >0?openImageDetailEditModal(imagePdfCadItem):triggerFileInput(imagePdfCadItem.key,'image')"
            class="flex flex-col gap-y-0.5 p-4 bg-gray-200 justify-center items-center cursor-pointer max-md:p-2">
            <div class="flex flex-col justify-center items-center">
              <img v-if="imagePdfCadItem.images.length >0" src="/vectors/order/uploadFile/photo_uploaded.svg" alt=""
                   class="w-14 h-14" />
              <img v-else src="/vectors/order/uploadFile/photo_xx.svg" alt="" class="w-14 h-14" />
              <p>圖片預覽({{ imagePdfCadItem.images.length }})</p>
            </div>
          </div>
          <div @click="triggerFileInput(imagePdfCadItem.key,'image')"
               class="flex flex-col gap-y-0.5 p-4 bg-color-secondary justify-center items-center cursor-pointer max-md:p-2">
            <img src="/vectors/general/upload.svg" alt="addImage" class="w-7 h-7" />
            <p>上傳圖片</p>
          </div>
          <input type="file" :ref="el => (imageInputElement[imagePdfCadItem.key] = el as HTMLInputElement)"
                 :accept="UploadImageType"
                 multiple required @change="event => imageUpload(event,imagePdfCadItem)" class="hidden">
        </div>
        <div class="flex flex-col gap-y-2 w-1/3">
          <div
            @click="imagePdfCadItem.pdf.url !== ''?previewFile(imagePdfCadItem,PdfCadTypeEnum.Pdf):triggerFileInput(imagePdfCadItem.key,'pdf')"
            class="flex flex-col gap-y-0.5 p-4 bg-gray-200 justify-center items-center cursor-pointer max-md:p-2">
            <div class="flex flex-col justify-center items-center">
              <img v-if="imagePdfCadItem.pdf.url !== ''" src="/vectors/order/uploadFile/pdf_uploaded.svg" alt=""
                   class="w-14 h-14" />
              <img v-else src="/vectors/order/uploadFile/pdf_xx.svg" alt="" class="w-14 h-14" />
              <p>PDF預覽</p>
            </div>
          </div>
          <div @click="triggerFileInput(imagePdfCadItem.key,'pdf')"
               class="flex flex-col gap-y-0.5 p-4 bg-color-secondary justify-center items-center cursor-pointer max-md:p-2">
            <img src="/vectors/general/upload.svg" alt="addImage" class="w-7 h-7" />
            <p>上傳PDF</p>
          </div>
          <input type="file" :ref="el => (pdfInputElement[imagePdfCadItem.key] = el as HTMLInputElement)"
                 accept="application/pdf"
                 required @change="event => fileUpload(event,imagePdfCadItem,PdfCadTypeEnum.Pdf)" class="hidden">
        </div>
        <div class="flex flex-col gap-y-2 w-1/3">
          <div
            @click="imagePdfCadItem.cad.url !== ''?previewFile(imagePdfCadItem,PdfCadTypeEnum.Cad):triggerFileInput(imagePdfCadItem.key,'cad')"
            class="flex flex-col gap-y-0.5 p-4 bg-gray-200 justify-center items-center cursor-pointer max-md:p-2">
            <div class="flex flex-col justify-center items-center">
              <img v-if="imagePdfCadItem.cad.url !== ''" src="/vectors/order/uploadFile/cad_uploaded.svg" alt=""
                   class="w-14 h-14" />
              <img v-else src="/vectors/order/uploadFile/cad_xx.svg" alt="" class="w-14 h-14" />
              <p>CAD預覽</p>
            </div>
          </div>
          <div @click="triggerFileInput(imagePdfCadItem.key,'cad')"
               class="flex flex-col gap-y-0.5 p-4 bg-color-secondary justify-center items-center cursor-pointer max-md:p-2">
            <img src="/vectors/general/upload.svg" alt="addImage" class="w-7 h-7" />
            <p>上傳CAD</p>
          </div>
          <input type="file" :ref="el => (cadInputElement[imagePdfCadItem.key] = el as HTMLInputElement)"
                 required @change="event => fileUpload(event,imagePdfCadItem,PdfCadTypeEnum.Cad)" class="hidden">
        </div>
      </div>
      <hez-divider />
    </div>
    <div
      class="flex max-md:flex-col w-full gap-2">
      <button type="button"
              class="button-basic w-full bg-color-selected hover:opacity-80 "
              @click="openAddItemModal()">新增項目
      </button>

      <template v-if="subStatus === DesignOrderSubStatusEnum.NotUploaded">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish2D()">請求撥款
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design2DUploaded">
        <button v-if="isDesign2DValid"
                type="button" class="button-basic w-full bg-color-selected hover:opacity-80 "
                @click="finish2D()">請求撥款
        </button>
        <button v-else type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish2D()">請求撥款
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design2DRequest">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish2D()">客戶驗收中
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design2DReject">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish2D()">客戶要求修正
        </button>
      </template>
      <template v-if="subStatus >= DesignOrderSubStatusEnum.Design2DAgree">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish2D()">客戶已同意撥款
        </button>
      </template>

    </div>
  </div>
  <!--新增項目-對話框-->
  <DefaultModal title="新增項目名稱" :showCloseButton="false" :click-outside-close="false" ref="addItemModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemTitle"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="addItem()">確認
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeAddItemModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
  <!--編輯項目-對話框-->
  <DefaultModal title="編輯項目名稱" :showCloseButton="false" :click-outside-close="false" ref="editItemTitleModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="editItemTitleData.title"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="editItemTitle()">修改
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeEditTitleModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
  <!--預覽-對話框-->
  <DefaultModal title="預覽" :show-close-button="true" :click-outside-close="false" ref="imageDetailEditModalRef"
                @closeModal="closeImageDetailEditModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true" v-if="!reFreshSwiper">
          <swiper-slide v-for="mediaDetail in [...imageDetailInfo.images].reverse()" :key="mediaDetail.key"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ mediaDetail.updateTime }}</p>
            <img :src="mediaDetail.url" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <div class="flex justify-between items-center">
              <input type="text"
                     class="relative block border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
                     v-model="mediaDetail.description"
                     @change="editImageDetailDescription(mediaDetail.key, mediaDetail.description)"
                     placeholder="請輸入註解文字" />
              <div class="flex flex-col justify-center cursor-pointer hover-zoom"
                   @click="deleteMediaDetail(imageDetailInfo.key,mediaDetail.key)">
                <div class="flex justify-center">
                  <TrashIcon class="w-7 h-7" />
                </div>
                <p>刪除此圖片</p>
              </div>
            </div>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>

  <DesignerRemitModal title="客戶尚未匯款" ref="remitModal" />
</template>
