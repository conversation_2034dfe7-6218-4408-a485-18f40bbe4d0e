import { BaseResponse } from '@/model/response/baseResponse.ts';
import { BindInfoEnum, MeasureOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import { AddressItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';

export interface CustomerMeasureOrderPublishResponse extends BaseResponse {
  result: CustomerOrderMeasureItem;
}

export interface CustomerMeasureOrderBindResponse extends BaseResponse {
  result: { [key: string]: BindInfoEnum };
}

export interface CustomerGetOneMeasureOrderDetailResponse extends BaseResponse {
  result: CustomerOrderMeasureItem;
}

export interface CustomerGetManyMeasureOrderDetailResponse extends BaseResponse {
  result: CustomerBasicOrderMeasureItem[];
}

export interface deleteOrderResponse extends BaseResponse {

}

export interface refillOrderTimeResponse extends BaseResponse {
  reserveTimes: {
    index: number;
    createTime: string;
    measureTime: string;
    isDeleted: boolean;
    deletedTime: string;
  }[];
}

export interface CustomerBasicOrderMeasureItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: MeasureOrderStatusEnum;
  isDeleted: boolean;
  address: string;
  designerName: string;
  measureTime: string;
  content: {
    houseInfo: MeasureBasicContent;
    photos: MeasureBasicContent;
    houseCheck: MeasureBasicContent;
    floorPlan: MeasureBasicContent;
    note: MeasureBasicContent;
  };
  isNextStepUsed: boolean;
}

export interface MeasureBasicContent {
  updateTime: string;
  updateCount: number;
}

export interface CustomerOrderMeasureItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: MeasureOrderStatusEnum;
  customerId: string;
  customerName: string;
  isDeleted: boolean;
  address: AddressItem;
  designerId: string;
  designerName: string;
  designerAvatarUrl: string;
  designerPhone: string;
  measureDate: {
    refreshTime: string;
    isConfirm: boolean;
    measureTime: string;
    reserveTimes: {
      index: number;
      createTime: string;
      measureTime: string;
      isDeleted: boolean;
      deletedTime: string;
    }[]
  };
  content: CustomerMeasureContent,
  isNextStepUsed: boolean;
  chatRoomId: string;
  orderRatingId: string;
}

export interface CustomerMeasureContent {
  houseInfo: CustomerTextContent;
  photos: CustomerImageVideoContent;
  houseCheck: CustomerImageVideoContent;
  floorPlan: CustomerImagePdfCadContent;
  note: CustomerTextContent;
  constructionRequest: CustomerTextContent;
  waterQuality: CustomerTextContent;
  airQuality: CustomerTextContent;
  noise: CustomerTextContent;
  humidity: CustomerTextContent;
  radiation: CustomerTextContent;
}

export interface CustomerTextContent {
  updateTime: string;
  updateCount: number;
  content: {
    updateTime: string;
    name: string;
    text: string;
  }[];
}

export interface CustomerImageVideoContent {
  updateTime: string;
  updateCount: number;
  content: CustomerImageVideoItem[];
}

export interface CustomerImageVideoItem {
  updateTime: string;
  name: string;
  media: CustomerMediaUrlItem[];
  type: MediaTypeEnum;
}

export interface CustomerMediaUrlItem {
  updateTime: string;
  url: string;
  description: string;
}

export interface CustomerImagePdfCadContent {
  updateTime: string;
  updateCount: number;
  content: CustomerImagePdfCadItem[];
}

export interface CustomerImagePdfCadItem {
  updateTime: string;
  name: string;
  images: {
    updateTime: string;
    url: string;
    description: string;
  }[];
  pdf: {
    updateTime: string;
    url: string;
  };
  cad: {
    updateTime: string;
    url: string;
  };
}

