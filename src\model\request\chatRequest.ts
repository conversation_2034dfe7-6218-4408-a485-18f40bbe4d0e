import { MsgFileTypeEnum, MsgSendTypeEnum, RecordTypeEnum } from '@/model/enum/chatEnum.ts';

export interface MeetDialRequest {
  roomId: string;
  recordType: RecordTypeEnum;
}

export interface MeetAnswerRequest {
  roomId: string;
}

export interface GetOneChatRoomRequest {
  roomId: string;
}

export interface GetChatRoomMsgRequest {
  roomId: string;
  //跳過多少筆 預設0
  skip: number;
  //取多少筆 預設20
  limit: number;
}

export interface SendChatRoomMsgRequest {
  roomId: string;
  message: {
    type: MsgSendTypeEnum;
    sticker: string
    text: string;
    files: FileItem[];
  };
}

export interface UnSendChatRoomMsgRequest {
  roomId: string;
  messageId: string;
}

export interface FileItem {
  type: MsgFileTypeEnum;
  name: string;
  url: string;
}
