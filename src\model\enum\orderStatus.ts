/**
 * @description Step1 訂單狀態
 */
export enum MeasureOrderStatusEnum {
  /**
   * 訂單已成立，需驗證手機號碼
   */
  VerifyPhone = 0,

  /**
   * 訂單已成立，待安排丈量師
   */
  WaitingSurveyor = 1,

  /**
   * 訂單已成立，丈量師即將到府服務
   */
  SurveyorComing = 2,

  /**
   * 待丈量師上傳資料
   */
  WaitingUpload = 3,

  /**
   * 丈量師已上傳資料
   */
  SurveyDone = 4,

  /**
   * 訂單已成立，需重新填寫預約時間
   */
  RefillTime = 5,
}

/**
 * @description Step2 訂單狀態
 */
export enum DesignOrderStatusEnum {
  /**
   * 客戶比價找設計師
   */
  Comparing = 0,

  /**
   * 雙方簽約、匯款
   */
  Contracting = 1,

  /**
   * 設計師上傳、客戶驗收
   */
  Working = 2,

  /**
   * 訂單完成
   */
  Completed = 3,
}

export enum DesignOrderSubStatusEnum {
  /**
   * 2D室內設計未上傳
   */
  NotUploaded = 0,

  /**
   * 2D室內設計已上傳
   */
  Design2DUploaded = 1,

  /**
   * 2D室內設計請求撥款
   */
  Design2DRequest = 2,

  /**
   * 2D室內設計拒絕撥款
   */
  Design2DReject = 3,

  /**
   * 2D室內設計同意撥款
   */
  Design2DAgree = 4,

  /**
   * 3D模型設計已上傳
   */
  Design3DUploaded = 5,

  /**
   * 3D模型設計請求撥款
   */
  Design3DRequest = 6,

  /**
   * 3D模型設計拒絕撥款
   */
  Design3DReject = 7,

  /**
   * 3D模型設計同意
   */
  Design3DAgree = 8,

  /**
   * 裝潢施工費清單已上傳
   */
  ConstructionAmountDocsUploaded = 9,

  /**
   * 裝潢施工費清單被接受
   */
  ConstructionAmountDocsAccepted = 10,

  /**
   * 裝潢施工費清單被要求修正
   */
  ConstructionAmountDocsRequest = 11,
}

/**
 * @description Step3 訂單狀態
 */
export enum ConstructionOrderStatusEnum {
  /**
   * 客戶比價找設計師 - 未接續設計師才會有此狀態
   */
  Comparing = 0,

  /**
   * 客戶等待設計師詳細報價 - 未接續設計師才會有此狀態
   */
  Quoting = 1,

  /**
   * 雙方簽約、匯款
   */
  Contracting = 2,

  /**
   * 設計師上傳、客戶驗收
   */
  Working = 3,

  /**
   * 訂單完成
   */
  Completed = 4,
}

export enum SigningStatusEnum {
  /**
   * 初始狀態
   */
  Init = 0,

  /**
   * 驗證/審核中
   */
  Verifying = 1,

  /**
   * 驗證/審核已完成
   */
  Verified = 2,

  /**
   * 驗證/審核失敗
   */
  VerifyFail = 3,
}

export enum AmountStatusEnum {
  /**
   * 未報價
   */
  NotQuoted = 0,

  /**
   * 已報價
   */
  Quoted = 1,

  /**
   * 客戶同意
   */
  CustomerAgreed = 2,

  /**
   * 客戶不同意
   */
  CustomerDisagreed = 3,
}

//驗收流程Enum
export enum AcceptanceProcessStatusEnum {
  /**
   * 初始狀態
   */
  Init = 0,

  /**
   * 等待客戶確認
   */
  WaitingCustomerConfirm = 1,

  /**
   * 等待設計師確認
   */
  WaitingDesignerConfirm = 2,

  /**
   * 雙方同意
   */
  AgreementReached = 3,
}

//申請追加工程Enum
export enum AppendProcessStatusEnum {
  /**
   * 初始或客戶完成同意
   */
  InitOrCompleted = 0,

  /**
   * 等待客戶確認
   */
  WaitingCustomerConfirm = 1,

  /**
   * 客戶不同意
   */
  CustomerDisagreed = 2,
}

export enum ConstructionProcessStatusEnum {
  /**
   * 餘額不足
   */
  InsufficientBalance = 0,

  /**
   * 未請求撥款
   */
  NotRequested = 1,

  /**
   * 待撥款
   */
  PendingDisbursement = 2,

  /**
   * 已完成撥款
   */
  Completed = 3,

  /**
   * 要求修正
   */
  Reject = 4,
}

export enum BindInfoEnum {
  /**
   * 成功
   */
  Success = 0,

  /**
   * 未知的錯誤
   */
  Unknown = 1,

  /**
   * 資料或格式錯誤
   */
  DataOrFormatError = 2,

  /**
   * 找不到訂單資料
   */
  OrderNotFound = 3,

  /**
   * 訂單已被刪除
   */
  OrderIsDeleted = 4,

  /**
   * 訂單已被綁定
   */
  OrderIsBind = 5
}

export enum RemittanceStatusEnum {
  /**
   * 未匯款
   */
  NotRemitted = 0,

  /**
   * 審核中(匯款中)
   */
  InProgress = 1,

  /**
   * 已匯款(匯款完成)
   */
  Remitted = 2,
}

export enum InvoiceOrderStatusEnum {
  /**
   * 未開立
   */
  NotIssued = 0,

  /**
   * 待開立
   */
  Pending = 1,

  /**
   * 已開立
   */
  Issued = 2,
}

export enum DesignerConstructionQuoteStatusEnum {
  /**
   * 未初始報價
   */
  UNACCEPTED = 0,

  /**
   * 已初始報價
   */
  INIT_QUOTED = 1,

  /**
   * 已詳細報價
   */
  DETAIL_QUOTED = 2,

  /**
   * 詳細報價已接受
   */
  ACCEPTED = 3,
}
