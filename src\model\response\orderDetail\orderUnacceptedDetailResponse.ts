import { BaseResponse } from '@/model/response/baseResponse.ts';
import {
  AddressItem, ConstructionAmountItem, ConstructionCustomerPublishInfo, DesignContentKeyItem,
  DesignCustomerPublishInfo,
  DesignerQuoteItem,
  UnacceptedMeasureContentKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { Budget } from '@/model/general/budget.ts';
import { AmountStatusEnum } from '@/model/enum/orderStatus.ts';

export interface MeasureOrderUnacceptedDetailResponse extends BaseResponse {
  result: MeasureOrderUnacceptedDetailItem;
}

export interface DesignOrderUnacceptedDetailResponse extends BaseResponse {
  result: DesignOrderUnacceptedDetailItem;
}

export interface ConstructionOrderUnacceptedDetailResponse extends BaseResponse {
  result: ConstructionOrderUnacceptedDetailItem;
}

export interface MeasureOrderUnacceptedDetailItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: AddressItem;
  measureTime: {
    index: number;
    measureTime: string;
    checkTime: string[];
  }[];
}

export interface DesignOrderUnacceptedDetailItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: string;
  publishInfo: DesignCustomerPublishInfo;
  measureContent: UnacceptedMeasureContentKeyItem;
  designerQuote: DesignerQuoteItem;
  chatRoomId: string;
}

export interface ConstructionOrderUnacceptedDetailItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  customerName: string;
  address: AddressItem;
  publishInfo: ConstructionCustomerPublishInfo;
  measureContent: UnacceptedMeasureContentKeyItem;
  designContent: DesignContentKeyItem;
  designerId: string;
  customerId: string;
  customerAvatarUrl: string;
  customerPhone: string;
  contract: ConstructionQuotingContractItem;
  chatRoomId: string;
}

export interface ConstructionQuotingContractItem {
  constructionEstimate: Budget;
  isDetailQuoted: boolean;
  amountStatus: AmountStatusEnum;
  constructionAmountDocs: ConstructionAmountItem[];
  constructionAmount: number;
  constructionDiscountAmount: number;
}