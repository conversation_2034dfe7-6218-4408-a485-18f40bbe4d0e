<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { GetLinkListResponse, LinkRecordItem } from '@/model/response/noteResponse.ts';
import { NoteListService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { formatFullDateTime } from '@/utils/timeFormat.ts';
import HezDivider from '@/components/General/HezDivider.vue';
import { openNewTab } from '@/utils/chatRoomUtils.ts';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const data = ref<LinkRecordItem[]>([]);
const skip = ref<number>(0);
const isFirstTime = ref<boolean>(true);
const isMoreData = ref<boolean>(true);

const getLinkList = async () => {
  let res: GetLinkListResponse;
  if (props.isDesigner) {
    res = await NoteListService.GetLinkListByDesigner(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  } else {
    res = await NoteListService.GetLinkListByCustomer(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    if (res.links.length < 20) {
      isMoreData.value = false;
    }

    if (res.links.length === 0) {
      if (!isFirstTime.value) toastInfo('已無更多資料');
      return;
    } else {
      data.value.push(...res.links);
      skip.value += 20;
    }
  }
};

const showData = computed(() => {
  return data.value.reduce((acc, item) => {
    // 將 createTime 轉換為當地時間的日期字串
    const date = new Date(item.createTime).toLocaleDateString();

    // 如果 acc 中不存在該日期，則初始化為空陣列
    if (!acc[date]) {
      acc[date] = [];
    }

    // 將 item 添加到對應日期的陣列中
    acc[date].push(item);

    return acc;
  }, {} as Record<string, LinkRecordItem[]>);
});

onMounted(async () => {
  await getLinkList();
  isFirstTime.value = false;
});
</script>

<template>
  <div class="w-full text-black" v-if="data.length === 0">
    <p>尚無任何連結</p>
  </div>
  <div class="w-full flex flex-col gap-y-1 text-black" v-else>
    <div v-for="(links,date) in showData" :key="date" class="w-full">
      <p class="text-start mb-2 font-bold">{{ date }}</p>
      <div v-for="link in links" :key="link.matchUrl" class="mb-2 cursor-pointer"
           @click="openNewTab(link.matchUrl)">
        <div class="flex gap-x-2 items-center">
          <img src="/vectors/chatRoom/link.svg" alt="file" class="w-20 h-20" />
          <div class="flex flex-col items-start gap-y-1">
            <p class="break-all text-blue-600 text-start">{{ link.text }}</p>
            <p>{{ formatFullDateTime(link.createTime) }}</p>
          </div>
        </div>
        <HezDivider />
      </div>
    </div>
    <p class="cursor-pointer" v-if="isMoreData" @click="getLinkList()">查看更多</p>
  </div>
</template>
