import { RatingTypeEnum } from '@/model/enum/ratingType.ts';

export interface IntroductionFormated {
  avatarUrl: string;
  designerName: string;
  satisfaction: string;
  portfolioCount: number;
  hasConstructionTeam: string;
  region: string[];
  website: string;
  companyName: string;
  companyUnifiedBusinessNumber: string;
  companyServiceTime: string;
}

export interface RatingFormated {
  orderId: string;
  customerName: string;
  customerAvatar: string;
  ratingType: RatingTypeEnum;
  addressTitle: string;
  address: string;
  housePingTitle: string;
  housePing: string;
  amount: string;
  amountTitle: string;
  durationTitle: string;
  duration: string;
  comment: string;
  ratingTitle: string;
  measureRating: {
    arrivalOnTime: starRatingItem;
    serviceAttitude: starRatingItem;
    professionalQuality: starRatingItem;
    completionTime: starRatingItem;
  };
  designRating: {
    workQuality: starRatingItem;
    enthusiasticAttitude: starRatingItem;
    designEfficiency: starRatingItem;
  };
  constructionRating: {
    constructionQuality: starRatingItem;
    serviceAttitude: starRatingItem;
    completionTime: starRatingItem;
  };
}

export interface starRatingItem {
  star: number;
  nonStar: number;
}
