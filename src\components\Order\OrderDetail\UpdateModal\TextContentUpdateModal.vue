<script setup lang="ts">
import { ref } from 'vue';
import TextContentModalContent from '@/components/Order/OrderDetail/UpdateModal/ModalContent/TextContentModalContent.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { TextKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';

const props = defineProps<{ 
  title: string; 
  orderId: string;
  contentName: string; 
  canNotDeleteCount: number;//從前往後數 有多少欄位不可刪除
  canAddUnit: boolean
}>();
const textContent = defineModel<TextKeyContent>({ required: true });
const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const textContentModalRef = ref<InstanceType<typeof TextContentModalContent> | null>(null);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  updateModalRef.value?.closeModal();
};

const handleDefaultModalClose = async () => {
  await textContentModalRef.value?.saveData();
  closeModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" :showCloseButton="true"
                :click-outside-close="false"
                @closeModal="handleDefaultModalClose()" ref="updateModalRef">
    <TextContentModalContent v-model="textContent" :orderId="props.orderId" :contentName="props.contentName" :canNotDeleteCount="props.canNotDeleteCount" :canAddUnit="props.canAddUnit"
                           ref="textContentModalRef" />
  </DefaultModal>
</template>
