import { BaseResponse } from '@/model/response/baseResponse.ts';
import {
  ConstructionOrderStatusEnum,
  DesignOrderStatusEnum, DesignOrderSubStatusEnum,
  MeasureOrderStatusEnum
} from '@/model/enum/orderStatus.ts';
import {
  ConstructionAmountItem,
  ImagePdfCadKeyContent,
  ImageVideoKeyContent,
  MeasureContentKeyItem, ProcessContent
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';

export interface MeasureDataUpdateResponse extends BaseResponse {
  orderStatus: MeasureOrderStatusEnum;
  content: MeasureContentKeyItem;
}

export interface DesignDataUpdateResponse extends BaseResponse {
  orderStatus: DesignOrderStatusEnum;
  subStatus: DesignOrderSubStatusEnum;
  content: {
    design2D: ImagePdfCadKeyContent;
    design3D: ImageVideoKeyContent;
    constructionAmountDocs: ConstructionAmountItem[];
  };

}

export interface ConstructionDataUpdateResponse extends BaseResponse {
  orderStatus: ConstructionOrderStatusEnum;
  content: {
    process: ProcessContent;
  };
}
