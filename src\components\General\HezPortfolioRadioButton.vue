<script setup lang="ts">
import { OrderSelection } from '@/model/general/orderSelection.ts';
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  title: string;
  options: OrderSelection[];
  legend: string;
  name: string;
}>();

const selected = defineModel<string>({ required: true });
const otherInput = ref(''); // 儲存“其他”選項的輸入值
const isKnownOption = (value: string) => {
  return props.options.some(option => option.name === value);
};

const initialSelected = isKnownOption(selected.value) ? selected.value : '其他';
const actualSelected = ref(initialSelected); // 實際選擇的選項初始化為 selected 的值
if (!isKnownOption(selected.value)) {
  otherInput.value = selected.value;
}
const isOtherSelected = computed(() => actualSelected.value === '其他');

// 監聽實際選擇的變化
watch(actualSelected, (newValue) => {
  if (newValue !== '其他') {
    selected.value = newValue;
  }
});

// 監聽其他選項的變化
watch(otherInput, (newValue) => {
  if (isOtherSelected.value) {
    selected.value = newValue;
  }
});

watch(selected, (newValue) => {
  if (newValue !== otherInput.value && isKnownOption(newValue)) {
    actualSelected.value = newValue;
  }
});

</script>

<template>
  <div class="text-start">
    <fieldset class="flex flex-col space-y-4">
      <label class="text-base font-semibold ">{{ props.title }}</label>
      <legend class="sr-only">{{ props.legend }}</legend>
      <div class="flex max-md:flex-col md:items-center justify-start gap-4">
        <div v-for="option in options" :key="option.key" class="flex items-center">
          <input
            :id="option.key"
            :name="props.name"
            type="radio"
            :value="option.name"
            v-model="actualSelected"
            class="h-4 w-4 border-gray-300 text-gray-600 focus:ring-gray-600"
          />
          <label :for="option.key" class="ml-2 flex items-center font-medium leading-6">
            <!--            <img :src="option.icon" :alt="option.name" class="w-6 h-6" />-->
            {{ option.name }}
          </label>
        </div>
        <input v-if="isOtherSelected"
               type="text"
               placeholder="請輸入其他項目"
               v-model="otherInput"
               class="h-fit w-fit p-2 cus-border focus-border-color">
      </div>
    </fieldset>
  </div>
</template>
