<script setup lang="ts">
import { downloadFile } from '@/utils/fileDownloader.ts';
import HezDivider from '@/components/General/HezDivider.vue';
import { CustomerConstructionFee } from '@/model/response/customerDesignOrderResponse.ts';
import { computed } from 'vue';
import { budgetCombineLowToHigh, moneyAddCommas } from '@/utils/budgetFormat.ts';

const constructionFee = defineModel<CustomerConstructionFee>({ required: true });

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

const constructionFeeData = computed(() => {
  return {
    updateTime: constructionFee.value.updateTime,
    updateCount: constructionFee.value.updateCount,
    customerBudget: budgetCombineLowToHigh(constructionFee.value.constructionBudget),
    designerBudget: budgetCombineLowToHigh(constructionFee.value.constructionEstimate),
    constructionAmountDoc: {
      amount: constructionFee.value.constructionAmountDocs.length > 0 ? moneyAddCommas(constructionFee.value.constructionAmountDocs.slice(-1)[0].amount) : 0,
      documentUrl: constructionFee.value.constructionAmountDocs.length > 0 ? constructionFee.value.constructionAmountDocs.slice(-1)[0].documentUrl : ''
    }
  };
});

</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">裝潢施工報價</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div class="flex max-md:flex-col">
        <div class="flex flex-col gap-2 justify-start text-lg text-color-primary">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold text-nowrap">客戶的預算</p>
            <p class="font-bold text-black">{{ constructionFeeData.customerBudget }}</p>
          </div>

          <div class="flex md:gap-x-2 gap-x-0.5 items-center" v-if="constructionFee.constructionEstimate.lower !== -1">
            <p class="font-bold text-nowrap">設計師估價</p>
            <p class="font-bold text-black">{{ constructionFeeData.designerBudget }}</p>
          </div>

          <div class="flex md:gap-x-2 gap-x-0.5 items-center max-md:items-start">
            <p class="font-bold text-nowrap">裝潢施工費</p>
            <div v-if="constructionFee.constructionAmountDocs.length === 0" class="flex">
              <p class="font-bold text-black">未提供</p>
            </div>
            <div v-else class="flex flex-wrap items-center gap-1">
              <p class="font-bold text-black">{{ constructionFeeData.constructionAmountDoc.amount }}</p>
              <button class="cus-btn button-padding text-base text-nowrap"
                      @click="handleDownloadFile(constructionFeeData.constructionAmountDoc.documentUrl)">
                查看報價
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
