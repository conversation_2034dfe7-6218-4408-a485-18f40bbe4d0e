import { RatingResult } from '@/model/response/portfolioResponse.ts';
import { RatingFormated } from '@/model/formatted/portolio.ts';
import { RatingTypeEnum } from '@/model/enum/ratingType.ts';
import { formatDateRange } from '@/utils/timeFormat.ts';
import { moneyAddCommas } from '@/utils/budgetFormat.ts';

export const ratingDataFormat = (data: RatingResult[]): RatingFormated[] => {
  return data.map((item): RatingFormated => {
    return {
      orderId: item.orderId,
      customerName: obfuscateName(item.customerName),
      customerAvatar: item.customerAvatar,
      ratingType: item.ratingType,
      address: item.address,
      addressTitle: typeTitleConcat(item.ratingType, '地址'),
      housePing: `${item.areaPing}坪 (${item.houseType})`,
      housePingTitle: typeTitleConcat(item.ratingType, '坪數'),
      amount: moneyAddCommas(item.amount),
      amountTitle: typeTitleConcat(item.ratingType, '價錢'),
      durationTitle: typeTitleConcat(item.ratingType, '時間'),
      duration: formatDateRange(item.startTime, item.endTime),
      comment: item.comment,
      ratingTitle: ratingTypeToTitle(item.ratingType),
      measureRating: {
        arrivalOnTime: {//準時抵達
          star: item.measureRating.arrivalOnTime,
          nonStar: 5 - item.measureRating.arrivalOnTime
        },
        serviceAttitude: {//服務態度
          star: item.measureRating.serviceAttitude,
          nonStar: 5 - item.measureRating.serviceAttitude
        },
        professionalQuality: {//專業品質
          star: item.measureRating.professionalQuality,
          nonStar: 5 - item.measureRating.professionalQuality
        },
        completionTime: {//完成時間
          star: item.measureRating.completionTime,
          nonStar: 5 - item.measureRating.completionTime
        }
      },
      designRating: {
        workQuality: {//作品質量
          star: item.designRating.workQuality,
          nonStar: 5 - item.designRating.workQuality
        },
        enthusiasticAttitude: {//態度熱忱
          star: item.designRating.enthusiasticAttitude,
          nonStar: 5 - item.designRating.enthusiasticAttitude
        },
        designEfficiency: {//設計效率
          star: item.designRating.designEfficiency,
          nonStar: 5 - item.designRating.designEfficiency
        }
      },
      constructionRating: {
        constructionQuality: {//施工品質
          star: item.constructionRating.constructionQuality,
          nonStar: 5 - item.constructionRating.constructionQuality
        },
        serviceAttitude: {//服務態度
          star: item.constructionRating.serviceAttitude,
          nonStar: 5 - item.constructionRating.serviceAttitude
        },
        completionTime: {//完成時間
          star: item.constructionRating.completionTime,
          nonStar: 5 - item.constructionRating.completionTime
        }
      }
    };
  });
};

const typeTitleConcat = (ratingType: RatingTypeEnum, title: string): string => {
  switch (ratingType) {
    case RatingTypeEnum.Measure:
      return `丈量${title}`;
    case RatingTypeEnum.Design:
      return `設計${title}`;
    case RatingTypeEnum.Construction:
      return `裝潢${title}`;
  }
};

const ratingTypeToTitle = (ratingType: RatingTypeEnum): string => {
  switch (ratingType) {
    case RatingTypeEnum.Measure:
      return '空間丈量';
    case RatingTypeEnum.Design:
      return '室內設計';
    case RatingTypeEnum.Construction:
      return '裝潢施工';
  }
};

const obfuscateName = (name: string): string => {
  if (name.length === 2) {
    // 如果是兩個字的姓名，只模糊後面一個字
    return `${name.charAt(0)}O`;
  } else if (name.length > 2) {
    // 如果是多於兩個字的姓名，保留首尾字母，中間用"O"替代
    const firstChar = name.charAt(0);
    const lastChar = name.charAt(name.length - 1);
    const middle = 'O'.repeat(name.length - 2);
    return `${firstChar}${middle}${lastChar}`;
  } else {
    // 如果姓名只有一個字或空的情況，直接返回
    return name;
  }
};
