import BaseApiService from './baseApiService.ts';
import {
  CustomerMeasureOrderBindRequest,
  CustomerGetOneOrderDetailRequest,
  CustomerMeasureOrderPublishRequest,
  CustomerGetManyOrderDetailRequest,
  refillMeasureOrderTimeRequest,
  deleteOrderRequest,
  CustomerGetOneInvoiceRequest,
  CompleteInvoiceInfoRequest
} from '@/model/request/customerMeasureOrderRequest.ts';
import {
  CustomerMeasureOrderBindResponse,
  CustomerGetOneMeasureOrderDetailResponse,
  CustomerMeasureOrderPublishResponse,
  CustomerGetManyMeasureOrderDetailResponse,
  deleteOrderResponse,
  refillOrderTimeResponse
} from '@/model/response/customerMeasureOrderResponse.ts';
import {
  CustomerGetManyDesignOrderDetailResponse,
  CustomerGetOneDesignOrderDetailResponse
} from '@/model/response/customerDesignOrderResponse.ts';
import {
  CustomerGetManyConstructionOrderDetailResponse,
  CustomerGetOneConstructionOrderDetailResponse
} from '@/model/response/customerConstructionOrderResponse.ts';
import {
  CustomerAccepting2D3DRequest,
  CustomerAcceptingAmountDocsRequest
} from '@/model/request/customerDesignOrderRequest.ts';
import { CustomerAcceptingProcessRequest } from '@/model/request/customerConstructionOrderRequest.ts';
import { CustomerBasicInvoiceResponse, CustomerInvoiceResponse } from '@/model/response/customerInvoiceResponse.ts';
import { RateResponse } from '@/model/response/RateResponse.ts';
import { RateRequest } from '@/model/request/RateRequest.ts';
import { BaseResponse } from '@/model/response/baseResponse.ts';
import { CustomerOrderStepCheckRequest } from '@/model/request/CustomerOrderRequest.ts';

export class CustomerOrderService {
  static async StepCheck(requestData: CustomerOrderStepCheckRequest): Promise<BaseResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/StepCheck', requestData);
    return response.data as BaseResponse;
  }
}

export class CustomerMeasureOrderService {
  /**
   * @description: 刊登空間丈量訂單
   */
  static async publishOrder(
    requestData: CustomerMeasureOrderPublishRequest
  ): Promise<CustomerMeasureOrderPublishResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Publish', requestData);
    return response.data as CustomerMeasureOrderPublishResponse;
  }

  /**
   * @description: 將未綁定的訂單綁定給當前使用者
   */
  static async bindOrder(requestData: CustomerMeasureOrderBindRequest): Promise<CustomerMeasureOrderBindResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Bind', requestData);
    return response.data as CustomerMeasureOrderBindResponse;
  }

  /**
   * @description: 取消丈量訂單
   */
  static async deleteOrder(requestData: deleteOrderRequest): Promise<deleteOrderResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Delete', requestData);
    return response.data as deleteOrderResponse;
  }

  /**
   * @description: 重新預約丈量時間
   */
  static async refillOrderTime(requestData: refillMeasureOrderTimeRequest): Promise<refillOrderTimeResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Update/MeasureTimes', requestData);
    return response.data as refillOrderTimeResponse;
  }
}

export class CustomerDesignOrderService {
  /**
   * @description: 取消設計訂單
   */
  static async deleteOrder(requestData: deleteOrderRequest): Promise<deleteOrderResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Delete', requestData);
    return response.data as deleteOrderResponse;
  }

  /**
   * @description: 同意撥款或要求修正 2D
   */
  static async accepting2D(
    requestData: CustomerAccepting2D3DRequest
  ): Promise<CustomerGetOneDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Approve/Design2D', requestData);
    return response.data as CustomerGetOneDesignOrderDetailResponse;
  }

  /**
   * @description: 同意撥款或要求修正 3D
   */
  static async accepting3D(
    requestData: CustomerAccepting2D3DRequest
  ): Promise<CustomerGetOneDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Approve/Design3D', requestData);
    return response.data as CustomerGetOneDesignOrderDetailResponse;
  }

  /**
   * @description: 同意報價清單
   */
  static async approveAmountDocs(
    requestData: CustomerAcceptingAmountDocsRequest
  ): Promise<CustomerGetOneDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Approve/AmountDocs', requestData);
    return response.data as CustomerGetOneDesignOrderDetailResponse;
  }

  /**
   * @description: 要求修正報價清單
   */
  static async rejectAmountDocs(
    requestData: CustomerAcceptingAmountDocsRequest
  ): Promise<CustomerGetOneDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Approve/Request', requestData);
    return response.data as CustomerGetOneDesignOrderDetailResponse;
  }
}

export class CustomerConstructionOrderService {
  /**
   * @description: 取消裝潢訂單
   */
  static async deleteOrder(requestData: deleteOrderRequest): Promise<deleteOrderResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Construction/Delete', requestData);
    return response.data as deleteOrderResponse;
  }

  /**
   * @description: 同意撥款或要求修正某個工程款
   */
  static async acceptingProcess(
    requestData: CustomerAcceptingProcessRequest
  ): Promise<CustomerGetOneConstructionOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Construction/Process/Approve', requestData);
    return response.data as CustomerGetOneConstructionOrderDetailResponse;
  }
}

export class CustomerMeasureOrderDetailService {
  /**
   * @description: 取得所有空間丈量訂單基本內容
   */
  static async getManyOrderDetail(
    requestData: CustomerGetManyOrderDetailRequest
  ): Promise<CustomerGetManyMeasureOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Get/Many/Detail', requestData);
    return response.data as CustomerGetManyMeasureOrderDetailResponse;
  }

  /**
   * @description: 取得單一空間丈量訂單詳細內容
   */
  static async getOneOrderDetail(
    requestData: CustomerGetOneOrderDetailRequest
  ): Promise<CustomerGetOneMeasureOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Measure/Get/One/Detail', requestData);
    return response.data as CustomerGetOneMeasureOrderDetailResponse;
  }
}

export class CustomerDesignOrderDetailService {
  /**
   * @description: 取得所有室內設計訂單基本內容
   */
  static async getManyOrderDetail(
    requestData: CustomerGetManyOrderDetailRequest
  ): Promise<CustomerGetManyDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Get/Many/Detail', requestData);
    return response.data as CustomerGetManyDesignOrderDetailResponse;
  }

  /**
   * @description: 取得單一室內設計訂單詳細內容
   */
  static async getOneOrderDetail(
    requestData: CustomerGetOneOrderDetailRequest
  ): Promise<CustomerGetOneDesignOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Design/Get/One/Detail', requestData);
    return response.data as CustomerGetOneDesignOrderDetailResponse;
  }
}

export class CustomerConstructionOrderDetailService {
  /**
   * @description: 取得所有裝潢施工訂單基本內容
   */
  static async getManyOrderDetail(
    requestData: CustomerGetManyOrderDetailRequest
  ): Promise<CustomerGetManyConstructionOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Construction/Get/Many/Detail', requestData);
    return response.data as CustomerGetManyConstructionOrderDetailResponse;
  }

  /**
   * @description: 取得單一裝潢施工訂單詳細內容
   */
  static async getOneOrderDetail(
    requestData: CustomerGetOneOrderDetailRequest
  ): Promise<CustomerGetOneConstructionOrderDetailResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Construction/Get/One/Detail', requestData);
    return response.data as CustomerGetOneConstructionOrderDetailResponse;
  }
}

export class CustomerInvoiceDetailService {
  /**
   * @description: 取得所有發票基本資料
   */
  static async getManyInvoice(requestData: CustomerGetManyOrderDetailRequest): Promise<CustomerBasicInvoiceResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Invoice/Get/Many/Basic', requestData);
    return response.data as CustomerBasicInvoiceResponse;
  }

  /**
   * @description: 取得單一發票基本資料
   */
  static async getOneInvoice(requestData: CustomerGetOneInvoiceRequest): Promise<CustomerInvoiceResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Invoice/Get/One/Detail', requestData);
    return response.data as CustomerInvoiceResponse;
  }

  /**
   * @description: 填寫發票開立資訊
   */
  static async CompleteInfo(requestData: CompleteInvoiceInfoRequest): Promise<CustomerInvoiceResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/Invoice/CompleteInfo', requestData);
    return response.data as CustomerInvoiceResponse;
  }
}

export class CustomerRateService {
  /**
   * @description: 客戶撰寫訂單評價
   */
  static async RateOrder(requestData: RateRequest): Promise<RateResponse> {
    const response = await BaseApiService.Customer.post('/Order/Customer/OrderRating', requestData);
    return response.data as RateResponse;
  }
}
