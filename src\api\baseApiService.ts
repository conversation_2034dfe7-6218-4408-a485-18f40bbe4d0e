import axios, { AxiosError } from 'axios';
import type { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { toastError } from '@/utils/toastification.ts';
import { piniaStoreClean } from '@/utils/piniaStoreClean.ts';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

export class BaseApiService {
  private baseUrl = import.meta.env.VITE_API_URL;
  private readonly axiosInstance: AxiosInstance;
  private readonly _isDesigner: boolean;

  constructor(isDesigner: boolean) {
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json'
      },
      method: 'post',
      timeout: 20000 // 設定超時時間為 20 秒
    });
    this._isDesigner = isDesigner;
    this.setInterceptors();
  }

  private setInterceptors() {
    this.axiosInstance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        const userInfoStore = this._isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();
        config.headers.Authorization = userInfoStore.guestToken;
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );
    this.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse) => {
        if (response.data.status === APIStatusCodeEnum.TokenIllegal) {
          const userInfoStore = this._isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();
          piniaStoreClean(this._isDesigner);
          await userInfoStore.getGuestToken(); //重新取得Token 會觸發所有組件重載 要避免在onMounted中觸發未登入造成的TokenIllegal 不然有機會無限呼叫
          return response;
        }
        if (response.data.status === APIStatusCodeEnum.Unknown) {
          toastError('呼叫API發生未知的錯誤');
          return response;
        }
        return response;
      },
      async (error: AxiosError) => {
        if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
          toastError('API 請求逾時');
        }
        return Promise.reject(error);
      }
    );
  }

  post(url: string, data = {}) {
    return this.axiosInstance.post(url, data);
  }
}

export default {
  Customer: new BaseApiService(false),
  Designer: new BaseApiService(true)
};