import BaseApiService from './baseApiService.ts';
import type { ManyUnacceptedOrderCardResponse } from '@/model/response/orderCard/manyOrderCardResponse.ts';
import type { getManyOrderCardRequest } from '@/model/request/orderCard/manyOrderCardRequest.ts';
import {
  ManyAcceptedOrderCardResponse, ManyHistoricalOrderCardResponse,
  ManyQuotingOrderCardResponse
} from '@/model/response/orderCard/manyOrderCardResponse.ts';
import { orderDetailRequest } from '@/model/request/orderDetail/orderDetailRequest.ts';
import { DesignOrderItemEnum} from '@/model/enum/orderUpdateType.ts';
import {
  ConstructionOrderAcceptedDetailResponse,
  DesignOrderAcceptedDetailResponse,
  MeasureOrderAcceptedDetailResponse
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import {
  AddHousePhotosItemRequest,
  AddTextItemRequest,
  DeleteItemRequest,
  UpdateImagePdfCadContentRequest,
  UpdateTextContentRequest,
  UpdateImageVideoContentRequest,
  UpdateSetUnitRequest,
} from '@/model/request/orderDetail/measureDataUpdateRequest.ts';
import {
  ConstructionDataUpdateResponse,
  DesignDataUpdateResponse,
  MeasureDataUpdateResponse
} from '@/model/response/orderDetail/dataUpdateResponse.ts';
import {
  Add2DItemRequest,
  Add3DItemRequest,
  FinishWorkRequest,
  Update2DRequest,
  Update3DRequest, UpdateConstructionFeeRequest
} from '@/model/request/orderDetail/designDataUpdateRequest.ts';
import {
  FinishProcessRequest,
  UpdateProcessRequest
} from '@/model/request/orderDetail/constructionDataUpdateRequest.ts';
import {
  ConstructionOrderUnacceptedDetailResponse,
  DesignOrderUnacceptedDetailResponse, MeasureOrderUnacceptedDetailResponse
} from '@/model/response/orderDetail/orderUnacceptedDetailResponse.ts';
import {
  CancelQuoteOrRejectOrderRequest,
  DesignOrderQuoteRequest
} from '@/model/request/orderDetail/designQuoteRequest.ts';
import {
  CancelQuoteOrRejectOrderResponse, MeasureOrderOperateResponse,
  OperateAppendProcessResponse
} from '@/model/response/orderDetail/orderOperateResponse.ts';
import {
  CancelAppendProcessRequest,
  ConstructionOrderQuoteDetailRequest,
  ConstructionOrderQuoteRequest, SetAppendProcessRequest
} from '@/model/request/orderDetail/constructionQuoteRequest.ts';
import {
  MeasureOrderAcceptRequest,
  MeasureOrderOperateRequest
} from '@/model/request/orderDetail/measureOrderRequest.ts';

export class OrderCardService {
  /**
   * @description: 取得所有 "未接單"訂單卡片資訊
   */
  static async getManyUnaccepted(requestData: getManyOrderCardRequest): Promise<ManyUnacceptedOrderCardResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Get/Many/Unaccepted'), requestData);
    return response.data as ManyUnacceptedOrderCardResponse;
  }

  /**
   * @description: 取得所有 "報價中"訂單卡片資訊
   */
  static async getManyQuoting(requestData: getManyOrderCardRequest): Promise<ManyQuotingOrderCardResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Get/Many/Quoting'), requestData);
    return response.data as ManyQuotingOrderCardResponse;
  }

  /**
   * @description: 取得所有 "已接單"訂單卡片資訊
   */
  static async getManyAccepted(requestData: getManyOrderCardRequest): Promise<ManyAcceptedOrderCardResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Get/Many/Accepted'), requestData);
    return response.data as ManyAcceptedOrderCardResponse;
  }

  /**
   * @description: 取得所有 "訂單歷史"訂單卡片資訊
   */
  static async getManyHistorical(requestData: getManyOrderCardRequest): Promise<ManyHistoricalOrderCardResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Get/Many/Historical'), requestData);
    return response.data as ManyHistoricalOrderCardResponse;
  }
}

export class OrderDetailService {
  /**
   * @description: 取得 Step1 單一"已接單"或"訂單歷史"的完整資訊
   */
  static async getMeasureAccepted(requestData: orderDetailRequest): Promise<MeasureOrderAcceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Get/Detail/Accepted'), requestData);
    return response.data as MeasureOrderAcceptedDetailResponse;
  }

  /**
   * @description: 取得 Step2 單一"已接單"或"訂單歷史"的完整資訊
   */
  static async getDesignAccepted(requestData: orderDetailRequest): Promise<DesignOrderAcceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Get/Detail/Accepted'), requestData);
    return response.data as DesignOrderAcceptedDetailResponse;
  }

  /**
   * @description: 取得 Step3 單一"已接單"或"訂單歷史"的完整資訊
   */
  static async getConstructionAccepted(requestData: orderDetailRequest): Promise<ConstructionOrderAcceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Get/Detail/Accepted'), requestData);
    return response.data as ConstructionOrderAcceptedDetailResponse;
  }

  /**
   * @description: 取得 Step1 單一"未接單"的完整資訊 Stpe1 沒有"報價中"
   */
  static async getMeasureUnaccepted(requestData: orderDetailRequest): Promise<MeasureOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Get/Detail/Unaccepted'), requestData);
    return response.data as MeasureOrderUnacceptedDetailResponse;
  }

  /**
   * @description: 取得 Step2 單一"未接單"或"報價中"的完整資訊
   */
  static async getDesignUnaccepted(requestData: orderDetailRequest): Promise<DesignOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Get/Detail/Unaccepted'), requestData);
    return response.data as DesignOrderUnacceptedDetailResponse;
  }

  /**
   * @description: 取得 Step3 單一"未接單"或"報價中"的完整資訊
   */
  static async getConstructionUnaccepted(requestData: orderDetailRequest): Promise<ConstructionOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Get/Detail/Unaccepted'), requestData);
    return response.data as ConstructionOrderUnacceptedDetailResponse;
  }
}

export class MeasureOrderDetailService {
  /**
   * @description: 接受訂單
   */
  static async AcceptOrder(requestData: MeasureOrderAcceptRequest): Promise<MeasureOrderAcceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Accept'), requestData);
    return response.data as MeasureOrderAcceptedDetailResponse;
  }

  /**
   * @description: 拒絕訂單
   */
  static async RejectOrder(requestData: MeasureOrderOperateRequest): Promise<MeasureOrderOperateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Reject'), requestData);
    return response.data as MeasureOrderOperateResponse;
  }

  /**
   * @description: 完成訂單
   */
  static async FinishOrder(requestData: MeasureOrderOperateRequest): Promise<MeasureOrderOperateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Finish'), requestData);
    return response.data as MeasureOrderOperateResponse;
  }

  /**
   * @description: 取消訂單
   */
  static async DeleteOrder(requestData: MeasureOrderOperateRequest): Promise<MeasureOrderOperateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Delete'), requestData);
    return response.data as MeasureOrderOperateResponse;
  }

  /**
   * @description: 新增 Step1 訂單的 "房屋資訊"、"屋況紀錄(非專業技師)"、"丈量規劃"、"房屋/屋主備註"資料欄位 API路徑會跟著項目變動
   */
  static async addOtherMeasureDataItem(requestData: AddTextItemRequest, contentName: string): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post((`/Order/Designer/Measure/Update/Push/${contentName}`), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 新增 Step1 訂單的"房屋照片"資料欄位
   */
  static async addHousePhotosItem(requestData: AddHousePhotosItemRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Push/Photos'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 新增、更新、刪除 Step1 訂單的 "房屋資訊"、"水質檢測"等等的資料欄位單位 API路徑會跟著項目變動
   */
  static async updateSetUnit(requestData: UpdateSetUnitRequest, contentName: string): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post((`/Order/Designer/Measure/Update/Set/Unit/${contentName}`), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單TextContent類的通用上傳方法
   */
  static async updateTextContent(requestData: UpdateTextContentRequest, contentName: string): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post((`/Order/Designer/Measure/Update/Set/${contentName}`), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單的房屋資訊 預計刪除
   */
  static async updateHouseInfo(requestData: UpdateTextContentRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Set/HouseInfo'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單的房屋照片
   */
  static async updateHousePhotos(requestData: UpdateImageVideoContentRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Set/Photos'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單的屋況紀錄(非專業技師)
   */
  static async updateHouseCheck(requestData: UpdateImageVideoContentRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Set/HouseCheck'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單的丈量規劃
   */
  static async updateFloorPlan(requestData: UpdateImagePdfCadContentRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Set/FloorPlan'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 更新 Step1 訂單的房屋/屋主備註
   */
  static async updateNote(requestData: UpdateTextContentRequest): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Measure/Update/Set/Note'), requestData);
    return response.data as MeasureDataUpdateResponse;
  }

  /**
   * @description: 移除 Step1 訂單的項目的資料欄位 API路徑會跟著項目變動
   */
  static async deleteItem(requestData: DeleteItemRequest, contentName: string): Promise<MeasureDataUpdateResponse> {
    const response = await BaseApiService.Designer.post((`/Order/Designer/Measure/Update/Pull/${contentName}`), requestData);
    return response.data as MeasureDataUpdateResponse;
  }
}

export class DesignOrderDetailService {
  /**
   * @description: 對某訂單進行報價
   */
  static async quote(requestData: DesignOrderQuoteRequest): Promise<DesignOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Quote'), requestData);
    return response.data as DesignOrderUnacceptedDetailResponse;
  }

  /**
   * @description: 對某訂單取消報價
   */
  static async quoteCancel(requestData: CancelQuoteOrRejectOrderRequest): Promise<CancelQuoteOrRejectOrderResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Quote/Cancel'), requestData);
    return response.data as CancelQuoteOrRejectOrderResponse;
  }

  /**
   * @description: 設計師拒接某訂單
   */
  static async rejectOrder(requestData: CancelQuoteOrRejectOrderRequest): Promise<CancelQuoteOrRejectOrderResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Reject'), requestData);
    return response.data as CancelQuoteOrRejectOrderResponse;
  }

  /**
   * @description: 增加「2D室內設計」欄位
   */
  static async add2DItem(requestData: Add2DItemRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Push/Design2D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 增加「3D模型設計」欄位
   */
  static async add3DItem(requestData: Add3DItemRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Push/Design3D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 上傳「2D室內設計」資料
   */
  static async update2D(requestData: Update2DRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Set/Design2D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 上傳「3D模型設計」資料
   */
  static async update3D(requestData: Update3DRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Set/Design3D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 移除欄位
   */
  static async deleteItem(requestData: DeleteItemRequest, itemName: DesignOrderItemEnum): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post((`/Order/Designer/Design/Update/Pull/${itemName}`), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 完成「2D室內設計」並請款
   */
  static async finish2D(requestData: FinishWorkRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Finish/Design2D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 完成「3D室內設計」並請款
   */
  static async finish3D(requestData: FinishWorkRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Finish/Design3D'), requestData);
    return response.data as DesignDataUpdateResponse;
  }

  /**
   * @description: 上傳裝潢施工報價
   */
  static async updateConstructionFee(requestData: UpdateConstructionFeeRequest): Promise<DesignDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Design/Update/Set/ConstructionFee'), requestData);
    return response.data as DesignDataUpdateResponse;
  }
}

export class ConstructionOrderDetailService {
  /**
   * @description: 對某訂單進行初步報價
   */
  static async quote(requestData: ConstructionOrderQuoteRequest): Promise<ConstructionOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Quote'), requestData);
    return response.data as ConstructionOrderUnacceptedDetailResponse;
  }

  /**
   * @description: 對某訂單進行詳細報價
   */
  static async quoteDetail(requestData: ConstructionOrderQuoteDetailRequest): Promise<ConstructionOrderUnacceptedDetailResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/QuoteDetail'), requestData);
    return response.data as ConstructionOrderUnacceptedDetailResponse;
  }

  /**
   * @description: 對某訂單取消初步報價
   */
  static async quoteCancel(requestData: CancelQuoteOrRejectOrderRequest): Promise<CancelQuoteOrRejectOrderResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Quote/Cancel'), requestData);
    return response.data as CancelQuoteOrRejectOrderResponse;
  }

  /**
   * @description: 設計師拒接某訂單
   */
  static async rejectOrder(requestData: CancelQuoteOrRejectOrderRequest): Promise<CancelQuoteOrRejectOrderResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Reject'), requestData);
    return response.data as CancelQuoteOrRejectOrderResponse;
  }

  /**
   * @description: 新增或修改追加流程
   */
  static async setAppendProcess(requestData: SetAppendProcessRequest): Promise<OperateAppendProcessResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/AppendProcess/Set'), requestData);
    return response.data as OperateAppendProcessResponse;
  }

  /**
   * @description: 取消追加流程
   */
  static async CancelAppendProcess(requestData: CancelAppendProcessRequest): Promise<OperateAppendProcessResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/AppendProcess/Cancel'), requestData);
    return response.data as OperateAppendProcessResponse;
  }

  /**
   * @description: 更新上傳工程款資訊
   */
  static async updateProcess(requestData: UpdateProcessRequest): Promise<ConstructionDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Update/Set/Process'), requestData);
    return response.data as ConstructionDataUpdateResponse;
  }

  /**
   * @description: 完成工程款並請款
   */
  static async finishProcess(requestData: FinishProcessRequest): Promise<ConstructionDataUpdateResponse> {
    const response = await BaseApiService.Designer.post(('/Order/Designer/Construction/Update/Finish/Process'), requestData);
    return response.data as ConstructionDataUpdateResponse;
  }
}
