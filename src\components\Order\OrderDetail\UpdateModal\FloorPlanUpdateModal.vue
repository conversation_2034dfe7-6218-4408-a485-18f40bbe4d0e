<script setup lang="ts">
import { ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import FloorPlanModalContent
  from '@/components/Order/OrderDetail/UpdateModal/ModalContent/FloorPlanModalContent.vue';
import { ImagePdfCadKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps<{ title: string; orderId: string; }>();
const floorPlan = defineModel<ImagePdfCadKeyContent>({ required: true });
const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const isApiLoading = ref(false);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  if (isApiLoading.value) {
    toastInfo('請等待檔案上傳完成');
  } else {
    updateModalRef.value?.closeModal();
  }
};

const changeApiLoading = (isLoading: boolean) => {
  isApiLoading.value = isLoading;
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" ref="updateModalRef"
                :click-outside-close="false"
                :showCloseButton="true"
                @closeModal="closeModal">
    <FloorPlanModalContent v-model="floorPlan" :orderId="props.orderId" @change-api-loading="changeApiLoading" />
  </DefaultModal>
</template>
