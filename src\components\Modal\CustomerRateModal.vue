<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { computed, ref } from 'vue';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import { RateRequest } from '@/model/request/RateRequest.ts';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import { toastSuccess, toastWarning } from '@/utils/toastification.ts';
import { CustomerRateService } from '@/api/customerOrder.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const props = defineProps<{
  deisgnerAvatar: string;
  deisgnerName: string;
  orderType: OrderTypeEnum;
  orderRatingId: string;
}>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const rate = ref<RateRequest>({
  orderRatingId: '',
  comment: '',
  measureRating: {
    arrivalOnTime: 0,
    serviceAttitude: 0,
    professionalQuality: 0,
    completionTime: 0
  },
  designRating: {
    workQuality: 0,
    enthusiasticAttitude: 0,
    designEfficiency: 0
  },
  constructionRating: {
    constructionQuality: 0,
    serviceAttitude: 0,
    completionTime: 0
  }
});

const openModal = () => {
  ModalRef.value?.openModal();
};

const sendRate = async () => {
  rate.value.orderRatingId = props.orderRatingId;
  switch (props.orderType) {
    case OrderTypeEnum.Measure:
      if (rate.value.measureRating.arrivalOnTime === 0 || rate.value.measureRating.serviceAttitude === 0 ||
        rate.value.measureRating.professionalQuality === 0 || rate.value.measureRating.completionTime === 0) {
        toastWarning('請填寫評價');
        return;
      }
      break;
    case OrderTypeEnum.Design:
      if (rate.value.designRating.workQuality === 0 || rate.value.designRating.enthusiasticAttitude === 0 ||
        rate.value.designRating.designEfficiency === 0) {
        toastWarning('請填寫評價');
        return;
      }
      break;
    case OrderTypeEnum.Construction:
      if (rate.value.constructionRating.constructionQuality === 0 || rate.value.constructionRating.serviceAttitude === 0 ||
        rate.value.constructionRating.completionTime === 0) {
        toastWarning('請填寫評價');
        return;
      }
      break;
    default:
      break;
  }
  const res = await CustomerRateService.RateOrder(rate.value);
  if (res.status === APIStatusCodeEnum.Success) {
    toastSuccess('評價成功');
    ModalRef.value?.closeModal();
  }
};

// const orderTypeName = computed(() => {
//   switch (props.orderType) {
//     case OrderTypeEnum.Measure:
//       return '空間丈量';
//     case OrderTypeEnum.Design:
//       return '室內設計';
//     case OrderTypeEnum.Construction:
//       return '裝潢施工';
//     default:
//       return '';
//   }
// });
//
// const rateTypeName = computed(() => {
//   switch (props.orderType) {
//     case OrderTypeEnum.Measure:
//       return '準時抵達、服務態度、專業品質、完成時間';
//     case OrderTypeEnum.Design:
//       return '作品質量、態度熱忱、設計效率';
//     case OrderTypeEnum.Construction:
//       return '施工品質、服務態度、完工時間';
//     default:
//       return '';
//   }
// });

const teamName = computed(() => {
  switch (props.orderType) {
    case OrderTypeEnum.Measure:
      return '丈量師';
    case OrderTypeEnum.Design:
      return '設計師';
    case OrderTypeEnum.Construction:
      return '裝潢團隊';
    default:
      return '';
  }
});

const placeHolder = computed(() => {
  const Fullstar = rate.value.measureRating.arrivalOnTime + rate.value.measureRating.serviceAttitude +
    rate.value.measureRating.professionalQuality + rate.value.measureRating.completionTime === 20
    || rate.value.designRating.workQuality + rate.value.designRating.enthusiasticAttitude + rate.value.designRating.designEfficiency === 15
    || rate.value.constructionRating.constructionQuality + rate.value.constructionRating.serviceAttitude + rate.value.constructionRating.completionTime === 15;

  if (Fullstar) {
    return '（選填）請在此填寫您的評價';
  } else {
    return '您的評價對裝潢團隊非常重要，若您有不愉快的經驗，請請在評論區具體描述您的理由。';
  }
});

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="填寫評價" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-lg"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col gap-2 items-center text-black p-2 divide-y divide-gray-300">
      <div class="flex items-center justify-start gap-x-2 w-full">
        <img v-if="props.deisgnerAvatar" class="w-16 h-16 rounded-full"
             :src="props.deisgnerAvatar" alt="designerAvatar" />
        <UserCircleIcon v-else class="w-16 h-16 rounded-full" />
        <div class="flex flex-col gap-y-2 items-start">
          <p>{{ deisgnerName }}</p>
          <p class="text-sm">{{ teamName }}</p>
        </div>
      </div>

      <template v-if="orderType === OrderTypeEnum.Measure">
        <div class="flex flex-col gap-4 w-full items-start p-2">
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">準時抵達</p>
            <img v-for="star in rate.measureRating.arrivalOnTime" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.arrivalOnTime = star" />
            <img v-for="nonStar in ( 5 - rate.measureRating.arrivalOnTime)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.arrivalOnTime = rate.measureRating.arrivalOnTime + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">服務態度</p>
            <img v-for="star in rate.measureRating.serviceAttitude" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.serviceAttitude = star" />
            <img v-for="nonStar in ( 5 - rate.measureRating.serviceAttitude)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.serviceAttitude = rate.measureRating.serviceAttitude + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">專業品質</p>
            <img v-for="star in rate.measureRating.professionalQuality" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.professionalQuality = star" />
            <img v-for="nonStar in ( 5 - rate.measureRating.professionalQuality)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.professionalQuality = rate.measureRating.professionalQuality + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">完成時間</p>
            <img v-for="star in rate.measureRating.completionTime" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.completionTime = star" />
            <img v-for="nonStar in ( 5 - rate.measureRating.completionTime)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.measureRating.completionTime = rate.measureRating.completionTime + nonStar" />
          </div>
        </div>
      </template>

      <template v-if="orderType === OrderTypeEnum.Design">
        <div class="flex flex-col gap-2 w-full items-start p-2">
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">作品質量</p>
            <img v-for="star in rate.designRating.workQuality" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.workQuality = star" />
            <img v-for="nonStar in (5 - rate.designRating.workQuality)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.workQuality = rate.designRating.workQuality + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">態度熱忱</p>
            <img v-for="star in rate.designRating.enthusiasticAttitude" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.enthusiasticAttitude = star" />
            <img v-for="nonStar in (5 - rate.designRating.enthusiasticAttitude)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.enthusiasticAttitude = rate.designRating.enthusiasticAttitude + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">設計效率</p>
            <img v-for="star in rate.designRating.designEfficiency" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.designEfficiency = star" />
            <img v-for="nonStar in (5 - rate.designRating.designEfficiency)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.designRating.designEfficiency = rate.designRating.designEfficiency + nonStar" />
          </div>
        </div>
      </template>

      <template v-if="orderType === OrderTypeEnum.Construction">
        <div class="flex flex-col gap-2 w-full items-start p-2">
          <div class="flex gap-x-0.5 items-center  max-md:justify-center">
            <p class="mr-2">施工品質</p>
            <img v-for="star in rate.constructionRating.constructionQuality" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.constructionQuality = star" />
            <img v-for="nonStar in ( 5 - rate.constructionRating.constructionQuality)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.constructionQuality = rate.constructionRating.constructionQuality + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">服務態度</p>
            <img v-for="star in rate.constructionRating.serviceAttitude" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.serviceAttitude = star" />
            <img v-for="nonStar in ( 5 - rate.constructionRating.serviceAttitude)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.serviceAttitude = rate.constructionRating.serviceAttitude + nonStar" />
          </div>
          <div class="flex gap-x-0.5 items-center max-md:justify-center">
            <p class="mr-2">完工時間</p>
            <img v-for="star in rate.constructionRating.completionTime" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.completionTime = star" />
            <img v-for="nonStar in ( 5 - rate.constructionRating.completionTime)" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-8 h-8 cursor-pointer"
                 @click="rate.constructionRating.completionTime = rate.constructionRating.completionTime + nonStar" />
          </div>
        </div>
      </template>

      <div class="flex flex-col gap-2 items-start w-full p-2">
        <p>您的評論</p>
        <label for="about" class="sr-only">workInfo</label>
        <div class="text-start w-full">
                          <textarea id="description" name="description" rows="4" :placeholder="placeHolder"
                                    v-model="rate.comment"
                                    class="resize-none input-basic rounded-md input-focus" />
        </div>
      </div>

      <!--      <div class="flex flex-col p-2 items-start text-start text-sm">-->
      <!--        <p>謝謝您完成<span>{{ deisgnerName }}</span>的<span>{{ orderTypeName }}</span>服務。</p>-->
      <!--        <p>我們邀請您針對<span>{{ rateTypeName }}</span>做評價！</p>-->
      <!--        <p>評價送出後無法再修改，您的評價對<span>{{ teamName }}</span>會有非常重要的影響，</p>-->
      <!--        <p>建議您用客觀公正的方式評價</p>-->
      <!--      </div>-->
      <div class="p-2 w-full">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80"
                @click="sendRate">送出評價
        </button>
      </div>

    </div>
  </DefaultModal>
</template>
