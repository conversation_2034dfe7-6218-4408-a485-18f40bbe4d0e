import { createApp } from 'vue';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import Toast, { PluginOptions } from 'vue-toastification';
import { register } from 'swiper/element/bundle';
import App from './App.vue';
import router from './router';
import 'vue-toastification/dist/index.css';
import '@/assets/css/tailwind.css';
import '@/assets/css/mySwiper.css';

const app = createApp(App);
const pinia = createPinia();
const ToastOptions: PluginOptions = {
  transition: 'Vue-Toastification__fade',
  maxToasts: 3,
  newestOnTop: true,
  toastClassName: 'hez', // 自訂CSS樣式 要看去 tailwind.css 看
};

pinia.use(piniaPluginPersistedstate);

app.use(pinia);
app.use(router);
app.use(Toast, ToastOptions);
app.use(register);
app.mount('#app');
