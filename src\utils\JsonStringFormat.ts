import type { SetText } from '@/model/general/JsonString';

export const JSONStringToObject = (jsonString: string): SetText => {
  const setTextData: SetText = JSON.parse(jsonString);
  return {
    mode: setTextData.mode,
    content: setTextData.content
  };
};

export const ObjectToJSONString = (object: {
  content: { hint: string, text: string, unit?: string , option?: string},
  mode: string
}, houseTypeInput?: string): string => {
  if (houseTypeInput !== undefined) {
    object.content.text = houseTypeInput;
  }
  if (object.mode === 'selectWithText') {
    object.content.option = undefined;
  }
  return JSON.stringify(object);
};

export const parseJsonString = (item: { updateTime: string, name: string, text: string, key: string }) => {
  const JsonStringContent = JSONStringToObject(item.text);
  return {
    updateTime: item.updateTime,
    name: item.name,
    text: {
      content: {
        hint: JsonStringContent.content.hint,
        text: JsonStringContent.content.text,
        ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
      },
      mode: JsonStringContent.mode
    }
  };
};