<script setup lang="ts">
import { CustomerTextContent } from '@/model/response/customerMeasureOrderResponse.ts';
import { getStyleConfig } from '@/model/general/textContentStyleConfig.ts';
import { computed } from 'vue';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';
import HezDivider from '@/components/General/HezDivider.vue';

const props = defineProps<{
  title: string;
  subTitle?: string;
  data: CustomerTextContent;
}>();

const contentData = computed(() => {
  return props.data.content.map((item) => {
    const JsonStringContent = JSONStringToObject(item.text);
    return {
      updateTime: item.updateTime,
      name: item.name,
      text: {
        content: {
          hint: JsonStringContent.content.hint,
          text: JsonStringContent.content.text,
          ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
        },
        mode: JsonStringContent.mode
      }
    };
  });
});
</script>

<template>
  <div class="cus-border my-1 flex flex-col gap-4">
    <div class="flex items-center justify-center space-x-2 text-2xl">
      <p class="text-color-primary text-center font-bold">{{ title }}</p>
      <p class="text-color-primary text-center text-xl font-bold">{{ subTitle }}</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div class="flex flex-col">
        <div
          v-for="(infoData, index) in contentData"
          :key="index"
          class="text-color-primary mb-4 flex justify-start text-lg"
        >
          <div v-if="infoData.name !== '噪音評估' && infoData.name !== '濕度評估'" class="flex gap-x-0.5 md:gap-x-2">
            <p class="break-all font-bold" v-html="getStyleConfig('name', infoData.name, infoData.name, false)"></p>
            <div v-if="infoData.text.content.text === ''" class="flex">
              <p class="break-all font-bold text-black">(丈量師未上傳資料)</p>
            </div>
            <div v-else class="flex">
              <p
                class="break-all font-bold text-black"
                v-html="getStyleConfig('text', infoData.name, infoData.text.content.text, false)"
              ></p>
              <p
                v-if="infoData.text.content.unit"
                class="font-bold text-black"
                v-html="getStyleConfig('unit', infoData.name, infoData.text.content.unit, false)"
              ></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
