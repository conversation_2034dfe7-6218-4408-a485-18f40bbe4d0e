<script setup lang="ts">
import { TextKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { computed, ref } from 'vue';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';
import HouseInfoUpdateModal from '@/components/Order/OrderDetail/UpdateModal/HouseInfoUpdateModal.vue';
import Hez<PERSON><PERSON>ider from '@/components/General/HezDivider.vue';

const houseInfo = defineModel<TextKeyContent>({ required: true });
const props = defineProps<{
  orderId: string;
  disabled: boolean;
}>();
const updateModalRef = ref<InstanceType<typeof HouseInfoUpdateModal> | null>(null);

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const houseInfoData = computed(() => {
  let infoData: {
    key: string,
    updateTime: string,
    name: string,
    text: { content: { hint: string, text: string, unit?: string }, mode: string }
  }[] = [];
  houseInfo.value.content.forEach((item) => {
    const JsonStringContent = JSONStringToObject(item.text);
    infoData.push({
      key: item.key,
      updateTime: item.updateTime,
      name: item.name,
      text: {
        content: {
          hint: JsonStringContent.content.hint,
          text: JsonStringContent.content.text,
          ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
        },
        mode: JsonStringContent.mode
      }
    });
  });
  return infoData;
});
</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">房屋資訊</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div class="flex flex-col gap-y-2">
        <div v-for="infoData in houseInfoData" :key="infoData.key"
             class="flex justify-start text-lg text-color-primary">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold break-all p-2">{{ infoData.name }}</p>
            <div v-if="infoData.text.content.text === '' " class="flex">
              <p class="break-all">未上傳</p>
            </div>
            <div v-else class="flex items-center">
              <p class="font-bold break-all">{{ infoData.text.content.text }}</p>
              <p class="font-bold">{{ infoData.text.content.unit }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="w-full cus-btn button-padding text-xl"
            v-if="!disabled" @click="openUpdateModal()">
      資料上傳/修改
    </button>
  </div>
  <HouseInfoUpdateModal v-model="houseInfo" title="房屋資訊" :orderId="props.orderId" ref="updateModalRef" />
</template>
