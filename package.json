{"name": "homeeasy-web", "version": "3.0.0 Alpha", "private": true, "type": "module", "scripts": {"postinstallIOS:dev": "shx cp public/.well-known/apple-app-site-association-development.json public/.well-known/apple-app-site-association", "postinstallIOS:prod": "shx cp public/.well-known/apple-app-site-association-production.json public/.well-known/apple-app-site-association", "postinstall:dev": "shx cp public/.well-known/assetlinks.development.json public/.well-known/assetlinks.json", "postinstall:prod": "shx cp public/.well-known/assetlinks.production.json public/.well-known/assetlinks.json", "dev:dev": "yarn postinstall:dev && yarn postinstallIOS:dev && vite", "dev:prod": "yarn postinstall:prod && yarn postinstallIOS:prod && vite", "dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:test": "vue-tsc && vite build --mode test", "build:dev": "yarn postinstall:dev && yarn postinstallIOS:dev && vue-tsc && vite build --mode development", "build:prod": "yarn postinstall:prod && yarn postinstallIOS:prod && vue-tsc && vite build --mode production", "build-only": "vite build", "preview": "vite preview", "start:test": "yarn build:test && yarn preview", "start:dev": "yarn build:dev && yarn preview", "start:prod": "yarn build:prod && yarn preview", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@aws-sdk/client-cognito-identity": "^3.504.0", "@aws-sdk/client-s3": "^3.504.0", "@aws-sdk/credential-provider-cognito-identity": "^3.504.0", "@aws-sdk/s3-request-presigner": "^3.504.0", "@ffmpeg/ffmpeg": "^0.12.10", "@headlessui/vue": "^1.7.19", "@heroicons/vue": "^2.1.1", "@microsoft/signalr": "^8.0.0", "@tailwindcss/forms": "^0.5.7", "@vuepic/vue-datepicker": "^8.6.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.5", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "fs": "^0.0.1-security", "livekit-client": "^2.3.0", "path": "^0.12.7", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "puppeteer": "^24.15.0", "shx": "^0.3.4", "swiper": "^11.0.7", "uuid": "^9.0.1", "vue": "^3.4.19", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5", "vue3-google-map": "^0.20.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/crypto-js": "^4.2.2", "@types/node": "^18.19.3", "@types/uuid": "^9.0.8", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.18", "eslint": "^8.49.0", "eslint-plugin-tailwindcss": "^3.14.3", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.35", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "~5.3.0", "vite": "5.1.4", "vue-tsc": "^1.8.25"}}