<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import HezStep from '@/components/customer/HezStep.vue';
import { useStep1PublishTitleStore } from '@/stores/customerGlobal.ts';

const router = useRouter();
const selcted = ref<string>('step1');
const titleStore = useStep1PublishTitleStore();

const goToMeasurePublish = () => {
  router.push({
    name: 'customermeasurepublish'
  });
};
const nextStep = () => {
  if (selcted.value === 'step1') {
    titleStore.setTitle('', '');
  } else if (selcted.value === 'step2') {
    titleStore.setTitle('為順利的完成室內設計服務', '我們需要派人到府空間丈量');
  } else if (selcted.value === 'step3') {
    titleStore.setTitle('為順利的完成裝潢施工服務', '我們需要派人到府空間丈量');
  }
  goToMeasurePublish();
};
</script>

<template>
  <div class="w-full">
    <p class="my-8 font-bold text-2xl text-center">您需要的服務項目</p>
    <div class="flex flex-col w-full cus-border md:py-14 gap-y-4 ">
      <div class="w-full flex flex-col space-y-4">
        <label for="step1" class="cus-border cursor-pointer flex items-center px-4 py-2 gap-2">
          <input
            id="step1"
            name="service"
            type="radio"
            value="step1"
            v-model="selcted"
            class="h-4 w-4 radio-btn"
          />
          <span class="text-lg text-nowrap">到府空間丈量服務</span>
        </label>
        <label for="step2" class="cus-border cursor-pointer flex items-center px-4 py-2 gap-2">
          <input
            id="step2"
            name="service"
            type="radio"
            value="step2"
            v-model="selcted"
            class="h-4 w-4 radio-btn"
          />
          <span class="text-lg text-nowrap">室內設計</span>
        </label>
        <label for="step3" class="cus-border cursor-pointer flex items-center px-4 py-2 gap-2">
          <input
            id="step3"
            name="service"
            type="radio"
            value="step3"
            v-model="selcted"
            class="h-4 w-4 radio-btn"
          />
          <span class="text-lg text-nowrap">裝潢施工</span>
        </label>
      </div>

<!--      <div v-if="selcted === 'step1'"></div>-->
      <div v-if="selcted === 'step2'">
        <div class="flex items-center max-md:flex-col gap-2">
          <p class="text-lg text-nowrap">室內設計預算</p>
          <input type="text" class="cus-border px-4 py-2 w-full focus-border-color" placeholder="請輸入室內設計預算">
        </div>
      </div>
      <div v-else-if="selcted === 'step3'">
        <div class="flex items-center max-md:flex-col gap-2">
          <p class="text-lg text-nowrap">裝潢施工預算</p>
          <input type="text" class="cus-border px-4 py-2 w-full focus-border-color" placeholder="請輸入裝潢施工預算">
        </div>
      </div>
      <div class="flex my-4">
        <button class="cus-btn w-full md:text-lg"
                @click="nextStep()">下一步
        </button>
      </div>
    </div>
    <HezStep />
  </div>
</template>
