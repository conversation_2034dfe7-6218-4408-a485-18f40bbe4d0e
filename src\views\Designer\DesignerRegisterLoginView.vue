<script setup lang="ts">

import DesignerRegisterLogin from '@/components/Designer/DesignerRegisterLogin.vue';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const designerInfoStore = useDesignerInfoStore();

onMounted(() => {
  if (designerInfoStore.loginState) router.push({ name: 'designerhome' });
});
</script>

<template>
  <DesignerRegisterLogin />
</template>
