<script setup lang="ts">
import {
  ImagePdfCadKeyContent,
  ImagePdfCadKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import FloorPlanUpdateModal from '@/components/Order/OrderDetail/UpdateModal/FloorPlanUpdateModal.vue';
import { ref } from 'vue';
import PDFCADImageMediaModal from '@/components/General/PDFCADImageMediaModal.vue';
import { downloadFile } from '@/utils/fileDownloader.ts';
import HezDivider from '@/components/General/HezDivider.vue';

const floorPlan = defineModel<ImagePdfCadKeyContent>({ required: true });
const updateModalRef = ref<InstanceType<typeof FloorPlanUpdateModal> | null>(null);
const PdfCadImageMediaModalRef = ref<InstanceType<typeof PDFCADImageMediaModal> | null>(null);

const props = defineProps<{
  orderId: string;
  disabled: boolean;
}>();

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const openMediaModal = (imagePdfCadItem: ImagePdfCadKeyItem) => {
  PdfCadImageMediaModalRef.value?.openMediaModal(imagePdfCadItem);
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">丈量規劃</p>
    </div>
    <HezDivider />

    <div class="flex justify-start">
      <div v-if="floorPlan.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col gap-y-2" v-else>
        <div v-for="floorPlanItem in floorPlan.content" :key="floorPlanItem.key"
             class="flex justify-start text-lg text-color-primary">
          <div class="flex md:gap-2 gap-1 items-center max-md:flex-wrap max-md:items-start">
            <p class="font-bold p-2">{{ floorPlanItem.name }}</p>
            <div class="flex max-md:flex-col gap-1">
              <div>
                <div v-if="floorPlanItem.images.length === 0" class="flex">
                  <p class="text-black">未上傳</p>
                </div>
                <div v-else class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="openMediaModal(floorPlanItem)">
                    查看圖片
                  </button>
                </div>
              </div>

              <div v-if="floorPlanItem.images.length !== 0">
                <div v-if="floorPlanItem.pdf.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(floorPlanItem.pdf.url)">
                    下載 PDF
                  </button>
                </div>
              </div>
              <div v-if="floorPlanItem.images.length !== 0">
                <div v-if="floorPlanItem.cad.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(floorPlanItem.cad.url)">
                    下載 CAD
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="w-full cus-btn button-padding text-xl"
            v-if="!disabled" @click="openUpdateModal()">
      資料上傳/修改
    </button>
  </div>
  <FloorPlanUpdateModal v-model="floorPlan" title="丈量規劃" :orderId="props.orderId" ref="updateModalRef" />
  <PDFCADImageMediaModal ref="PdfCadImageMediaModalRef" />
</template>
