<script setup lang="ts">
import {
  ImagePdfCadKeyContent,
  ImagePdfCadKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { computed, ref } from 'vue';
import PDFCADImageMediaModal from '@/components/General/PDFCADImageMediaModal.vue';
import { downloadFile } from '@/utils/fileDownloader.ts';
import Hez<PERSON><PERSON>ider from '@/components/General/HezDivider.vue';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import Update2DModal from '@/components/Order/OrderDetail/UpdateModal/Update2DModal.vue';

const design2D = defineModel<ImagePdfCadKeyContent>('design2D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus');
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus');

const emits = defineEmits(['refreshData']);
const updateModalRef = ref<InstanceType<typeof Update2DModal> | null>(null);
const PdfCadImageMediaModalRef = ref<InstanceType<typeof PDFCADImageMediaModal> | null>(null);

const props = defineProps<{
  orderId: string;
  disabled: boolean;
}>();

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const openMediaModal = (imagePdfCadItem: ImagePdfCadKeyItem) => {
  PdfCadImageMediaModalRef.value?.openMediaModal(imagePdfCadItem);
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

const subStatusForUpdate = computed(() => {
  if (subStatus.value) {
    return subStatus.value;
  } else {
    return DesignOrderSubStatusEnum.NotUploaded;
  }
});

const orderStatusForUpdate = computed(() => {
  if (orderStatus.value) {
    return orderStatus.value;
  } else {
    return DesignOrderStatusEnum.Comparing;
  }
});

defineExpose({ openUpdateModal });
</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">2D室內設計</p>
    </div>
    <HezDivider />

    <div class="flex justify-start">
      <div v-if="design2D.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col gap-y-2" v-else>
        <div v-for="ImagePdfCadItem in design2D.content" :key="ImagePdfCadItem.key"
             class="flex justify-start text-lg text-color-primary">
          <div class="flex md:gap-2 gap-1 items-center max-md:flex-wrap max-md:items-start">
            <p class="font-bold p-2">{{ ImagePdfCadItem.name }}</p>
            <div class="flex max-md:flex-col gap-1">
              <div>
                <div v-if="ImagePdfCadItem.images.length === 0" class="flex">
                  <p class="text-black">未上傳</p>
                </div>
                <div v-else class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="openMediaModal(ImagePdfCadItem)">
                    查看圖片
                  </button>
                </div>
              </div>

              <div v-if="ImagePdfCadItem.images.length !== 0">
                <div v-if="ImagePdfCadItem.pdf.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(ImagePdfCadItem.pdf.url)">
                    下載 PDF
                  </button>
                </div>
              </div>
              <div v-if="ImagePdfCadItem.images.length !== 0">
                <div v-if="ImagePdfCadItem.cad.url" class="flex">
                  <button class="cus-btn button-padding text-base text-nowrap"
                          @click="handleDownloadFile(ImagePdfCadItem.cad.url)">
                    下載 CAD
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="w-full cus-btn button-padding text-xl"
            v-if="!disabled" @click="openUpdateModal()">
      資料上傳/修改
    </button>
  </div>
  <Update2DModal title="2D室內設計"
                 :order-id="props.orderId"
                 v-model:design2-d="design2D"
                 v-model:sub-status="subStatusForUpdate"
                 v-model:order-status="orderStatusForUpdate"
                 ref="updateModalRef"
                 @refresh-data="emits('refreshData')" />
  <PDFCADImageMediaModal ref="PdfCadImageMediaModalRef" />
</template>
