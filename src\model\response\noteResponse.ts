import { BaseResponse } from '@/model/response/baseResponse.ts';

export interface GetMeetListResponse extends BaseResponse {
  meets: MeetRecordItem[];
}

export interface GetNoteListResponse extends BaseResponse {
  notes: NoteRecordItem[];
}

export interface GetImageListResponse extends BaseResponse {
  images: ImageRecordItem[];
}

export interface GetFileListResponse extends BaseResponse {
  files: FileRecordItem[];
}

export interface GetLinkListResponse extends BaseResponse {
  links: LinkRecordItem[];
}

export interface OneNoteResponse extends BaseResponse {
  note: NoteRecordItem;
}

export interface DeleteNoteResponse extends BaseResponse {
}

export interface GetCommentListResponse extends BaseResponse {
  comments: CommentItem[];
}

export interface AddCommentResponse extends BaseResponse {
  comment: CommentItem;
}

export interface DeleteCommentResponse extends BaseResponse {
}

export interface MeetRecordItem {
  meetId: string;
  initiatorUserId: string;
  initiatorName: string;
  initiatorAvatar: string;
  startedAt: string;
  //單位秒
  duration: number;
  meetUrl: string;
}

export interface NoteRecordItem {
  noteId: string;
  createTime: string;
  refreshTime: string;
  roomId: string;
  userId: string;
  username: string;
  userAvatar: string;
  text: string;
  medias: string[];
  commentCount: number;
}

export interface ImageRecordItem {
  createTime: string;
  userId: string;
  imageUrl: string;
  isVideo: boolean;
}

export interface FileRecordItem {
  createTime: string;
  userId: string;
  fileName: string;
  fileUrl: string;
}

export interface LinkRecordItem {
  createTime: string;
  userId: string;
  text: string;
  matchUrl: string;
}

export interface CommentItem {
  commentId: string;
  createTime: string;
  userId: string;
  username: string;
  userAvatar: string;
  text: string;
}
