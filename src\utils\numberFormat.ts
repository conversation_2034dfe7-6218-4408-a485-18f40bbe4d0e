export const formatInteger = (input: string) => {
  return input.toString().replace(/^0+|[^0-9]/g, ''); // 移除前導零和非數字字符
};

export const formatFloat = (input: string) => {
  // 僅允許數字和小數點
  let formattedInput = input.replace(/[^0-9.]/g, '');
  // 僅保留第一個小數點，清除後續的小數點及其後的所有字符
  const parts = formattedInput.split('.');
  if (parts.length > 2) {
    formattedInput = `${parts[0]}.${parts[1]}`;
  }
  // 如果小數點前沒有數字，保留單個零
  if (formattedInput.startsWith('.')) {
    formattedInput = '0' + formattedInput;
  }
  // 移除多餘的前導零
  formattedInput = formattedInput.replace(/^0+(?=\d)/, '');

  // 移除小數位後綴的零
  if (formattedInput.includes('.')) {
    formattedInput = formattedInput.replace(/(\.\d*?)0+$/, '$1');
    // 如果小數點後面沒有數字，去掉小數點
    formattedInput = formattedInput.replace(/\.$/, '');
  }
  // 如果格式化後的結果為空，則返回 ''
  if (!formattedInput) {
    formattedInput = '';
  }
  return formattedInput;
};

export const formatDecimal = (input: string) => {
  const onlyNumbers = input.replace(/[^0-9.]/g, ''); // 移除非數字字符 除了.
  let [integerPart, decimalPart] = onlyNumbers.split('.');
  integerPart = integerPart.replace(/^0+/, '') || '0'; // 移除整數部分的前導零
  if (decimalPart) {
    decimalPart = decimalPart.replace(/0+$/, ''); // 移除小數點後的多餘零
  }
  return integerPart + (decimalPart ? `.${decimalPart}` : '');
};
