/**
 * @description: 轉換範例：ISO 8601 格式 -> 2023/12/01(日) 14:30
 */
export const formatFullDateTimeWithDay = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    weekday: 'narrow',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 2023/12/01(日)
 */
export const formatFullDateWithDay = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    weekday: 'narrow',
    hour12: false,
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 2023/12/01 14:30
 */
export const formatFullDateTime = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 2023/12/01
 */
export const formatFullDate = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 12/01 (日)
 */
export const formatMonthDateWithDay = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  // 使用 toLocaleDateString 方法來根據當地時區格式化日期
  const formattedDate = timestamp.toLocaleDateString('zh-TW', {
    month: '2-digit',
    day: '2-digit',
    weekday: 'narrow',
    timeZone: 'Asia/Taipei'
  });

  // 將格式化後的日期分割成所需的格式
  const [monthDay] = formattedDate.split(' ');
  const [month, day] = monthDay.split('/');

  return `${month}/${day}`;
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 12/01 14:30
 */
export const formatMonthDateTime = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleString('zh-TW', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 14:30
 */
export const formatTime = (isoDate: string) => {
  const timestamp = new Date(isoDate);

  return timestamp.toLocaleString('zh-TW', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: 'Asia/Taipei'
  });
};

/**
 * @description: 轉換範例：ISO 8601 格式 -> 2023/12/01 ~ 2023/12/31
 */
export const formatDateRange = (isoDateStart: string, isoDateEnd: string) => {
  const startTimestamp = new Date(isoDateStart);
  const endTimestamp = new Date(isoDateEnd);

  const StartDate = startTimestamp.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: 'Asia/Taipei'
  });

  const EndDate = endTimestamp.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: 'Asia/Taipei'
  });

  return `${StartDate} ~ ${EndDate}`;
};

/**
 * @description: 根據Strat和End算出進行總時長 格式：hh:mm:ss
 */
export const formatDuration = (isoDateStart: string, isoDateEnd: string) => {
  const startTimestamp = new Date(isoDateStart);
  const endTimestamp = new Date(isoDateEnd);

// 計算兩個時間戳之間的差異（毫秒）
  const durationMs = endTimestamp.getTime() - startTimestamp.getTime();

  // 計算總秒數
  const totalSeconds = Math.floor(durationMs / 1000);

  // 計算小時、分鐘和秒
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  let formattedDuration: string;
  if (hours === 0) {
    // 格式化為 mm:ss
    formattedDuration = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  } else {
    // 格式化為 hh:mm:ss
    formattedDuration = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }
  return formattedDuration;
};

/**
 * @description: 判斷傳入的 ISO 8601 時間，返回 '今日' '明日' 或 '預約'
 */
export const measureOrderScheduleHint = (isoDate: string): string => {
  const date = new Date(isoDate);
  const today = new Date();
  const tomorrow = new Date();
  const dayAfterTomorrow = new Date();

  // 設置明天的日期
  tomorrow.setDate(today.getDate() + 1);
  // 設定後天的日期
  dayAfterTomorrow.setDate(today.getDate() + 2);

  // 去掉時間部分，只保留日期進行比較
  const isToday = date.toDateString() === today.toDateString();
  const isTomorrow = date.toDateString() === tomorrow.toDateString();
  const isDayAfterTomorrow = date.toDateString() === dayAfterTomorrow.toDateString();
  if (isToday) {
    return '今日丈量行程';
  } else if (isTomorrow) {
    return '明日丈量行程';
  } else if (isDayAfterTomorrow) {
    return '後天丈量行程';
  } else {
    return '預約丈量行程';
  }
};

export const DatePickerToFullDateTimeWithDay = (date: Date) => {
  const formattedDate = date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false, // 24小時制
    weekday: 'narrow'
  });

  const [datePart, timePart] = formattedDate.split(' ');
  const [year, month, day] = datePart.split('/');

  return `${year}/${month}/${day}${timePart}`;
};

export const TimeToEmptyString = (_date: Date) => {
  return '';
};
