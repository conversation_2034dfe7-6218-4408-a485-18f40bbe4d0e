import { OrderTypeEnum } from '@/model/enum/orderType.ts';

interface BaseCardData {
  orderType: OrderTypeEnum;
  orderId: string;
  orderTitle: string;
  customerName: string;
  address: string;
}

export interface MeasureCardData extends BaseCardData {
  measureTime: string;
  cardHint: string;
}

export interface DesignCardData extends BaseCardData {
  areaPing: string;
  designAmount: string;
  constructionBudget: string;
  cardHint: DesignCardHintData;
}

export interface ConstructionCardData extends BaseCardData {
  houseType: string;
  areaPing: string;
  constructionBudget: string;
  cardHint: ConstructionCardHintData;
}

export interface DesignCardHintData {
  orderStatus: string;
  twoDStatus: string;
  threeDStatus: string;
  constructionAmountListStatus: string;
}

export interface ConstructionCardHintData {
  orderTakeStatus: string;
  workStatus: string;
}

export type AcceptedOrderType = MeasureCardData | DesignCardData | ConstructionCardData;
