<script setup lang="ts">
import HezDesignerStep from '@/components/Designer/HezDesignerStep.vue';
import DesignerRegisterLogin from '@/components/Designer/DesignerRegisterLogin.vue';
import HezDesignerInfo from '@/components/Designer/HezDesignerInfo.vue';
import { onMounted } from 'vue';
import { deviceInfo } from '@/utils/userDeviceInfo.ts';
import { SecurityService } from '@/api/security.ts';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';

const designerInfoStore = useDesignerInfoStore();

onMounted(() => {
  const mountData = {
    deviceVersion: deviceInfo.deviceVersion,
    appVersion: deviceInfo.appVersion,
    fcmToken: deviceInfo.fcmToken
  };
  SecurityService.mountCheckLoginByDesigner(mountData);

  if (designerInfoStore.guestToken === '') {
    console.log('設計師Token空的');
    designerInfoStore.getGuestToken();
  }
});
</script>

<template>
  <div class="my-8 flex flex-col space-y-8">
    <HezDesignerInfo />
    <HezDesignerStep />
    <DesignerRegisterLogin v-if="!designerInfoStore.loginState" />
  </div>
</template>
