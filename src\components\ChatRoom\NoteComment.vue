<script setup lang="ts">
import { nextTick, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { toastError, toastInfo } from '@/utils/toastification.ts';
import { NoteService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import {
  AddCommentResponse,
  CommentItem,
  DeleteCommentResponse,
  GetCommentListResponse
} from '@/model/response/noteResponse.ts';
import HezDivider from '@/components/General/HezDivider.vue';
import { formatMonthDateTime } from '@/utils/timeFormat.ts';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const noteId = ref<string>('');
const commentData = ref<CommentItem[]>([]);
const commentInput = ref<string>('');
const skip = ref<number>(0);
const isAPILoading = ref(false);
const commentRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const userInfoStore = props.isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();
const isFirstTime = ref<boolean>(true);
const commentAreaRef = ref<HTMLDivElement | null>(null);
const emit = defineEmits(['update-comment-count']);
const isMoreData = ref<boolean>(true);

const addComment = async () => {
  if (isAPILoading.value === true) return; // API還在跑 不執行動作
  if (commentInput.value === '') {
    toastError('請輸入留言內容');
    return;
  }
  isAPILoading.value = true;
  let res: AddCommentResponse;
  if (props.isDesigner) {
    res = await NoteService.AddCommentByDesigner({
      roomId: props.roomId,
      noteId: noteId.value,
      text: commentInput.value
    });
  } else {
    res = await NoteService.AddCommentByCustomer({
      roomId: props.roomId,
      noteId: noteId.value,
      text: commentInput.value
    });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    commentData.value.unshift(res.comment);
    scrollToEnd();
    commentInput.value = '';
    emit('update-comment-count', noteId.value, true);
  }

  isAPILoading.value = false;
};

const deleteComment = async (commentId: string) => {
  let res: DeleteCommentResponse;
  if (props.isDesigner) {
    res = await NoteService.DeleteCommentByDesigner({
      roomId: props.roomId,
      noteId: noteId.value,
      commentId: commentId
    });
  } else {
    res = await NoteService.DeleteCommentByCustomer({
      roomId: props.roomId,
      noteId: noteId.value,
      commentId: commentId
    });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    commentData.value = commentData.value.filter(comment => comment.commentId !== commentId);
    emit('update-comment-count', noteId.value, false);
  }
};

const openModal = async (id: string) => {
  noteId.value = id;
  commentRef.value?.openModal();
  await getComment();
  isFirstTime.value = false;
  if (commentAreaRef.value) {
    commentAreaRef.value.scrollTop = commentAreaRef.value.scrollHeight;
  }
};

const closeModal = () => {
  commentRef.value?.closeModal();
  commentData.value = [];
  skip.value = 0;
  isFirstTime.value = true;
};

const getComment = async () => {
  let res: GetCommentListResponse;
  if (props.isDesigner) {
    res = await NoteService.GetCommentListByDesigner({
      roomId: props.roomId,
      noteId: noteId.value,
      skip: skip.value,
      limit: 20
    });
  } else {
    res = await NoteService.GetCommentListByCustomer({
      roomId: props.roomId,
      noteId: noteId.value,
      skip: skip.value,
      limit: 20
    });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    if (res.comments.length < 20) {
      isMoreData.value = false;
    }

    if (res.comments.length === 0) {
      if (!isFirstTime.value) toastInfo('已無更多資料');
      return;
    } else {
      commentData.value.push(...res.comments);
      skip.value += 20;
    }
  }
};

const scrollToEnd = () => {
  nextTick(() => {
    if (commentAreaRef.value) {
      commentAreaRef.value.scrollTop = commentAreaRef.value.scrollHeight;
    }
  });
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="留言" ref="commentRef"
                :click-outside-close="false"
                :showCloseButton="true"
                modal-width="max-w-4xl"
                @closeModal="closeModal">
    <div class="flex-col">
      <div class="h-[50vh] overflow-y-auto mb-4" ref="commentAreaRef">
        <p class="cursor-pointer" v-if="isMoreData" @click="getComment()">查看更多</p>
        <div v-for="comment in [...commentData].reverse()" :key="comment.commentId"
             class="w-full flex justify-between p-2">
          <div class="mr-2 flex gap-x-2 items-start w-full">
            <img v-if="comment.userAvatar" :src="comment.userAvatar" alt="avatar"
                 class="w-20 h-20 max-md:w-8 max-md:h-8 rounded-full" />
            <img v-else src="/vectors/general/avatar.svg" alt="avatar"
                 class="w-20 h-20 max-md:w-8 max-md:h-8 rounded-full" />
            <div class="text-start">
              <p class="font-bold text-lg mb-2">{{ comment.username }}</p>
              <p class="break-all">{{ comment.text }}</p>
            </div>
          </div>
          <div class="flex flex-col items-end">
            <p class="text-nowrap">{{ formatMonthDateTime(comment.createTime) }}</p>
            <div v-if="comment.userId === userInfoStore.userId" class="relative">
              <Menu as="div">
                <MenuButton class="block text-gray-500 hover:text-gray-900">
                  <span class="sr-only">Open options</span>
                  <EllipsisVerticalIcon class="h-10 w-10 max-md:w-8 max-md:h-8" aria-hidden="true" />
                </MenuButton>
                <transition enter-active-class="transition ease-out duration-100"
                            enter-from-class="transform opacity-0 scale-95"
                            enter-to-class="transform opacity-100 scale-100"
                            leave-active-class="transition ease-in duration-75"
                            leave-from-class="transform opacity-100 scale-100"
                            leave-to-class="transform opacity-0 scale-95">
                  <MenuItems
                    class="absolute right-8 bottom-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                    <MenuItem
                      v-slot="{ active }">
                      <a
                        :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                        @click="deleteComment(comment.commentId)"
                      >刪除留言</a>
                    </MenuItem>
                  </MenuItems>
                </transition>
              </Menu>
            </div>
          </div>
          <HezDivider />
        </div>
      </div>
      <div class="p-2 w-full">
        <div class="flex max-md:w-full gap-2 items-center">
          <input type="text" v-model="commentInput" class="p-2 border rounded grow" placeholder="輸入留言內容"
                 @keyup.enter="addComment()">
          <img src="/vectors/chatRoom/send.svg" alt="" class="cursor-pointer w-12 rounded-md hover:bg-gray-200"
               @click="addComment()">
        </div>
      </div>
    </div>
  </DefaultModal>
</template>
