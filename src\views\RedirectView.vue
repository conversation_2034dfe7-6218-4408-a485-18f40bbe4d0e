<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { toastError } from '@/utils/toastification.ts';
import { IsProduction } from '@/utils/hezParameters.ts';
import { PortfolioService } from '@/api/portfolio.ts';

const route = useRoute();
const router = useRouter();
const redirectPath = ref<string>(route.params.redirectPath as string);

onMounted(async () => {
  const res = await PortfolioService.getRedirectDesignerPageByCustomer({ targetUrl: redirectPath.value });
  if (res.status === APIStatusCodeEnum.Success) {
    if (IsProduction) {
      window.location.href = `https://www.homeeasy.app/customer/portfolio/${res.userId}`;
    } else {
      window.location.href = `https://www-dev.homeeasy.app/customer/portfolio/${res.userId}`;
    }
  } else if (res.status === APIStatusCodeEnum.RedirectTargetNotFound) {
    toastError('找不到對應的頁面');
    await router.push({ name: 'customerhome' });
  }
});
</script>

<template>
  <div class="container mx-auto p-4">
    跳轉中...
  </div>
</template>
