<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const InvoiceData = ref<{
  invoiceIndex: number;
  fileUrl: string;
  amount: string;
  createTime: string;
}>({
  invoiceIndex: 0,
  fileUrl: '',
  amount: '',
  createTime: ''
});

const openModal = (InvoiceResult: {
  invoiceIndex: number;
  fileUrl: string;
  amount: string;
  createTime: string;
}) => {
  InvoiceData.value = InvoiceResult;
  modalRef.value?.openModal();
};

const closeMediaModal = () => {
  modalRef.value?.closeModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="`已開立發票 (第${InvoiceData.invoiceIndex + 1}張)`" :show-close-button="true"
                :click-outside-close="true" ref="modalRef"
                @closeModal="closeMediaModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true">
          <swiper-slide class="bg-center bg-auto">
            <p class="mb-2">{{ InvoiceData.createTime }}</p>
            <img :src="InvoiceData.fileUrl" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <p class="mt-2">金額：{{ InvoiceData.amount }}</p>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>
</template>
