<script setup lang="ts">
import { onBeforeUnmount, ref, onMounted } from 'vue';
import { SecurityService } from '@/api/security.ts';
import type { RegisterData } from '@/model/general/security.ts';
import { PhoneVerifyStatusEnum } from '@/model/enum/verifyStatusEnum.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { phoneNumberValid } from '@/utils/phoneNumberValid.ts';
import { toastError, toastSuccess } from '@/utils/toastification.ts';
import { passwordValid } from '@/utils/passwordValid.ts';
import { hashPassword } from '@/utils/crypto.ts';
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline';
import type { LoginData } from '@/model/general/security.ts';
import { decryptAES } from '@/utils/crypto.ts';
import { useRouter } from 'vue-router';
import { DesignerAccountService } from '@/api/designerAccount.ts';
import { useDesignerInfoStore, useDesignerVerifyStore, useDesignerRedirectStore } from '@/stores/designerGlobal.ts';

const phone = ref<string>('');
const verifyCode = ref<string>('');
const phoneVerifyStatus = ref<PhoneVerifyStatusEnum>(PhoneVerifyStatusEnum.Unverified);
const registerInput = ref<RegisterData>({
  phoneVerifyId: '',
  password: '',
  rePassword: '',
  agreeTerm: false
});
const sentCodeCountDown = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const showPassword = ref(false);
const showRePassword = ref(false);
const isNowDoingLogin = ref(false);

const loginInput = ref<LoginData>({
  phone: '',
  password: '',
  rememberForShow: true
});
const showLoginPassword = ref(false);
const router = useRouter();
const designerInfoStore = useDesignerInfoStore();
const redirectStore = useDesignerRedirectStore();
const useDesignerVerify = useDesignerVerifyStore();

const sendCode = async (phoneNumber: string) => {
  const validResult = phoneNumberValid(phoneNumber);
  if (!validResult) {
    toastError('請輸入正確的手機號碼');
    return;
  } else {
    const sendVerifyCodeResult = await SecurityService.sendVerifyCodeByDesigner({ phone: phoneNumber });
    if (sendVerifyCodeResult.status === APIStatusCodeEnum.Success) {
      toastSuccess('驗證碼已傳送');
      phoneVerifyStatus.value = PhoneVerifyStatusEnum.VerifyCodeSent;
      startCountdown();
    } else {
      if (sendVerifyCodeResult.status === APIStatusCodeEnum.PhoneVerifySendExceeded) {
        toastError('驗證碼傳送次數過多');
        return;
      }
      toastError('驗證碼傳送失敗');
    }
  }
};

const startCountdown = () => {
  sentCodeCountDown.value = 60;
  countdownInterval.value = window.setInterval(() => {
    if (sentCodeCountDown.value !== null) {
      sentCodeCountDown.value -= 1;
      console.log(sentCodeCountDown.value);
      if (sentCodeCountDown.value <= 0) {
        clearInterval(countdownInterval.value!);
        sentCodeCountDown.value = null;
        phoneVerifyStatus.value = PhoneVerifyStatusEnum.Unverified;
        console.log('clear');
      }
    }
  }, 1000);
};

const checkVerifyCode = async (code: string) => {
  const checkResult = await SecurityService.checkVerifyCodeByDesigner({ verifyCode: code });
  if (checkResult.status === APIStatusCodeEnum.Success) {
    toastSuccess('驗證成功');
    phoneVerifyStatus.value = PhoneVerifyStatusEnum.Verified;
    clearCountdown();
    registerInput.value.phoneVerifyId = checkResult.phoneVerifyId;
  } else {
    toastError('驗證失敗');
  }
};

const register = async () => {
  if (phoneVerifyStatus.value !== PhoneVerifyStatusEnum.Verified) {
    toastError('請先完成手機號碼驗證');
    return;
  }

  if (!registerInput.value.agreeTerm) {
    toastError('請勾選同意服務條款');
    return;
  }

  const { password, rePassword } = registerInput.value;
  if (password !== rePassword) {
    toastError('密碼不一致');
    return;
  }

  if (!passwordValid(password)) {
    toastError('密碼格式錯誤');
    return;
  }

  const ciphertext = await hashPassword(password);
  const registerResult = await SecurityService.registerAndLoginByDesigner({
    phoneVerifyId: registerInput.value.phoneVerifyId,
    password: ciphertext
  });
  if (registerResult.status !== APIStatusCodeEnum.Success) {
    toastError('註冊失敗');
    return;
  }
  designerInfoStore.registerSuccess(registerResult.userId, phone.value, registerInput.value.password);
  toastSuccess('註冊成功');
  resetRegister();
  await updateDesignerVerifyStore(); // 更新驗證狀態
  // 註冊成功之後 要按照Pinia儲存的 redirectRouteName 進行導向
  if (redirectStore.redirectRouteName === 'designerportfolio') {
    await router.push({ name: redirectStore.redirectRouteName, params: { designerId: designerInfoStore.userId } });
  } else {
    await router.push({ name: redirectStore.redirectRouteName });
  }
  redirectStore.resetRedirectRoute();
};

const resetRegister = () => {
  phone.value = '';
  verifyCode.value = '';
  phoneVerifyStatus.value = PhoneVerifyStatusEnum.Unverified;
  registerInput.value = {
    phoneVerifyId: '',
    password: '',
    rePassword: '',
    agreeTerm: false
  };
};

const clearCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
  }
};

//---------------------------------------------------登入區塊

const processLogin = async () => {
  if (!loginInput.value.phone || !loginInput.value.password) {
    toastError('請輸入完整登入資訊');
    return;
  }
  if (!(phoneNumberValid(loginInput.value.phone)) || !(passwordValid(loginInput.value.password))) {
    toastError('請輸入正確的登入資訊格式');
    return;
  }
  const loginSuccess = await designerInfoStore.login(loginInput.value);
  if (loginSuccess) {
    resetLoginInput();
    // 登入成功之後 要按照Pinia儲存的 redirectRouteName 進行導向
    await updateDesignerVerifyStore(); // 更新驗證狀態
    if (redirectStore.redirectRouteName === 'designerportfolio') {
      await router.push({ name: redirectStore.redirectRouteName, params: { designerId: designerInfoStore.userId } });
    } else {
      await router.push({ name: redirectStore.redirectRouteName });
    }
    redirectStore.resetRedirectRoute();
  }
};

const resetLoginInput = () => {
  loginInput.value = {
    phone: '',
    password: '',
    rememberForShow: true
  };
};

const checkRememberPassword = () => {
  if (designerInfoStore.rememberMe) {
    loginInput.value = {
      phone: decryptAES(designerInfoStore.loginCipher.phone),
      password: decryptAES(designerInfoStore.loginCipher.password),
      rememberForShow: true
    };
  }
};

const updateDesignerVerifyStore = async () => {
  const res = await DesignerAccountService.getInfo({});
  useDesignerVerify.setTaiwanIdVerify(res.result.taiwanId.verifyStatus);
  useDesignerVerify.setCompanyVerify(res.result.company.verifyStatus);
  useDesignerVerify.setEmailVerify(res.result.email.isVerify);
};

onMounted(() => {
  checkRememberPassword();
  if (designerInfoStore.hasEverLogin) {
    isNowDoingLogin.value = true;
  }
});

onBeforeUnmount(() => {
  resetRegister();
  resetLoginInput();
  clearCountdown();
});

</script>

<template>
  <div class="w-full cus-border mx-auto my-8 md:text-lg">
    <!--    註冊-->
    <div v-if="!isNowDoingLogin" class="flex flex-col justify-center items-center space-y-8">
      <div class="flex flex-col items-center gap-2">
        <p class="text-lg font-bold">註冊</p>
        <p class="text-black">已經註冊過家易帳號？ <span
          class="text-blue-700 cursor-pointer" @click="isNowDoingLogin = true">立即登入！</span></p>
      </div>

      <div class="w-full max-w-md space-y-4">
        <label class="text-gray-700">驗證手機
          <span v-if="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified" class="text-blue-600">
            驗證成功</span>
          <span v-else-if="phoneVerifyStatus === PhoneVerifyStatusEnum.VerifyCodeSent" class="text-red-500">
              驗證碼已傳送 <span v-if="sentCodeCountDown !== null">({{ sentCodeCountDown }})</span>
            </span>
          <span v-else class="text-red-500">未驗證</span></label>

        <div class="flex items-center gap-x-2">
          <label for="phone" class="sr-only">Phone</label>
          <input id="phone" name="phone" type="tel" v-model="phone"
                 :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified || phoneVerifyStatus === PhoneVerifyStatusEnum.VerifyCodeSent"
                 class="cus-border focus-border-color p-2 w-full hez-disable"
                 placeholder="請輸入手機號碼" />
          <button type="button"
                  :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified || phoneVerifyStatus === PhoneVerifyStatusEnum.VerifyCodeSent"
                  class="cus-btn py-2 px-4 hez-disable text-nowrap md:w-2/5"
                  @click="sendCode(phone)">發送驗證碼
          </button>
        </div>

        <div class="flex items-center gap-x-2">
          <label for="OTP" class="sr-only">Password</label>
          <input id="OTP" name="OTP" type="tel"
                 v-model="verifyCode" :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified"
                 class="cus-border focus-border-color p-2 w-full hez-disable"
                 placeholder="請輸入驗證碼" />
          <button type="button" :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified"
                  class="cus-btn py-2 px-4 hez-disable text-nowrap md:w-2/5"
                  @click="checkVerifyCode(verifyCode)">驗證
          </button>
        </div>
        <p>設定登入密碼</p>
        <p class="text-gray-500">密碼至少為六個位元，需包含英文與數字</p>
        <div class="relative">
          <label for="password" class="sr-only">Password</label>
          <input id="password" name="password" :type="showPassword ? 'text' : 'password'"
                 v-model="registerInput.password"
                 :disabled="phoneVerifyStatus !== PhoneVerifyStatusEnum.Verified"
                 class="cus-border px-4 py-2 w-full focus-border-color"
                 placeholder="請輸入密碼" />
          <EyeIcon v-if="showPassword" @click="showPassword = false"
                   class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          <EyeSlashIcon v-else @click="showPassword = true"
                        class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
        </div>
        <div class="relative">
          <label for="rePassword" class="sr-only">rePassword</label>
          <input id="rePassword" name="rePassword" :type="showRePassword ? 'text' : 'password'"
                 v-model="registerInput.rePassword"
                 :disabled="phoneVerifyStatus !== PhoneVerifyStatusEnum.Verified"
                 class="cus-border px-4 py-2 w-full focus-border-color"
                 placeholder="請再次輸入密碼" />
          <EyeIcon v-if="showRePassword" @click="showRePassword = false"
                   class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          <EyeSlashIcon v-else @click="showRePassword = true"
                        class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
        </div>

        <div class="flex gap-x-1 items-center">
          <input id="read-term" name="read-term" type="checkbox"
                 class="w-5 h-5 check-btn"
                 v-model="registerInput.agreeTerm" />
          <div class="flex justify-center gap-x-0.5 max-md:flex-wrap">
            <p>我已閱讀並同意</p>
            <a href="/designer/terms" target="_blank"
               class="text-red-500 cursor-pointer hover:underline">服務條款</a>
          </div>
        </div>
        <div class="mt-1 md:mt-2">
          <p>如果無法收到驗證碼或驗證相關問題</p>
          <div class="flex justify-start gap-x-0.5 max-md:flex-wrap">
            <p>請聯絡客服：</p>
            <a href="mailto:<EMAIL>"
               class="text-red-500 cursor-pointer hover:underline"><EMAIL></a>
          </div>
        </div>
        <button @click="register"
                class="cus-btn w-full py-2 px-4"> 註冊
        </button>
      </div>
    </div>

    <!--    登入-->
    <div v-else class="flex flex-col justify-center items-center space-y-8">
      <div class="flex flex-col items-center gap-2">
        <p class="text-lg font-bold">登入</p>
        <p class="text-black">尚未註冊過家易帳號？ <span
          class="text-red-500 font-bold cursor-pointer" @click="isNowDoingLogin = false">立即註冊！</span></p>
      </div>
      <div class="w-full max-w-md space-y-4">
        <div>
          <label for="phone" class="sr-only">Phone</label>
          <input id="phone" name="phone" type="tel" v-model="loginInput.phone"
                 class="cus-border px-4 py-2 w-full focus-border-color"
                 placeholder="請輸入手機號碼" />
        </div>
        <div class="relative">
          <label for="password" class="sr-only">Password</label>
          <input id="password" name="password" :type="showLoginPassword ? 'text' : 'password'"
                 v-model="loginInput.password"
                 class="cus-border px-4 py-2 w-full focus-border-color"
                 placeholder="請輸入密碼" />
          <EyeIcon v-if="showLoginPassword" @click="showLoginPassword = false"
                   class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          <EyeSlashIcon v-else @click="showLoginPassword = true"
                        class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
        </div>
        <div class="flex gap-x-1 items-center">
          <input id="remember-me" name="remember-me" type="checkbox"
                 class="w-5 h-5 check-btn" v-model="loginInput.rememberForShow" />
          <label for="remember-me" class="ml-2 block leading-6">記住密碼</label>
        </div>
        <button @click="processLogin"
                class="cus-btn w-full py-2">
          登入
        </button>
      </div>
    </div>
  </div>

</template>
