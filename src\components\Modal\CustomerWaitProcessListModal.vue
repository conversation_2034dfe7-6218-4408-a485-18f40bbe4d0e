<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import { ref } from 'vue';

// defineProps<{ title: string; }>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="等待設計師提供報價清單" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">因網頁無法自動刷新</p>
        <p class="font-bold md:text-lg">請在家易App查看設計師提供報價清單</p>
        <p class="font-bold md:text-lg">超過百位設計師方便您比價</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>
</template>
