<script setup lang="ts">
import { CustomerTextContent } from '@/model/response/customerMeasureOrderResponse.ts';
import HezDivider from '@/components/General/HezDivider.vue';

const houseNote = defineModel<CustomerTextContent>({ required: true });

</script>

<template>
  <div class="flex flex-col cus-border my-1 gap-4">
    <div v-for="note in houseNote.content" :key="note.name" class="flex flex-col gap-y-2">
      <div class="flex justify-evenly text-2xl items-center">
        <p class=" font-bold text-color-primary text-center">{{ note.name }}</p>
      </div>
      <HezDivider />
      <div class="flex justify-center">
        <div class="flex w-full text-lg text-color-primary mb-4">
        <textarea id="houseNote" v-model="note.text" name="houseNote" rows="5" disabled
                  placeholder="未上傳"
                  class="w-full text-base rounded-md" />
        </div>
      </div>
    </div>
  </div>
</template>
