<script setup lang="ts">
import { TextKeyContent, TextKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { MeasureOrderDetailService } from '@/api/designerOrder.ts';
import HezDivider from '@/components/General/HezDivider.vue';
import { computed } from 'vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const houseNote = defineModel<TextKeyContent>({ required: true });
const props = defineProps<{
  orderId: string;
  disabled: boolean;
}>();

const updateHouseNote = async (note: TextKeyItem, index: number) => {
  const result = await MeasureOrderDetailService.updateNote({
    orderId: props.orderId,
    builders: [{
      key: note.key,
      setName: index === 0 ? '房屋備註(梁柱窗等)' : '屋主備註(依情況提供)',
      setText: note.text
    }]
  });
  if ((result.status === APIStatusCodeEnum.Success)) {
    houseNote.value = result.content.note;
  }
};

const placeholder1 = computed(() => {
  return props.disabled ? '未上傳' : '請具體描述房屋備注\n' +
    '例如：\n' +
    '房屋位於三樓，面積約90平方米，結構為三房兩廳兩衛。';
});

const placeholder2 = computed(() => {
  return props.disabled ? '未上傳' : '請具體描述屋主備注\n' +
    '例如：\n' +
    '屋主表示此房屋曾於三年前進行過一次全面裝修，包括水電更新和地板更換。';
});

</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div v-for="(note,index) in houseNote.content" :key="note.key" class="flex flex-col gap-y-2">
      <div class="flex justify-evenly text-2xl items-center">
        <p class=" font-bold text-color-primary text-center">{{ note.name }}</p>
      </div>
      <HezDivider />

      <div class="flex justify-center">
        <div class="flex w-full justify-start text-lg text-color-primary mb-4">
        <textarea id="houseNote" name="houseNote" rows="5" :disabled="disabled"
                  :placeholder="index === 0? placeholder1 : placeholder2"
                  v-model="note.text" @change="updateHouseNote(note,index)"
                  class="w-full input-basic text-base rounded-md input-focus" />
        </div>
      </div>
    </div>
  </div>

</template>
