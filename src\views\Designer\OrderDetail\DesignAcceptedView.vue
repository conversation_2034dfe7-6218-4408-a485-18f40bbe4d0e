<script setup lang="ts">
import { useRoute } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import HezDivider from '@/components/General/HezDivider.vue';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum, SigningStatusEnum } from '@/model/enum/orderStatus.ts';
import HouseNote from '@/components/Order/OrderDetail/SubComponets/HouseNote.vue';
import HousePhoto from '@/components/Order/OrderDetail/SubComponets/HousePhoto.vue';
import FloorPlan from '@/components/Order/OrderDetail/SubComponets/FloorPlan.vue';
import HouseCheck from '@/components/Order/OrderDetail/SubComponets/HouseCheck.vue';
import DesignHouseInfo from '@/components/Order/OrderDetail/SubComponets/DesignHouseInfo.vue';
import TextContent from '@/components/Order/OrderDetail/SubComponets/TextContent.vue';
import { DesignOrderDetailService, OrderDetailService } from '@/api/designerOrder.ts';
import {
  ConstructionAmountItem,
  DesignOrderAcceptedDetailItem,
  ImagePdfCadKeyItem,
  ImageVideoKeyItem,
  TextKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { budgetCombineLowToHigh, moneyAddCommas, MoneyToTenThousand } from '@/utils/budgetFormat.ts';
import { formatFullDate } from '@/utils/timeFormat.ts';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import { toastError } from '@/utils/toastification.ts';
import DesignerSignModal from '@/components/Modal/DesignerSignModal.vue';
import DesignerRemitModal from '@/components/Modal/DesignerRemitModal.vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import Upload2D from '@/components/Order/OrderDetail/SubComponets/Upload2D.vue';
import Upload3D from '@/components/Order/OrderDetail/SubComponets/Upload3D.vue';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { downloadFile } from '@/utils/fileDownloader.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import { formatInteger } from '@/utils/numberFormat.ts';

const route = useRoute();
const orderId = route.params.id as string;
const designerInfoStore = useDesignerInfoStore();
const chatHubStore = useChatHubStore();
const signModalTitle = ref('合約簽署');
const remitModalTitle = ref('匯款狀態');
const signModal = ref<InstanceType<typeof DesignerSignModal>>();
const remitModal = ref<InstanceType<typeof DesignerRemitModal>>();
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal>>();
const constructionFeeModalRef = ref<InstanceType<typeof DefaultModal>>();
const upload2DRef = ref<InstanceType<typeof Upload2D>>();
const upload3DRef = ref<InstanceType<typeof Upload3D>>();
const tempConstructionAmount = ref<number>(0); //單位是萬
const documentUrlInput = ref<HTMLInputElement | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);

const orderData = ref<DesignOrderAcceptedDetailItem>({
  designerId: '',
  orderId: '',
  createTime: '',
  refreshTime: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  publishInfo: {
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    designBudget: {
      upper: 0,
      lower: 0
    },
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    discussionFrequency: 0, // 每周討論頻率，單位次/周
    keyDesignDetail: '',
    referenceImages: [],
    assignDesignerId: '' //設計師端
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    }
  },
  designerQuote: {
    designAmount: 0,
    constructionEstimate: {
      upper: 0,
      lower: 0
    }
  },
  chatRoomId: '',
  customerId: '',
  customerAvatarUrl: '',
  customerPhone: '',
  isDeleted: false,
  status: DesignOrderStatusEnum.Contracting,
  subStatus: DesignOrderSubStatusEnum.NotUploaded,
  contract: {
    signingStatus: SigningStatusEnum.Init,
    isRemittanceInformationSet: false, //匯款資訊是否已設定
    amount: 0, //設計金額(報價金額)
    customerRemittanceAmount: 0
  },
  designContent: {
    design2D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    design3D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    constructionAmountDocs: [] as ConstructionAmountItem[]
  }
});

const orderDataDetail = computed(() => {
  return {
    status: orderData.value.status,
    subStatus: orderData.value.subStatus,
    designerId: orderData.value.designerId,
    orderId: orderData.value.orderId,
    customerName: orderData.value.customerName,
    address: orderData.value.address.fullName,
    publishInfo: orderData.value.publishInfo,
    measureContent: orderData.value.measureContent,
    contract: {
      signingStatus: orderData.value.contract.signingStatus,
      isRemittanceInformationSet: orderData.value.contract.isRemittanceInformationSet, //匯款資訊是否已設定
      amount: moneyAddCommas(orderData.value.contract.amount), //設計金額(報價金額)
      customerRemittanceAmount: moneyAddCommas(orderData.value.contract.customerRemittanceAmount) //客戶已匯款金額
    },
    designContent: {
      design2D: orderData.value.designContent.design2D,
      design3D: orderData.value.designContent.design3D,
      constructionAmountDocs: orderData.value.designContent.constructionAmountDocs
    },
    chatRoomId: orderData.value.chatRoomId,
    nameAddress: {
      name: orderData.value.customerName,
      address: orderData.value.address.fullName
    },
    design2DMoney: moneyAddCommas(Math.round(orderData.value.contract.amount / 2)),
    design3DMoney: moneyAddCommas(orderData.value.contract.amount - Math.round(orderData.value.contract.amount / 2)),
    latestConstructionAmountDoc: orderData.value.designContent.constructionAmountDocs.length === 0 ? {
      amount: '0', //報價金額
      amountNumber: orderData.value.designerQuote.constructionEstimate.lower, // 報價金額 給input用 第一次就先拿設計師估價的最低值
      time: '0', //時間
      documentUrl: '', //檔案URL
      count: 0 //第幾次
    } : {
      amount: moneyAddCommas(orderData.value.designContent.constructionAmountDocs.slice(-1)[0].amount),
      amountNumber: orderData.value.designContent.constructionAmountDocs.slice(-1)[0].amount,
      time: formatFullDate(orderData.value.designContent.constructionAmountDocs.slice(-1)[0].createTime),
      documentUrl: orderData.value.designContent.constructionAmountDocs.slice(-1)[0].documentUrl,
      count: orderData.value.designContent.constructionAmountDocs.length - 1
    },
    customerConstructionBudget: budgetCombineLowToHigh(orderData.value.publishInfo.constructionBudget),
    deignerConstructionEstimate: budgetCombineLowToHigh(orderData.value.designerQuote.constructionEstimate),
    deignerConstructionEstimateUpper: MoneyToTenThousand(orderData.value.designerQuote.constructionEstimate.upper)
  };
});

const showSignOrRemitModal = (title?: string) => {
  if (orderDataDetail.value.contract.signingStatus !== SigningStatusEnum.Verified) {
    if (title) {
      signModalTitle.value = title;
    } else {
      signModalTitle.value = '合約簽署';
    }
    signModal.value?.openModal();
  } else {
    if (title) {
      remitModalTitle.value = title;
    } else {
      remitModalTitle.value = '匯款狀態';
    }
    remitModal.value?.openModal();
  }
};

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      switch (orderDataDetail.value.status) {
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('免費語音');
          break;
        default:
          appUsePhoneModalRef.value?.openModal();
          break;
      }
      break;
    case contactItemEnum.Meet:
      switch (orderDataDetail.value.status) {
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('視訊會議');
          break;
        default:
          createMeet(orderDataDetail.value.chatRoomId, true, designerInfoStore.userId);
      }
      break;
    case contactItemEnum.Chat:
      switch (orderDataDetail.value.status) {
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('聊天室');
          break;
        default:
          if (chatHubStore._worker === null) {
            sharedWorkerNotSupportModalRef.value?.openModal();
          } else {
            chatRoomRef.value?.showChatRoom();
          }
      }
      break;
    case contactItemEnum.Note:
      switch (orderDataDetail.value.status) {
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('紀錄本');
          break;
        default:
          noteBookRef.value?.openModal();
      }
  }
};

const orderInfoClicked = () => {
  if (orderDataDetail.value.status === DesignOrderStatusEnum.Contracting) {
    showSignOrRemitModal('合約簽署');
  }
};

const design2DClicked = () => {
  if (orderDataDetail.value.contract.signingStatus !== SigningStatusEnum.Verified) {
    showSignOrRemitModal('2D室內設計');
  } else {
    upload2DRef.value?.openUpdateModal();
  }
};

const design3DClicked = () => {
  if (orderDataDetail.value.contract.signingStatus !== SigningStatusEnum.Verified) {
    showSignOrRemitModal('3D模型設計');
  } else {
    upload3DRef.value?.openUpdateModal();
  }
};

const constructionAmountClicked = () => {
  if (orderDataDetail.value.contract.signingStatus !== SigningStatusEnum.Verified) {
    showSignOrRemitModal('裝潢施工報價');
  } else if (orderDataDetail.value.status !== DesignOrderStatusEnum.Completed) {
    openConstructionFeeModal();
  }
};

//TODO 裝潢施工報價 資料上傳糞Code 起始點

const triggerFileInput = () => {
  documentUrlInput.value?.click();
};

const imageUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.File)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    await uploadConstructionFee(S3Urls[0]);
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const uploadConstructionFee = async (url: string) => {
  const res = await DesignOrderDetailService.updateConstructionFee({
    orderId: orderId,
    constructionAmount: tempConstructionAmount.value,
    constructionDocumentUrl: url
  });
  if (res.status === APIStatusCodeEnum.Success) {
    await getOrderData();
  }
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};


const openConstructionFeeModal = () => {
  tempConstructionAmount.value = Math.ceil(orderDataDetail.value.latestConstructionAmountDoc.amountNumber / 10000);
  constructionFeeModalRef.value?.openModal();
};

const closeConstructionFeeModal = () => {
  tempConstructionAmount.value = Math.ceil(orderDataDetail.value.latestConstructionAmountDoc.amountNumber / 10000);
  constructionFeeModalRef.value?.closeModal();
};

const cleanTempConstructionAmount = () => {
  tempConstructionAmount.value = parseInt(formatInteger(tempConstructionAmount.value.toString()));
  if (isNaN(tempConstructionAmount.value) || tempConstructionAmount.value <= 0) tempConstructionAmount.value = 1;
};

//TODO 裝潢施工報價 資料上傳糞Code 結束點

const is2DAllUploaded = computed(() => {
  const imagePass = orderDataDetail.value.designContent.design2D.content.every((item) => item.images.length > 0);
  const pdfPass = orderDataDetail.value.designContent.design2D.content.every((item) => item.pdf.url !== '');
  const cadPass = orderDataDetail.value.designContent.design2D.content.every((item) => item.cad.url !== '');
  return !(!imagePass || !pdfPass || !cadPass);
});

const is2DContentEmpty = computed(() => {
  return orderDataDetail.value.designContent.design2D.content.length === 0;
});

const is2DHasSomeUploaded = computed(() => {
  const imagePass = orderDataDetail.value.designContent.design2D.content.some((item) => item.images.length > 0);
  const pdfPass = orderDataDetail.value.designContent.design2D.content.some((item) => item.pdf.url !== '');
  const cadPass = orderDataDetail.value.designContent.design2D.content.some((item) => item.cad.url !== '');
  return (imagePass || pdfPass || cadPass);
});

const is3DAllUploaded = computed(() => {
  return orderDataDetail.value.designContent.design3D.content.every((item) => item.media.length > 0);
});

const is3dHasSomeUploaded = computed(() => {
  return orderDataDetail.value.designContent.design3D.content.some((item) => item.media.length > 0);
});

const is3DContentEmpty = computed(() => {
  return orderDataDetail.value.designContent.design3D.content.length === 0;
});

const getOrderData = async () => {
  const res = await OrderDetailService.getDesignAccepted({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    orderData.value = res.result;
    if (orderData.value.contract.signingStatus === SigningStatusEnum.Verified && orderData.value.status === DesignOrderStatusEnum.Contracting) {
      showSignOrRemitModal('客戶尚未匯款');
    }
  }
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

onMounted(async () => {
  await getOrderData();
});
</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <div class="cus-border text-lg">
      <h2 class="text-2xl text-center font-bold">室內設計</h2>
      <div class="flex p-4 flex-col gap-y-1">
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">屋主姓名</p>
          <p class="font-bold">{{ orderDataDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">設計地址</p>
          <p class="font-bold">{{ orderDataDetail.address }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">設計金額</p>
          <p class="font-bold">{{ orderDataDetail.contract.amount }}</p>
        </div>
        <p class="font-bold text-blue-600">
          如果需要使用分享桌面或傳送CAD檔案的功能，建議使用電腦的網頁上操作
        </p>
      </div>
      <HezDivider />
      <div class="flex justify-around">
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Phone)">
          <PhoneIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">免費語音</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Meet)">
          <VideoCameraIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">視訊會議</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Chat)">
          <ChatBubbleLeftEllipsisIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">聊天室</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Note)">
          <ClipboardDocumentListIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">紀錄本</span>
        </button>
      </div>
    </div>

    <div v-if="orderData.chatRoomId && chatHubStore._worker !== null"
         class="fixed flex flex-col justify-end bottom-0 right-0 w-80 z-40"
         :class="isChatRoomShow ? 'h-3/5' : 'h-auto' ">
      <ChatRoom :is-for-meet="false"
                :room-id="orderData.chatRoomId" :is-designer="true"
                @show-chat-room="changeChatRoomShow"
                ref="chatRoomRef" />
    </div>

    <!-- CustomerInfo Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="orderInfoClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          1
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold text-nowrap md:text-lg">客戶匯款金額</p>
            <p class="font-bold md:text-lg"> {{ orderDataDetail.contract.amount }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">合約狀態</p>
            <p v-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Init"
               class=" font-bold md:text-lg text-red-600">未簽署</p>
            <p v-else-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Verifying"
               class=" font-bold md:text-lg text-blue-600">已簽署(審核中)</p>
            <p v-else-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Verified"
               class=" font-bold md:text-lg text-black">已簽署</p>
            <p v-else class=" font-bold md:text-lg text-red-600">已簽署(審核失敗)</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">匯款狀態</p>
            <p v-if="orderDataDetail.status === DesignOrderStatusEnum.Contracting"
               class=" font-bold md:text-lg text-red-600">客戶未匯款</p>
            <p v-else class=" font-bold md:text-lg text-black">客戶已匯款</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 2D Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between cursor-pointer"
         @click="design2DClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          2
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class=" font-bold md:text-lg text-black">2D室內設計</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">工程款</p>
            <p class=" font-bold md:text-lg text-black">{{ orderDataDetail.design2DMoney }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <template v-if="orderDataDetail.status === DesignOrderStatusEnum.Contracting">
              <p class=" font-bold md:text-lg text-black">未上傳</p>
            </template>
            <template v-else-if="orderDataDetail.status === DesignOrderStatusEnum.Working">
              <p
                v-if="(orderDataDetail.subStatus === DesignOrderSubStatusEnum.NotUploaded || orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DUploaded) && is2DContentEmpty"
                class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.NotUploaded"
                 class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p
                v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DUploaded  && !is2DHasSomeUploaded"
                class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p
                v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DUploaded  && !is2DAllUploaded"
                class=" font-bold md:text-lg text-red-600">客戶未同意撥款</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DUploaded"
                 class=" font-bold md:text-lg text-blue-600">尚未請款</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DRequest"
                 class=" font-bold md:text-lg text-blue-600">驗收中</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DReject"
                 class=" font-bold md:text-lg text-red-600">客戶要求修正</p>
              <p v-else-if="orderDataDetail.subStatus >= DesignOrderSubStatusEnum.Design2DAgree "
                 class=" font-bold md:text-lg text-black">客戶已同意撥款</p>
            </template>
            <template v-else>
              <p class=" font-bold md:text-lg text-black">客戶已同意撥款</p>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 3D Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between cursor-pointer"
         @click="design3DClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          3
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class="font-bold md:text-lg text-black">3D模型設計</p>
          </div>
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">工程款</p>
            <p class="font-bold md:text-lg text-black">{{ orderDataDetail.design3DMoney }}</p>
          </div>
          <div class="flex gap-3">
            <p class="font-bold md:text-lg text-nowrap">狀態</p>
            <template v-if="orderDataDetail.status === DesignOrderStatusEnum.Contracting">
              <p class="font-bold md:text-lg text-black">未上傳</p>
            </template>
            <template v-else-if="orderDataDetail.status === DesignOrderStatusEnum.Working">
              <p v-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design2DAgree && !is3dHasSomeUploaded"
                 class="font-bold md:text-lg text-black">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design2DAgree && is3DContentEmpty"
                 class="font-bold md:text-lg text-black">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design2DAgree && is3dHasSomeUploaded"
                 class="font-bold md:text-lg text-black">客戶未同意撥款</p>

              <p
                v-else-if="(orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DAgree || orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DUploaded) && is3DContentEmpty"
                class="font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design2DAgree"
                 class="font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DUploaded && !is3DAllUploaded"
                 class="font-bold md:text-lg text-red-600">客戶未同意撥款</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DUploaded"
                 class="font-bold md:text-lg text-blue-600">尚未請款</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DRequest"
                 class="font-bold md:text-lg text-blue-600">驗收中</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DReject"
                 class="font-bold md:text-lg text-red-600">客戶要求修正</p>
              <p v-else-if="orderDataDetail.subStatus >= DesignOrderSubStatusEnum.Design3DAgree"
                 class="font-bold md:text-lg text-black">客戶已同意撥款</p>
            </template>
            <template v-else>
              <p class="font-bold md:text-lg text-black">客戶已同意撥款</p>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Amount Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between cursor-pointer"
         @click="constructionAmountClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          4
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class=" font-bold md:text-lg text-black">裝潢施工報價</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">裝潢施工報價</p>
            <template
              v-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree && orderDataDetail.designContent.constructionAmountDocs.length > 0">
              <p class=" font-bold md:text-lg text-black">
                {{ orderDataDetail.latestConstructionAmountDoc.amount }}</p>
            </template>
            <template v-else-if="orderDataDetail.status === DesignOrderStatusEnum.Contracting">
              <p class=" font-bold md:text-lg text-black">未報價</p>
            </template>
            <template v-else>
              <p v-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-black">未報價</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-red-600">未報價</p>
              <p v-else class=" font-bold md:text-lg text-black">
                {{ orderDataDetail.latestConstructionAmountDoc.amount }}</p>
            </template>
          </div>

          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">裝潢施工清單</p>
            <template
              v-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree && orderDataDetail.designContent.constructionAmountDocs.length > 0">
              <p class=" font-bold md:text-lg text-black">客戶未同意完成</p>
            </template>
            <template v-else-if="orderDataDetail.status === DesignOrderStatusEnum.Contracting">
              <p v-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else class=" font-bold md:text-lg text-black">未上傳</p>
            </template>
            <template v-else>
              <p v-if="orderDataDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-black">未上傳</p>
              <p v-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsUploaded"
                 class=" font-bold md:text-lg text-blue-600">
                第{{ orderDataDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ orderDataDetail.latestConstructionAmountDoc.time }})
              </p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsAccepted"
                 class=" font-bold md:text-lg text-black">
                第{{ orderDataDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ orderDataDetail.latestConstructionAmountDoc.time }})
              </p>
              <p v-else-if="orderDataDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsRequest"
                 class=" font-bold md:text-lg text-red-600">客戶要求修正</p>
            </template>
          </div>
        </div>
      </div>
    </div>
    <Upload2D :order-id="orderId" :disabled="orderDataDetail.contract.signingStatus !== SigningStatusEnum.Verified"
              :design2-d="orderDataDetail.designContent.design2D"
              :sub-status="orderDataDetail.subStatus"
              :order-status="orderDataDetail.status"
              @refresh-data="getOrderData()"
              ref="upload2DRef" />

    <Upload3D :order-id="orderId" :disabled="orderDataDetail.contract.signingStatus !== SigningStatusEnum.Verified"
              v-model:design3-d="orderDataDetail.designContent.design3D"
              :sub-status="orderDataDetail.subStatus"
              :order-status="orderDataDetail.status"
              @refresh-data="getOrderData()"
              ref="upload3DRef" />

    <!--    裝潢施工報價-->
    <div class="flex flex-col my-1 cus-border gap-4">
      <div class="flex justify-evenly text-2xl items-center">
        <p class=" font-bold text-color-primary text-center">裝潢施工報價</p>
      </div>
      <HezDivider />
      <div class="flex justify-start">
        <div class="flex flex-col">
          <div class="flex justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5 items-center">
              <p class="font-bold p-2">客戶的預算</p>
              <p class="font-bold text-black">{{ orderDataDetail.customerConstructionBudget }}</p>
            </div>
          </div>
          <div class="flex  justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5 items-center">
              <p class="font-bold p-2">設計師估價</p>
              <p class="font-bold text-black">{{ orderDataDetail.deignerConstructionEstimate }}</p>
            </div>
          </div>
          <div class="flex  justify-start text-lg text-color-primary mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5 items-center">
              <p class="font-bold p-2">裝潢施工費</p>
              <p v-if="orderDataDetail.designContent.constructionAmountDocs.length === 0" class="font-bold text-black">
                未上傳</p>
              <p v-else class="font-bold text-black">{{ orderDataDetail.latestConstructionAmountDoc.amount }}</p>
            </div>
          </div>
        </div>
      </div>
      <button class="w-full cus-btn button-padding text-xl"
              v-if="!(orderDataDetail.contract.signingStatus !== SigningStatusEnum.Verified || orderDataDetail.status === DesignOrderStatusEnum.Completed)"
              @click="openConstructionFeeModal()">
        資料上傳/修改
      </button>
    </div>

    <DesignHouseInfo v-model:house-info="orderDataDetail.measureContent.houseInfo"
                     v-model:publish-info="orderDataDetail.publishInfo"
                     v-model:name-address="orderDataDetail.nameAddress" />
    <HousePhoto v-model="orderDataDetail.measureContent.photos" :orderId="orderId" :disabled="true" />
    <HouseCheck v-model="orderDataDetail.measureContent.houseCheck" :orderId="orderId" :disabled="true" />
    <FloorPlan v-model="orderDataDetail.measureContent.floorPlan" :orderId="orderId" :disabled="true" />
    <TextContent v-model="orderDataDetail.measureContent.constructionRequest" :orderId="orderId" title="裝潢需求" contentName="constructionRequest" :canNotDeleteCount=16 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.waterQuality" :orderId="orderId" title="水質檢測" contentName="waterQuality" :canNotDeleteCount=6 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.airQuality" :orderId="orderId" title="空氣檢測" contentName="airQuality" :canNotDeleteCount=7 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.noise" :orderId="orderId" title="噪音檢測" subTitle="(安靜:50dB，吵雜:60dB)" contentName="noise" :canNotDeleteCount=1 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.humidity" :orderId="orderId" title="濕度檢測" subTitle="(正常:40~60%，潮濕:70%)" contentName="humidity" :canNotDeleteCount=1 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.radiation" :orderId="orderId" title="電磁輻射" contentName="radiation" :canNotDeleteCount=4 :disabled="true" :canAddUnit="false"/>
    <HouseNote v-model="orderDataDetail.measureContent.note" :orderId="orderId" :disabled="true" />
  </div>

  <AppUsePhoneModal :user-type="UserTypeEnum.Designer" ref="appUsePhoneModalRef" />
  <DesignerSignModal :title="signModalTitle" ref="signModal" />
  <DesignerRemitModal :title="remitModalTitle" ref="remitModal" />
  <NoteBook :room-id="orderData.chatRoomId" :is-designer="true" ref="noteBookRef" />

  <DefaultModal title="裝潢報價" :click-outside-close="false" :show-close-button="true" ref="constructionFeeModalRef"
                @close-modal="closeConstructionFeeModal()">

    <div class="flex flex-col gap-2 p-4 mx-auto">
      <div class="flex items-center mb-4">
        <label class="font-bold text-black">裝潢施工報價$</label>
        <input v-model="tempConstructionAmount" type="text" inputmode="numeric"
               @change="cleanTempConstructionAmount()"
               class="input-basic rounded-md ml-2 w-16 text-center" />
        <span class="ml-2">萬</span>
      </div>
      <div class="flex items-center mb-4">
        <p class="font-bold text-black mr-2">裝潢報價清單</p>
        <button class="bg-gray-300 text-black px-4 py-2 rounded-md flex flex-col items-center"
                @click="triggerFileInput()">
          <img src="/vectors/general/upload.svg" alt="" class="h-6 w-6 mr-2">
          上傳報價清單
        </button>
        <input type="file" ref="documentUrlInput"
               required @change="event => imageUpload(event)" class="hidden">
      </div>
      <div class="flex items-start mb-4">
        <p class="text-black mr-2">報價清單</p>
        <div class="flex flex-col gap-2">
          <div v-for="(file,index) in [...orderDataDetail.designContent.constructionAmountDocs].reverse()" :key="index"
               class="flex gap-2 items-center">
            <div class="border-2 border-black px-4 py-2 cursor-pointer" @click="handleDownloadFile(file.documentUrl)">
              <p>第{{ orderDataDetail.designContent.constructionAmountDocs.length - index }}次
                {{ moneyAddCommas(file.amount) }} 上傳日
                {{ formatFullDate(file.createTime) }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="text-start text-red-600">
        <p>
          註1：如委託人選擇相同的設計師進行裝潢施工，委託人將獲得同室內設計費用的裝潢施工減免，即從設計師的裝潢施工的費用扣除。</p>
        <p class="mt-2">
          註2：施工報價的最高金額未經委託人同意不得超過{{ orderDataDetail.deignerConstructionEstimateUpper }}元，否則將以設計費用的50%作為補償給委託人，若事先徵求委託人同意即可免除補償費。</p>
        <p class="mt-2">註3：設計師提供10%的室內設計費用作為平台使用費，此費用包含平台開立發票給委託人費用。</p>
      </div>
    </div>
  </DefaultModal>

  <AndroidNotSupportSharedWorkerModal :is-designer="true" ref="sharedWorkerNotSupportModalRef" />
</template>
