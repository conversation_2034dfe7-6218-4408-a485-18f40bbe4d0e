import type { Budget } from '@/model/general/budget';

/**
 * @description 預算金額格式化成 "幾萬-幾萬"
 */
export const budgetCombineLowToHigh = (budgets: Budget): string => {
  const result = `${MoneyToTenThousand(budgets.lower)} ~ ${MoneyToTenThousand(budgets.upper)}`;
  return result as string;
};

/**
 * @description 預算金額阿拉伯數字轉成幾萬
 */
export const MoneyToTenThousand = (money: number): string => {
  // 4捨5入到小數點第二位
  return `${roundTo((money / 10000), 2)}萬` as string;
};

export const constructionBudgetFormat = (amount: number, discountedAmount: number): string => {
  const moneyCommas = moneyAddCommas(discountedAmount);
  if (amount === discountedAmount) {
    return moneyCommas;
  } else {
    return `${moneyCommas} (已折抵)`;
  }
};

export const moneyAddCommas = (money: number): string => {
  const result = new Intl.NumberFormat('en-US').format(money);
  return `NT$ ${result}`;
};

export const calculatePercentage = (amount: number, total: number): string => {
  if (total === 0) return '0.00%'; // 避免除以0的情況
  const percentage = (amount / total) * 100;
  const roundedPercentage = roundTo(percentage, 2);
  return `${roundedPercentage.toFixed(2)}%`;
};

export const roundTo = (num: number, decimal: number) => {
  return Math.round((num + Number.EPSILON) * Math.pow(10, decimal)) / Math.pow(10, decimal);
};