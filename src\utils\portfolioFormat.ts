import {
  GetSingleDesignerPageResponse,
  PortfolioItem
} from '@/model/response/portfolioResponse.ts';
import { IntroductionFormated } from '@/model/formatted/portolio.ts';
import { floatToPercent } from '@/utils/generalFormat.ts';
import { regionEnumArrayToChinese } from '@/utils/regionFormat.ts';

export const portfolioDataFormat = (data: GetSingleDesignerPageResponse): {
  introduction: IntroductionFormated,
  portfolios: PortfolioItem[],
} => {
  const renderIntroductionData: IntroductionFormated = {
    avatarUrl: data.introduction.designerAvatar ? data.introduction.designerAvatar : '/vectors/general/avatar.svg',
    designerName: data.introduction.designerName ? data.introduction.designerName : '尚未驗證的設計師',
    satisfaction: data.introduction.satisfaction === -1 ? '尚無評價' : floatToPercent(data.introduction.satisfaction),
    portfolioCount: data.introduction.portfolioCount,
    hasConstructionTeam: data.introduction.hasConstructionTeam ? '有' : '無',
    region: regionEnumArrayToChinese(data.introduction.region),
    website: data.introduction.webSite,
    companyName: data.introduction.companyName,
    companyUnifiedBusinessNumber: data.introduction.companyUnifiedBusinessNumber,
    companyServiceTime: data.introduction.companyServiceTime
  };
  return { introduction: renderIntroductionData, portfolios: data.portfolios };
};
