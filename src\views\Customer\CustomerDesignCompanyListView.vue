<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { PortfolioService } from '@/api/portfolio.ts';
import { DesignCompanyItem } from '@/model/response/portfolioResponse.ts';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import { regionEnumArrayToChinese } from '@/utils/regionFormat.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

const directToStep1Modal = ref<InstanceType<typeof DefaultModal> | null>(null);
const modalTextAndAvatar = ref<{
  avatar: string;
  text: string;
}>({ avatar: '', text: '' });
const companyData = ref<DesignCompanyItem[]>();
const router = useRouter();

const openDirectToStep1Modal = (avatar: string | undefined, text: string) => {
  if (avatar === undefined) return;
  modalTextAndAvatar.value = { avatar: avatar, text: text };
  directToStep1Modal.value?.openModal();
};

const closeDirectToStep1Modal = () => {
  goToAskService();
  directToStep1Modal.value?.closeModal();
};

const goToCustomerPortfolio = (designerId: string | undefined) => {
  if (!designerId) return;
  router.push({
    name: 'customerportfolio',
    params: {
      designerId: designerId
    }
  });
};

const goToAskService = () => {
  router.push({
    name: 'askservice'
  });
};

const designCompanyData = computed(() => {
  return companyData.value?.map((item) => {
    let workTypeName = '';
    if (item.workType.designer && item.workType.decorator) {
      workTypeName = '室內設計、裝潢施工';
    } else if (item.workType.decorator) {
      workTypeName = '室內設計';
    } else if (item.workType.designer) {
      workTypeName = '裝潢施工';
    }
    const citys = regionEnumArrayToChinese(item.workRegion);
    const displayCity = citys.length > 3 ? citys.slice(0, 3) : citys;

    return {
      designerId: item.designerId,
      designerAvatar: item.designerAvatar,
      companyLogo: item.companyLogo,
      companyName: item.companyName,
      companyUnifiedBusinessNumber: item.companyUnifiedBusinessNumber,
      designerName: item.designerName,
      companyAddress: item.companyAddress,
      workCitys: citys,
      displayCity: displayCity,
      companyServiceTime: item.companyServiceTime,
      workType: workTypeName,
      portfolioCount: item.portfolioCount
    };
  });
});

onMounted(async () => {
  const result = await PortfolioService.getDesignCompanyList({});
  companyData.value = result.designers;
});
</script>

<template>
  <div class="w-full">
    <div class="flex flex-col space-y-12 max-md:p-4 md:my-12" v-if="designCompanyData?.length === 0">
      <div class="cus-border">
        <p class="text-center">無設計公司可顯示</p>
      </div>
    </div>

    <div class="flex flex-col space-y-12 max-md:p-4 md:my-12" v-else>
      <div v-for="company in designCompanyData" :key="company?.designerId"
           class="cus-border flex flex-col md:gap-y-8">
        <div class="flex justify-between gap-x-2 max-md:flex-col md:divide-x max-md:divide-y divide-black">
          <div class="flex flex-col justify-center items-center md:text-lg md:p-4 max-md:py-4 gap-4">
            <div class="h-20 w-20 md:h-40 md:w-40">
              <img v-if="company?.designerAvatar" :src="company?.designerAvatar" alt=""
                   class="w-full h-full rounded-full">
              <UserCircleIcon v-else class="w-full h-full rounded-full" />
            </div>
            <p>設計師</p>
            <p>{{ company?.designerName }}</p>
            <div class="flex items-center">
              <p class="mr-4">通過公司認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
            <div class="flex items-center">
              <p class="mr-4">通過身份認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
          </div>

          <div class="md:w-4/5 flex flex-col justify-center md:text-lg md:p-4 max-md:py-4 gap-2">
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">公司名稱</p>
              <p class="break-all">{{ company.companyName }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">統一編號</p>
              <p class="break-all">{{ company.companyUnifiedBusinessNumber }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">服務時間</p>
              <p class="break-all">{{ company.companyServiceTime }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">服務類別</p>
              <p class="break-all">{{ company.workType }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">作品數量</p>
              <p class="break-all">{{ company.portfolioCount }}件</p>
            </div>
            <p>服務地區</p>
            <div class="w-full flex gap-2 items-center flex-wrap">
              <div v-for="city in company.workCitys" :key="city"
                   class=" p-2 border border-gray-400 rounded-xl text-nowrap">
                <p>{{ city }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex max-md:flex-col gap-2 px-2 justify-between">
          <button
            class="cus-btn md:w-1/4 md:text-lg text-nowrap"
            @click="openDirectToStep1Modal(company?.designerAvatar,'電話聯絡')">電話聯絡
          </button>
          <button
            class="cus-btn md:w-1/4 md:text-lg text-nowrap"
            @click="openDirectToStep1Modal(company?.designerAvatar,'立即聯絡')">立即聯絡
          </button>
          <button
            class="cus-btn md:w-1/4 md:text-lg text-nowrap"
            @click="goToCustomerPortfolio(company?.designerId)">設計師作品
          </button>
        </div>

      </div>
    </div>
  </div>

  <DefaultModal :title="modalTextAndAvatar.text" :click-outside-close="false" :show-close-button="true"
                @closeModal="directToStep1Modal?.closeModal()" modalWidth="max-w-lg" ref="directToStep1Modal">
    <div class="flex flex-col gap-y-2 m-3 text-black items-center">
      <img v-if="modalTextAndAvatar.avatar" :src="modalTextAndAvatar.avatar" alt=""
           class="h-16 w-16 mr-4 rounded-full">
      <p>在{{ modalTextAndAvatar.text }}設計師之前</p>
      <p>請讓我們先了解您的裝潢需求</p>
      <p>這些資訊將幫助設計師更完整了解需求</p>
      <button type="button"
              class="button-basic w-full bg-color-selected hover:opacity-80"
              @click="closeDirectToStep1Modal()">下一步
      </button>
    </div>
  </DefaultModal>
</template>
