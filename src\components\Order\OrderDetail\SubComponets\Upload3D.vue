<script setup lang="ts">
import { computed, ref } from 'vue';
import { ImageVideoKeyContent, ImageVideoKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import MediaModal from '@/components/General/MediaModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import Update3DModal from '@/components/Order/OrderDetail/UpdateModal/Update3DModal.vue';

const design3D = defineModel<ImageVideoKeyContent>('design3D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus');
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus');
const emits = defineEmits(['refreshData']);
const updateModalRef = ref<InstanceType<typeof Update3DModal> | null>(null);
const mediaModalRef = ref<InstanceType<typeof MediaModal> | null>(null);

const props = defineProps<{
  orderId: string;
  disabled: boolean;

}>();

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const openMediaModal = (imageVideoItem: ImageVideoKeyItem) => {
  mediaModalRef.value?.openMediaModal(imageVideoItem);
};

const subStatusForUpdate = computed(() => {
  if (subStatus.value) {
    return subStatus.value;
  } else {
    return DesignOrderSubStatusEnum.Design2DAgree;
  }
});

const orderStatusForUpdate = computed(() => {
  if (orderStatus.value) {
    return orderStatus.value;
  } else {
    return DesignOrderStatusEnum.Comparing;
  }
});

defineExpose({ openUpdateModal });
</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">3D模型設計</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div v-if="design3D.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col" v-else>
        <div v-for="imageVideoItem in design3D.content" :key="imageVideoItem.key"
             class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold p-2">{{ imageVideoItem.name }}</p>
            <div v-if="imageVideoItem.media.length === 0" class="flex">
              <p class="text-black">未上傳</p>
            </div>
            <div v-else class="flex">
              <button class="cus-btn button-padding text-base"
                      v-if="imageVideoItem.type === MediaTypeEnum.Video" @click="openMediaModal(imageVideoItem)">
                查看影片
              </button>
              <button class="cus-btn button-padding text-base"
                      v-else-if="imageVideoItem.type === MediaTypeEnum.Image" @click="openMediaModal(imageVideoItem)">
                查看圖片
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="w-full cus-btn button-padding text-xl"
            v-if="!disabled" @click="openUpdateModal()">
      資料上傳/修改
    </button>
  </div>
  <Update3DModal title="3D模型設計"
                 :order-id="props.orderId"
                 v-model:design3-d="design3D"
                 v-model:sub-status="subStatusForUpdate"
                 v-model:order-status="orderStatusForUpdate"
                 ref="updateModalRef"
                 @refresh-data="emits('refreshData')" />
  <MediaModal ref="mediaModalRef" />
</template>
