<script setup lang="ts">
import { computed, nextTick, reactive, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import {
  MediaUrlKeyItem, ProcessKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { toastError, toastInfo, toastSuccess } from '@/utils/toastification.ts';
import { ConstructionOrderDetailService } from '@/api/designerOrder.ts';
import { TrashIcon } from '@heroicons/vue/20/solid';
import HezDivider from '@/components/General/HezDivider.vue';
import { UploadImageVideoType } from '@/utils/hezParameters.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import {
  ConstructionProcessStatusEnum
} from '@/model/enum/orderStatus.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const workProcess = defineModel<ProcessKeyItem>('workProcess', { required: true });
const workAmount = defineModel<string>('workAmount', { required: true });
const isAnyWorkChecking = defineModel<boolean>('isAnyWorkChecking', { required: true });

const emits = defineEmits(['refreshData', 'changeApiLoading']);
const props = defineProps<{ orderId: string; }>();

const mediaDetailEditModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const mediaDetailEditData = ref<ProcessKeyItem>({
  key: '',
  updateTime: '',
  name: '',
  updateCount: 0,
  status: ConstructionProcessStatusEnum.NotRequested,
  amount: 0,
  media: [] as MediaUrlKeyItem[],
  isAppend: false,
  amountDocUrl: ''
});
const isAPILoading = ref<boolean>(false);
const reFreshSwiper = ref<boolean>(false);
const fileInputElement = reactive<{
  //因為我用了v-for產生多個 檔案input 所以需要搞一個用media.key去便是他們各自獨立性的 reactive，不然所有input ref都是同一個會衝突
  [key: string]: HTMLInputElement | null;
}>({});

const clearMediaDetailEditData = () => {
  mediaDetailEditData.value = {
    key: '',
    updateTime: '',
    name: '',
    updateCount: 0,
    status: ConstructionProcessStatusEnum.NotRequested,
    amount: 0,
    media: [] as MediaUrlKeyItem[],
    isAppend: false,
    amountDocUrl: ''
  };
};

const openMediaDetailEditModal = (mediaItem: ProcessKeyItem) => {
  if (mediaItem.media.length === 0) {
    toastInfo('請先上傳圖片或影片');
    return;
  }
  mediaDetailEditData.value = mediaItem;
  mediaDetailEditModalRef.value?.openModal();
};
const closeMediaDetailEditModal = () => {
  mediaDetailEditModalRef.value?.closeModal();
  setTimeout(() => {
    clearMediaDetailEditData();
  }, 300);
};

const finishProcess = async () => {
  if (isAnyWorkChecking.value) {
    toastInfo('尚有項目驗收中，請驗收完成後再請款');
    return;
  }
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await ConstructionOrderDetailService.finishProcess({
      orderId: props.orderId,
      processKey: workProcess.value.key
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
    }
    isAPILoading.value = false;
  }
};

const deleteMediaDetail = async (mediaItemKey: string, mediaDetailKey: string) => {
  if (workProcess.value.status === ConstructionProcessStatusEnum.PendingDisbursement) {
    toastInfo('客戶驗收中，無法刪除照片');
    return;
  }
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await ConstructionOrderDetailService.updateProcess({
      orderId: props.orderId,
      builders: [{
        key: mediaItemKey,
        pushImage: [],
        pullImage: [mediaDetailKey],
        setImageDescription: []
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      const resultMedia = result.content.process.content.find(item => item.key === mediaItemKey);
      const resultMediaLength = resultMedia ? resultMedia.media.length : 0;
      //如果刪掉項目最後一張照片就直接退出Modal
      if (resultMediaLength === 0) {
        closeMediaDetailEditModal();
        setTimeout(() => {
          clearMediaDetailEditData();
        }, 300);
      } else {
        reFreshSwiper.value = true;
        mediaDetailEditData.value = result.content.process.content.find(item => item.key === mediaDetailEditData.value.key) as ProcessKeyItem;
        await nextTick();
        reFreshSwiper.value = false;
      }
    } else if (result.status === APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete) {
      toastInfo('驗收已完成，無法刪除照片');
    }
    isAPILoading.value = false;
  }
};

const editMediaDetailDescription = async (mediaDetailKey: string, description: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await ConstructionOrderDetailService.updateProcess({
      orderId: props.orderId,
      builders: [{
        key: mediaDetailEditData.value.key,
        pushImage: [],
        pullImage: [],
        setImageDescription: [{
          key: mediaDetailKey,
          description: description
        }]
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      mediaDetailEditData.value = result.content.process.content.find(item => item.key === mediaDetailEditData.value.key) as ProcessKeyItem;
    }
    isAPILoading.value = false;
  }
};

const triggerFileInput = (key: string) => {
  fileInputElement[key]?.click(); // 因為實現點擊div觸發上傳圖片，觸發 input 的 fileUpload
};

const fileUpload = async (event: Event, mediaItem: ProcessKeyItem) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    emits('changeApiLoading', true);
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.Picture)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      emits('changeApiLoading', false);
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    const pushMedia = S3Urls.map(url => {
      return {
        url: url,
        description: ''
      };
    });
    const result = await ConstructionOrderDetailService.updateProcess({
      orderId: props.orderId,
      builders: [{
        key: mediaItem.key,
        pushImage: pushMedia,
        pullImage: [],
        setImageDescription: []
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      toastSuccess('檔案上傳成功');
      emits('refreshData'); // 叫父元件刷新資料
    }
  }
  emits('changeApiLoading', false);
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const isWorkProcessValid = computed(() => {
  if (!workProcess.value || !workProcess.value.media) {
    return false;
  }
  return workProcess.value.media.length > 0;
});

const mediaDetailInfo = computed(() => {
  return {
    key: mediaDetailEditData.value.key,
    name: mediaDetailEditData.value.name,
    media: mediaDetailEditData.value.media.map((media) => {
      return {
        key: media.key,
        url: media.url,
        description: media.description,
        updateTime: formatFullDateTimeWithDay(media.updateTime)
      };
    }),
    updateTime: formatFullDateTimeWithDay(mediaDetailEditData.value.updateTime)
  };
});
</script>

<template>
  <div class="flex-col m-3">
    <div class="flex flex-col">
      <div class="flex justify-start px-5 mb-2">
        <p class="text-xl mr-2">{{ workProcess.name }}</p>
        <p class="text-xl">{{ workAmount }}</p>
      </div>
      <div class="flex justify-evenly">
        <div
          @click="workProcess.media.length >0?openMediaDetailEditModal(workProcess):triggerFileInput(workProcess.key)"
          class="flex flex-col w-2/3 gap-y-0.5 p-8 bg-gray-200 justify-center items-center cursor-pointer max-md:p-2">
          <div class="flex flex-col justify-center items-center">
            <img v-if="workProcess.media.length >0" src="/vectors/order/uploadFile/photo_uploaded.svg" alt=""
                 class="w-14 h-14" />
            <img v-else src="/vectors/order/uploadFile/photo_xx.svg" alt="" class="w-14 h-14" />
            <p>完工證明圖片({{ workProcess.media.length }})</p>
          </div>
        </div>
        <div @click="triggerFileInput(workProcess.key)"
             class="flex flex-col w-1/4 gap-y-0.5 p-8 bg-color-secondary justify-center items-center cursor-pointer max-md:p-2">
          <img src="/vectors/general/upload.svg" alt="addImage" class="w-7 h-7" />
          <p>上傳圖片</p>
        </div>
        <input type="file" :ref="el => (fileInputElement[workProcess.key] = el as HTMLInputElement)"
               :accept="UploadImageVideoType"
               multiple required @change="event => fileUpload(event,workProcess)" class="hidden">
      </div>
      <hez-divider />
    </div>
    <div
      class="flex max-md:flex-col w-full gap-2">
      <template v-if="workProcess.status === ConstructionProcessStatusEnum.NotRequested">
        <button v-if="isWorkProcessValid"
                type="button" class="button-basic w-full bg-color-selected hover:opacity-80 "
                @click="finishProcess()">完工請款
        </button>
        <button v-else type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finishProcess()">未完成
        </button>
      </template>
      <template v-if="workProcess.status === ConstructionProcessStatusEnum.PendingDisbursement">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finishProcess()">驗收中
        </button>
      </template>
      <template v-if="workProcess.status === ConstructionProcessStatusEnum.Reject">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finishProcess()">客戶要求修正
        </button>
      </template>
      <template v-if="workProcess.status === ConstructionProcessStatusEnum.Completed">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finishProcess()">客戶已同意撥款
        </button>
      </template>
      <template v-if="workProcess.status === ConstructionProcessStatusEnum.InsufficientBalance">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finishProcess()">客戶匯款不足
        </button>
      </template>

    </div>
  </div>
  <!--預覽-對話框-->
  <DefaultModal title="預覽" :show-close-button="true" :click-outside-close="false" ref="mediaDetailEditModalRef"
                @closeModal="closeMediaDetailEditModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true" v-if="!reFreshSwiper">
          <swiper-slide v-for="mediaDetail in [...mediaDetailInfo.media].reverse()" :key="mediaDetail.key"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ mediaDetail.updateTime }}</p>
            <img :src="mediaDetail.url" alt="" class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <div class="flex justify-between items-center">
              <input type="text"
                     class="relative block border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
                     v-model="mediaDetail.description"
                     @change="editMediaDetailDescription(mediaDetail.key, mediaDetail.description)"
                     placeholder="請輸入註解文字" />
              <div class="flex flex-col justify-center cursor-pointer hover-zoom"
                   @click="deleteMediaDetail(mediaDetailInfo.key,mediaDetail.key)">
                <div class="flex justify-center">
                  <TrashIcon class="w-7 h-7" />
                </div>
                <p>刪除此圖片</p>
              </div>
            </div>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>

</template>
