import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getCustomerGuestToken, SecurityService } from '@/api/security.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode';
import { decryptAES, encryptAES, hashPassword } from '@/utils/crypto';
import { toastError, toastInfo, toastSuccess } from '@/utils/toastification.ts';
import type { LoginData } from '@/model/general/security';
import { deviceInfo } from '@/utils/userDeviceInfo.ts';
import router from '@/router';

export const useCustomerInfoStore = defineStore('CustomerInfo', () => {
    const guestToken = ref('');
    const loginState = ref<boolean>(false);
    const userId = ref<string>('');
    const hasEverLogin = ref<boolean>(false);
    const rememberMe = ref<boolean>(true);
    const loginCipher = ref<{ phone: string; password: string; }>({
      phone: '',
      password: ''
    });

    const getGuestToken = async () => {
      const result = await getCustomerGuestToken(deviceInfo);

      if (result.status !== APIStatusCodeEnum.Success) {
        toastError('取得客戶Token失敗');
      } else {
        guestToken.value = result.token;//會觸發App.vue中的$subscribe
        // 如果已經登入，則重新登入
        if (loginState.value) {
          await login({
            phone: decryptAES(loginCipher.value.phone),
            password: decryptAES(loginCipher.value.password),
            rememberForShow: rememberMe.value
          });
        }
        console.log('已重新取得客戶Token');
      }
    };

    const registerSuccess = (id: string, phone: string, password: string) => {
      userId.value = id;
      loginCipher.value.phone = encryptAES(phone);
      loginCipher.value.password = encryptAES(password);
      loginState.value = true;
      hasEverLogin.value = true;
    };

    const login = async (data: LoginData): Promise<boolean> => {
      const ciphertext = await hashPassword(data.password);
      const loginResult = await SecurityService.userLoginByCustomer({
        phone: data.phone,
        password: ciphertext
      });

      if (loginResult.status === APIStatusCodeEnum.PhoneOrPasswordError) {
        toastError('帳號或密碼錯誤');
        return false;
      }
      loginState.value = true;
      userId.value = loginResult.userId;

      loginCipher.value = {
        phone: encryptAES(data.phone),
        password: encryptAES(data.password)
      };
      rememberMe.value = data.rememberForShow;

      hasEverLogin.value = true;
      toastSuccess('登入成功');
      localStorage.setItem('needToBeRefresh', JSON.stringify(['customer']));
      return true;
    };

    const logout = async () => {
      if (loginState.value === false) {
        console.error('使用者未登入');
        return;
      }
      try {
        await SecurityService.LogoutByCustomer({});
        toastInfo('登出成功');
        clearLoginStore();
        await getGuestToken();
        await router.push({ name: 'customerhome' });
        localStorage.setItem('needToBeRefresh', JSON.stringify(['customer', 'meeting']));
      } catch (error) {
        toastError('登出失敗');
      }
    };

    const clearLoginStore = () => {
      loginState.value = false;
      userId.value = '';
    };

    const clearPassword = () => {
      loginCipher.value = { phone: '', password: '' };
      rememberMe.value = true;
    };

    const clearGuestToken = () => {
      guestToken.value = '';
    };

    return {
      guestToken,
      loginState,
      userId,
      hasEverLogin,
      loginCipher,
      rememberMe,
      getGuestToken,
      registerSuccess,
      login,
      logout,
      clearLoginStore,
      clearPassword,
      clearGuestToken
    };
  },
  {
    persist: true
  }
);

export const useStep1PublishTitleStore = defineStore('step1PublishTitle', () => {
  const title = ref<{
    mainTitle: string;
    subTitle: string;
  }>({
    mainTitle: '為順利的完成到府丈量服務',
    subTitle: ''
  });

  const setTitle = (mainTitle: string, subTitle: string) => {
    title.value = {
      mainTitle,
      subTitle
    };
  };

  const resetTitle = () => {
    title.value = {
      mainTitle: '為順利的完成到府丈量服務',
      subTitle: ''
    };
  };

  return { title, setTitle, resetTitle };
});

export const customerStep1PublishStore = defineStore('customerStep1Publish', () => {
    const measureData = ref<{
      address: {
        name: string;
        location: {
          lat: number;
          lng: number;
        }
      };
      username: string;
      measureTimes: string[];
    }>({
      address: {
        name: '',
        location: {
          lat: 0,
          lng: 0
        }
      },
      username: '',
      measureTimes: []
    });

    const updateData = (data: {
      address: {
        name: string;
        location: {
          lat: number;
          lng: number;
        }
      };
      username: string;
      measureTimes: string[];
    }) => {
      measureData.value = data;
    };

    const cleanData = () => {
      measureData.value = {
        address: {
          name: '',
          location: {
            lat: 0,
            lng: 0
          }
        },
        username: '',
        measureTimes: []
      };
    };

    return { measureData, updateData, cleanData };
  }, {
    persist: true
  }
);
