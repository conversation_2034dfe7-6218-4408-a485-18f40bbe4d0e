<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';
import DesignerAppDownload from '@/components/General/DesignerAppDownload.vue';

defineProps<{ title: string; }>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="title" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">目前客戶未匯款</p>
        <p class="font-bold md:text-lg">請確定客戶已匯款後</p>
        <p class="font-bold md:text-lg">才開始著手設計</p>
        <p class="font-bold md:text-lg">避免做白工的情況發生</p>
        <p class="font-bold md:text-lg">如果要更好的互動體驗 可以在App端操作</p>
      </div>
      <DesignerAppDownload />
    </div>
  </DefaultModal>
</template>
