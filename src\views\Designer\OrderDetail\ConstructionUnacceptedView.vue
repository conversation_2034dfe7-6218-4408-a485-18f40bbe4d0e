<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import HezDivider from '@/components/General/HezDivider.vue';
import {
  ConstructionAmountItem,
  ImagePdfCadKeyItem,
  ImageVideoKeyItem,
  TextKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { ConstructionOrderUnacceptedDetailItem } from '@/model/response/orderDetail/orderUnacceptedDetailResponse.ts';
import { ConstructionOrderDetailService, OrderDetailService } from '@/api/designerOrder.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import HousePhoto from '@/components/Order/OrderDetail/SubComponets/HousePhoto.vue';
import HouseCheck from '@/components/Order/OrderDetail/SubComponets/HouseCheck.vue';
import FloorPlan from '@/components/Order/OrderDetail/SubComponets/FloorPlan.vue';
import HouseNote from '@/components/Order/OrderDetail/SubComponets/HouseNote.vue';
import TextContent from '@/components/Order/OrderDetail/SubComponets/TextContent.vue';
import { budgetCombineLowToHigh, moneyAddCommas, MoneyToTenThousand } from '@/utils/budgetFormat.ts';
import { parseJsonString } from '@/utils/JsonStringFormat.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { AmountStatusEnum, DesignerConstructionQuoteStatusEnum } from '@/model/enum/orderStatus.ts';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import { toastError, toastWarning } from '@/utils/toastification.ts';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import Upload3D from '@/components/Order/OrderDetail/SubComponets/Upload3D.vue';
import Upload2D from '@/components/Order/OrderDetail/SubComponets/Upload2D.vue';
import ConstructionHouseInfoWithSixPack from '@/components/Order/OrderDetail/SubComponets/ConstructionHouseInfoWithSixPack.vue';
import ConstructionHouseInfo from '@/components/Order/OrderDetail/SubComponets/ConstructionHouseInfo.vue';
import { formatFullDate } from '@/utils/timeFormat.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { downloadFile } from '@/utils/fileDownloader.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import {
  constructionLowerAmountMax,
  constructionLowerAmountMin,
  constructionUpperAmountMin,
  initConstructionLowerAmountInput,
  initConstructionUpperAmountInput
} from '@/utils/orderQuoteService.ts';
import { formatInteger } from '@/utils/numberFormat.ts';

const orderData = ref<ConstructionOrderUnacceptedDetailItem>({
  orderId: '',
  createTime: '',
  refreshTime: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  publishInfo: {
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    constructionNote: '',
    keyDesignDetail: '',
    referenceImages: [],
    isContinuedDesigner: false
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    houseDetection: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    }
  },
  designContent: {
    design2D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    design3D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    //延續前設計師才用這個
    constructionAmountDocs: [] as ConstructionAmountItem[]
  },
  designerId: '',
  customerId: '',
  customerAvatarUrl: '',
  customerPhone: '',
  contract: {
    constructionEstimate: {
      upper: 0,
      lower: 0
    },
    //是否為詳細報價階段
    isDetailQuoted: false,
    amountStatus: AmountStatusEnum.NotQuoted,
    //沒有延續前設計師才用這個
    constructionAmountDocs: [] as ConstructionAmountItem[],
    constructionAmount: -1,
    constructionDiscountAmount: -1
  },
  chatRoomId: ''
});

const route = useRoute();
const router = useRouter();
const designerInfoStore = useDesignerInfoStore();
const chatHubStore = useChatHubStore();
const orderId = route.params.id as string;
const rejectOrderModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const updateQuoteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal>>();
const constructionFeeModalRef = ref<InstanceType<typeof DefaultModal>>();
const tempConstructionAmount = ref<number>(0); //單位是萬
const documentUrlInput = ref<HTMLInputElement | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);
const stepEnabled = ref<boolean>(true);
const stepStatusMessage = ref<string>('');

const getOrderData = async () => {
  const res = await OrderDetailService.getConstructionUnaccepted({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    orderData.value = res.result;
  } else if (res.status === APIStatusCodeEnum.OrderStatusError) {
    // 跳轉至已接單的路由
    await router.push({ name: 'ConstructionAcceptedOrderDetail', params: { id: orderId } });
  }
};

const orderDataDetail = computed(() => {
  const mapJsonString = orderData.value.measureContent.houseInfo.content.map(parseJsonString);
  const sixPackHousePing = mapJsonString.find((item) => item.name === '室內坪數')?.text.content;
  const sixPackHouseAge = mapJsonString.find((item) => item.name === '房屋年齡')?.text.content;
  const sixPackHouseType = mapJsonString.find((item) => item.name === '房屋類型')?.text.content;
  return {
    orderId: orderData.value.orderId,
    customerName: orderData.value.customerName,
    address: orderData.value.contract.isDetailQuoted
      ? orderData.value.address.fullName
      : `${orderData.value.address.simpleName}***`,
    publishInfoForHouseInfo: orderData.value.publishInfo,
    publishInfo: {
      spaceUsage: orderData.value.publishInfo.spaceUsage,
      designStyle: orderData.value.publishInfo.designStyle,
      designTheme: orderData.value.publishInfo.designTheme,
      constructionBudget: budgetCombineLowToHigh(orderData.value.publishInfo.constructionBudget),
      constructionNote: orderData.value.publishInfo.constructionNote,
      referenceImages: orderData.value.publishInfo.referenceImages
    },
    measureContent: orderData.value.measureContent,
    designContent: orderData.value.designContent,
    contract: {
      constructionEstimate: budgetCombineLowToHigh(orderData.value.contract.constructionEstimate),
      constructionEstimateUpper: MoneyToTenThousand(orderData.value.contract.constructionEstimate.upper),
      isDetailQuoted: orderData.value.contract.isDetailQuoted,
      amountStatus: orderData.value.contract.amountStatus,
      //未接單跟報價中用到的裝潢施工詳細清單
      constructionAmountDocs: orderData.value.contract.constructionAmountDocs,
      constructionAmount: moneyAddCommas(orderData.value.contract.constructionAmount),
      constructionDiscountAmount: moneyAddCommas(orderData.value.contract.constructionDiscountAmount)
    },
    sixPack: {
      ping: `${sixPackHousePing?.text} ${sixPackHousePing?.unit}`,
      age: `${sixPackHouseAge?.text} ${sixPackHouseAge?.unit}`,
      houseType: sixPackHouseType?.text,
      spaceUsage: orderData.value.publishInfo.spaceUsage,
      designStyle: orderData.value.publishInfo.designStyle,
      designTheme: orderData.value.publishInfo.designTheme
    },
    areaPing: parseFloat(sixPackHousePing ? sixPackHousePing.text : '0'),
    quoteStatus:
      !orderData.value.contract.isDetailQuoted && !(orderData.value.contract.constructionEstimate.lower !== -1)
        ? DesignerConstructionQuoteStatusEnum.UNACCEPTED
        : !orderData.value.contract.isDetailQuoted && orderData.value.contract.constructionEstimate.lower !== 0
          ? DesignerConstructionQuoteStatusEnum.INIT_QUOTED
          : DesignerConstructionQuoteStatusEnum.DETAIL_QUOTED,
    designerId: orderData.value.designerId,
    customerId: orderData.value.customerId,
    chatroomId: orderData.value.chatRoomId,
    nameAddress: {
      name: orderData.value.customerName,
      address: orderData.value.address.fullName
    },
    latestConstructionAmountDoc:
      orderData.value.contract.constructionAmountDocs.length === 0
        ? {
            amount: '0', //報價金額
            amountNumber: 0, // 給input用
            time: '0', //時間
            documentUrl: '', //檔案URL
            count: 0 //第幾次
          }
        : {
            amount: moneyAddCommas(orderData.value.contract.constructionAmountDocs.slice(-1)[0].amount),
            amountNumber: orderData.value.contract.constructionAmountDocs.slice(-1)[0].amount,
            time: formatFullDate(orderData.value.contract.constructionAmountDocs.slice(-1)[0].createTime),
            documentUrl: orderData.value.contract.constructionAmountDocs.slice(-1)[0].documentUrl,
            count: orderData.value.contract.constructionAmountDocs.length - 1
          }
  };
});

const constructionUpperAmountInput = ref<string>('2');
const constructionLowerAmountInput = ref<string>('1');

const initQuotesInput = () => {
  constructionUpperAmountInput.value = initConstructionUpperAmountInput(
    orderData.value.contract.constructionEstimate.upper,
    orderDataDetail.value.areaPing
  ).toString();
  constructionLowerAmountInput.value = initConstructionLowerAmountInput(
    orderData.value.contract.constructionEstimate.lower,
    orderDataDetail.value.areaPing
  ).toString();
};

const openQuoteModal = () => {
  initQuotesInput();
  updateQuoteModalRef.value?.openModal();
};

const closeQuoteModal = () => {
  updateQuoteModalRef.value?.closeModal();
};

const constructionUpperAmountChange = () => {
  constructionUpperAmountInput.value = formatInteger(constructionUpperAmountInput.value); // 資料清理
  if (constructionUpperAmountInput.value === '') {
    constructionUpperAmountInput.value = initConstructionUpperAmountInput(
      orderData.value.contract.constructionEstimate.upper,
      orderDataDetail.value.areaPing
    ).toString();
  }
  // if (parseInt(constructionUpperAmountInput.value) < constructionUpperAmountMin(orderDataDetail.value.areaPing)) {
  //   toastInfo('裝潢報價的最高金額不得低於坪數乘以9');
  //   constructionUpperAmountInput.value = constructionUpperAmountMin(orderDataDetail.value.areaPing).toString();
  // }
  // // 因 裝潢報價(最低)上限->裝潢報價(最高) 所以會需要重新判斷裝潢報價(最低)是否符合條件
  // constructionLowerAmountChange();
};

const constructionLowerAmountChange = () => {
  constructionLowerAmountInput.value = formatInteger(constructionLowerAmountInput.value); // 資料清理
  if (constructionLowerAmountInput.value === '') {
    constructionLowerAmountInput.value = initConstructionLowerAmountInput(
      orderData.value.contract.constructionEstimate.lower,
      orderDataDetail.value.areaPing
    ).toString();
  }
  // if (parseInt(constructionLowerAmountInput.value) > constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value))) {
  //   toastInfo('裝潢報價的最低金額不得高於裝潢報價的最高金額');
  //   constructionLowerAmountInput.value = constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value)).toString();
  // }
  //
  // if (parseInt(constructionLowerAmountInput.value) < constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value))) {
  //   toastInfo('裝潢報價的最低金額需為最高金額的70%以上');
  //   constructionLowerAmountInput.value = constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value)).toString();
  // }
};

//TODO 裝潢施工報價 資料上傳糞Code 起始點

const triggerFileInput = () => {
  documentUrlInput.value?.click();
};

const imageUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.File)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    await uploadConstructionFee(S3Urls[0]);
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const uploadConstructionFee = async (url: string) => {
  const res = await ConstructionOrderDetailService.quoteDetail({
    orderId: orderId,
    amount: tempConstructionAmount.value,
    documentUrl: url
  });
  if (res.status === APIStatusCodeEnum.Success) {
    await getOrderData();
  }
};

const handleDownloadFile = async (url: string) => {
  await downloadFile(url);
};

const openConstructionFeeModal = () => {
  const initialAmount =
    orderDataDetail.value.latestConstructionAmountDoc.amountNumber === 0
      ? orderData.value.contract.constructionEstimate.lower
      : orderDataDetail.value.latestConstructionAmountDoc.amountNumber;
  tempConstructionAmount.value = Math.ceil(initialAmount / 10000);
  constructionFeeModalRef.value?.openModal();
};

const closeConstructionFeeModal = () => {
  const initialAmount =
    orderDataDetail.value.latestConstructionAmountDoc.amountNumber === 0
      ? orderData.value.contract.constructionEstimate.lower
      : orderDataDetail.value.latestConstructionAmountDoc.amountNumber;
  tempConstructionAmount.value = Math.ceil(initialAmount / 10000);
  constructionFeeModalRef.value?.closeModal();
};

const cleanTempConstructionAmount = () => {
  tempConstructionAmount.value = parseInt(formatInteger(tempConstructionAmount.value.toString())); // 資料清理
  if (isNaN(tempConstructionAmount.value) || tempConstructionAmount.value <= 0) tempConstructionAmount.value = 1;
};
//TODO 裝潢施工報價 資料上傳糞Code 結束點

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      appUsePhoneModalRef.value?.openModal();
      break;
    case contactItemEnum.Meet:
      createMeet(orderDataDetail.value.chatroomId, true, designerInfoStore.userId);
      break;
    case contactItemEnum.Chat:
      if (chatHubStore._worker === null) {
        sharedWorkerNotSupportModalRef.value?.openModal();
      } else {
        chatRoomRef.value?.showChatRoom();
      }
      break;
    case contactItemEnum.Note:
      noteBookRef.value?.openModal();
  }
};

const cancelQuote = async () => {
  const res = await ConstructionOrderDetailService.quoteCancel({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    rejectOrderModalRef.value?.closeModal();
    await router.push({ name: 'designerOrderList' });
  }
};

const rejectOrder = async () => {
  const res = await ConstructionOrderDetailService.rejectOrder({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    rejectOrderModalRef.value?.closeModal();
    await router.push({ name: 'designerOrderList' });
  }
};

const updateQuote = async () => {
  if (parseInt(constructionUpperAmountInput.value) < constructionUpperAmountMin(orderDataDetail.value.areaPing)) {
    toastWarning('裝潢報價的最高金額不得低於坪數乘以9');
    return;
  }

  if (
    parseInt(constructionLowerAmountInput.value) >
    constructionLowerAmountMax(parseInt(constructionUpperAmountInput.value))
  ) {
    toastWarning('裝潢報價的最低金額不得高於最高金額');
    return;
  }

  if (
    parseInt(constructionLowerAmountInput.value) <
    constructionLowerAmountMin(parseInt(constructionUpperAmountInput.value))
  ) {
    toastWarning('裝潢報價的最低金額需為最高金額的70%以上');
    return;
  }

  const res = await ConstructionOrderDetailService.quote({
    orderId,
    constructionEstimate: {
      upper: parseInt(constructionUpperAmountInput.value),
      lower: parseInt(constructionLowerAmountInput.value)
    }
  });
  if (res.status === APIStatusCodeEnum.Success) {
    await getOrderData();
    updateQuoteModalRef.value?.closeModal();
  } else if (res.status === APIStatusCodeEnum.StepFeatureClosed) {
    updateQuoteModalRef.value?.closeModal();
    stepEnabled.value = false;
    stepStatusMessage.value = '敬請期待功能開放';
  } else if (res.status === APIStatusCodeEnum.DesignerOrderDisabled) {
    updateQuoteModalRef.value?.closeModal();
    stepEnabled.value = false;
    stepStatusMessage.value = '接單權限已關閉';
  }
};

const goBack = () => {
  router.back();
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

onMounted(async () => {
  await getOrderData();
});
</script>

<template>
  <div v-if="!stepEnabled" class="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
    <div class="flex flex-col items-center rounded-2xl bg-white px-10 py-8 shadow-2xl">
      <span class="mb-4 text-2xl font-bold">{{ stepStatusMessage }}</span>
      <button class="cus-btn w-full" @click="goBack()">確認</button>
    </div>
  </div>

  <div class="my-8 flex w-full flex-col gap-y-8">
    <div class="cus-border text-lg">
      <h2 class="text-center text-2xl font-bold">裝潢施工</h2>
      <div class="flex flex-col gap-y-1 p-4">
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">屋主姓名</p>
          <p class="font-bold">{{ orderDataDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">裝潢地址</p>
          <p class="font-bold">{{ orderDataDetail.address }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">裝潢預算</p>
          <p class="font-bold">{{ orderDataDetail.publishInfo.constructionBudget }}</p>
        </div>
        <div
          class="flex gap-x-2"
          v-if="
            orderDataDetail.quoteStatus <= DesignerConstructionQuoteStatusEnum.INIT_QUOTED &&
            orderData.contract.constructionEstimate.lower !== -1
          "
        >
          <p class="text-nowrap font-bold">裝潢報價</p>
          <p
            v-if="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED"
            class="font-bold text-red-600"
          >
            {{ orderDataDetail.contract.constructionEstimate }}
          </p>
          <p v-else class="font-bold text-black">{{ orderDataDetail.contract.constructionAmount }}</p>
        </div>
      </div>
      <HezDivider />
      <template v-if="!orderDataDetail.contract.isDetailQuoted">
        <div class="rounded-lg p-4">
          <h3 class="mb-4 text-lg font-bold">設計重點</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">室內坪數</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.ping }}</p>
            </div>
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">裝修用途</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.spaceUsage }}</p>
            </div>
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">設計風格</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.designStyle }}</p>
            </div>
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">設計主題</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.designTheme }}</p>
            </div>
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">房屋年齡</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.age }}</p>
            </div>
            <div class="rounded-lg bg-gray-100 p-4 shadow-md">
              <p class="text-gray-600">房屋類型</p>
              <p class="break-all text-xl font-bold max-md:text-lg">{{ orderDataDetail.sixPack.houseType }}</p>
            </div>
          </div>
        </div>
        <HezDivider />
        <!-- 按鈕 -->
        <div class="flex justify-evenly gap-2 max-md:flex-col">
          <button
            class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
            @click="rejectOrderModalRef?.openModal"
          >
            <span v-if="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED">取消報價</span>
            <span v-else>無法接單</span>
          </button>
          <button
            class="bg-color-secondary hover:bg-color-selected w-full rounded-lg px-4 py-4 font-bold text-black shadow-md"
            @click="openQuoteModal()"
          >
            <span v-if="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED">更改報價</span>
            <span v-else>我要接單</span>
          </button>
        </div>
      </template>
      <template v-else>
        <div class="flex justify-around">
          <button
            class="hover-zoom flex flex-col items-center text-center"
            @click="contactItemClicked(contactItemEnum.Phone)"
          >
            <PhoneIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
            <span class="font-bold max-md:text-base">免費語音</span>
          </button>
          <button
            class="hover-zoom flex flex-col items-center text-center"
            @click="contactItemClicked(contactItemEnum.Meet)"
          >
            <VideoCameraIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
            <span class="font-bold max-md:text-base">視訊會議</span>
          </button>
          <button
            class="hover-zoom flex flex-col items-center text-center"
            @click="contactItemClicked(contactItemEnum.Chat)"
          >
            <ChatBubbleLeftEllipsisIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
            <span class="font-bold max-md:text-base">聊天室</span>
          </button>
          <button
            class="hover-zoom flex flex-col items-center text-center"
            @click="contactItemClicked(contactItemEnum.Note)"
          >
            <ClipboardDocumentListIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
            <span class="font-bold max-md:text-base">紀錄本</span>
          </button>
        </div>
      </template>
    </div>

    <div
      v-if="orderData.chatRoomId && chatHubStore._worker !== null"
      class="fixed bottom-0 right-0 z-40 flex w-80 flex-col justify-end"
      :class="isChatRoomShow ? 'h-3/5' : 'h-auto'"
    >
      <ChatRoom
        :is-for-meet="false"
        :room-id="orderData.chatRoomId"
        :is-designer="true"
        @show-chat-room="changeChatRoomShow"
        ref="chatRoomRef"
      />
    </div>

    <!--    裝潢施工報價-->
    <template v-if="orderDataDetail.contract.isDetailQuoted">
      <div class="cus-border my-1 flex flex-col gap-4">
        <div class="flex items-center justify-evenly text-2xl">
          <p class="text-color-primary text-center font-bold">裝潢施工報價</p>
        </div>
        <HezDivider />
        <div class="flex justify-start">
          <div class="flex flex-col">
            <div class="text-color-primary mb-4 flex justify-start text-lg">
              <div class="flex items-center gap-x-0.5 md:gap-x-2">
                <p class="p-2 font-bold">初始報價</p>
                <p class="font-bold text-black">{{ orderDataDetail.contract.constructionEstimate }}</p>
              </div>
            </div>
            <div class="text-color-primary mb-4 flex justify-start text-lg">
              <div class="flex items-center gap-x-0.5 md:gap-x-2">
                <p class="p-2 font-bold">最終報價</p>
                <p
                  v-if="orderDataDetail.contract.amountStatus === AmountStatusEnum.NotQuoted"
                  class="font-bold text-red-600"
                >
                  未提供
                </p>
                <p
                  v-else-if="orderDataDetail.contract.amountStatus === AmountStatusEnum.CustomerDisagreed"
                  class="font-bold text-red-600"
                >
                  客戶要求修正
                </p>
                <p v-else class="font-bold text-blue-600">{{ orderDataDetail.latestConstructionAmountDoc.amount }}</p>
              </div>
            </div>
          </div>
        </div>
        <button class="cus-btn button-padding w-full text-xl" @click="openConstructionFeeModal()">資料上傳/修改</button>
      </div>
    </template>

    <Upload2D :order-id="orderId" :disabled="true" v-model:design2-d="orderDataDetail.designContent.design2D" />
    <Upload3D :order-id="orderId" :disabled="true" v-model:design3-d="orderDataDetail.designContent.design3D" />
    <ConstructionHouseInfoWithSixPack
      v-if="!orderDataDetail.contract.isDetailQuoted"
      v-model:house-info="orderDataDetail.measureContent.houseInfo"
      v-model:publish-info="orderDataDetail.publishInfoForHouseInfo"
    />
    <ConstructionHouseInfo
      v-else-if="orderDataDetail.contract.isDetailQuoted"
      v-model:house-info="orderDataDetail.measureContent.houseInfo"
      v-model:publish-info="orderDataDetail.publishInfoForHouseInfo"
      v-model:name-address="orderDataDetail.nameAddress"
    />
    <HousePhoto v-model="orderDataDetail.measureContent.photos" :orderId="orderId" :disabled="true" />
    <HouseCheck v-model="orderDataDetail.measureContent.houseCheck" :orderId="orderId" :disabled="true" />
    <FloorPlan v-model="orderDataDetail.measureContent.floorPlan" :orderId="orderId" :disabled="true" />
    <TextContent
      v-model="orderDataDetail.measureContent.constructionRequest"
      :orderId="orderId"
      title="裝潢需求"
      contentName="constructionRequest"
      :canNotDeleteCount="16"
      :disabled="true"
      :canAddUnit="false"
    />
    <TextContent
      v-model="orderDataDetail.measureContent.houseDetection"
      :orderId="orderId"
      title="房屋檢測"
      contentName="houseDetection"
      :canNotDeleteCount="16"
      :disabled="true"
      :canAddUnit="false"
    />
    <HouseNote v-model="orderDataDetail.measureContent.note" :orderId="orderId" :disabled="true" />
  </div>

  <NoteBook :room-id="orderData.chatRoomId" :is-designer="true" ref="noteBookRef" />
  <DefaultModal
    :title="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED ? '取消報價' : '無法接單'"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="rejectOrderModalRef"
    @closeModal="rejectOrderModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">
          <span v-if="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED">
            當您選擇取消報價後
          </span>
          <span v-else>當您選擇拒絕這筆訂單後</span>
        </p>
        <p class="font-bold md:text-lg">該訂單將不再於您的訂單列表中顯示</p>
        <button
          type="button"
          v-if="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED"
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="cancelQuote()"
        >
          確定
        </button>
        <button
          type="button"
          v-else
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="rejectOrder()"
        >
          確定
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal
    :title="orderDataDetail.quoteStatus === DesignerConstructionQuoteStatusEnum.INIT_QUOTED ? '更改報價' : '確定報價'"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="updateQuoteModalRef"
    @closeModal="closeQuoteModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <!-- 裝潢報價 -->
        <div class="mb-4 w-full">
          <label class="mb-2 block font-bold text-gray-700">裝潢報價</label>
          <div class="mb-2 flex items-center">
            <span class="mr-2">最高$</span>
            <input
              v-model="constructionUpperAmountInput"
              type="text"
              inputmode="numeric"
              class="input-basic flex-1 rounded-md"
              @change="constructionUpperAmountChange()"
            />
            <span class="ml-2">萬</span>
          </div>
          <div class="flex items-center">
            <span class="mr-2">最低$</span>
            <input
              v-model="constructionLowerAmountInput"
              type="text"
              inputmode="numeric"
              class="input-basic flex-1 rounded-md"
              @change="constructionLowerAmountChange()"
            />
            <span class="ml-2">萬</span>
          </div>
          <p class="mt-2 text-start text-black">委託人預算：{{ orderDataDetail.publishInfo.constructionBudget }}</p>
        </div>

        <!-- 注意事項 -->
        <div class="mb-4 w-full">
          <p class="text-start text-sm text-black">
            註1：裝潢報價可以超過委託人預算，需包含廚具及空調等。裝潢報價的最高金額將來作為裝潢施工的最高金額，未經委託人同意不得任意更改，否則將以設計費用50%作為補償給客戶。
          </p>
          <p class="mt-2 text-start text-sm text-black">
            註2：客戶已完成室內設計，因故需要更換設計師做裝潢施工，您填寫的裝潢施工報價金額，需要符合將來的實際費用，如果設計包含廚房和空調，報價金額也需要符合。
          </p>
        </div>

        <button
          type="button"
          class="button-basic mt-2 w-full bg-gray-300 ring-1 ring-inset ring-gray-300 hover:bg-gray-400"
          @click="updateQuote()"
        >
          確定報價
        </button>
      </div>
    </div>
  </DefaultModal>
  <AppUsePhoneModal :user-type="UserTypeEnum.Designer" ref="appUsePhoneModalRef" />

  <DefaultModal
    title="裝潢報價"
    :click-outside-close="false"
    :show-close-button="true"
    ref="constructionFeeModalRef"
    @close-modal="closeConstructionFeeModal()"
  >
    <div class="mx-auto flex flex-col gap-2 p-4">
      <div class="mb-4 flex items-center">
        <label class="font-bold text-black">裝潢施工報價$</label>
        <input
          v-model="tempConstructionAmount"
          type="text"
          inputmode="numeric"
          class="input-basic ml-2 w-16 rounded-md text-center"
          @change="cleanTempConstructionAmount()"
        />
        <span class="ml-2">萬</span>
      </div>
      <div class="mb-4 flex items-center">
        <p class="mr-2 font-bold text-black">裝潢報價清單</p>
        <button
          class="flex flex-col items-center rounded-md bg-gray-300 px-4 py-2 text-black"
          @click="triggerFileInput()"
        >
          <img src="/vectors/general/upload.svg" alt="" class="mr-2 h-6 w-6" />
          上傳報價清單
        </button>
        <input type="file" ref="documentUrlInput" required @change="(event) => imageUpload(event)" class="hidden" />
      </div>
      <div class="mb-4 flex items-start">
        <p class="mr-2 text-black">報價清單</p>
        <div class="flex flex-col gap-2">
          <div
            v-for="(file, index) in [...orderDataDetail.contract.constructionAmountDocs].reverse()"
            :key="index"
            class="flex items-center gap-2"
          >
            <div class="cursor-pointer border-2 border-black px-4 py-2" @click="handleDownloadFile(file.documentUrl)">
              <p>
                第{{ orderDataDetail.contract.constructionAmountDocs.length - index }}次
                {{ moneyAddCommas(file.amount) }}
                上傳日 {{ formatFullDate(file.createTime) }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="text-start text-red-600">
        <p>
          註1：如委託人選擇相同的設計師進行裝潢施工，委託人將獲得同室內設計費用的裝潢施工減免，即從設計師的裝潢施工的費用扣除。
        </p>
        <p class="mt-2">
          註2：施工報價的最高金額未經委託人同意不得超過{{
            orderDataDetail.contract.constructionEstimateUpper
          }}元，否則將以設計費用的50%作為補償給委託人，若事先徵求委託人同意即可免除補償費。
        </p>
        <p class="mt-2">註3：設計師提供10%的室內設計費用作為平台使用費，此費用包含平台開立發票給委託人費用。</p>
      </div>
    </div>
  </DefaultModal>

  <AndroidNotSupportSharedWorkerModal :is-designer="true" ref="sharedWorkerNotSupportModalRef" />
</template>
