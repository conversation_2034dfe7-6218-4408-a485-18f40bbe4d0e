<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { computed, ref } from 'vue';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import { CustomerImageVideoItem, CustomerMediaUrlItem } from '@/model/response/customerMeasureOrderResponse.ts';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const modalTitle = ref<string>('');
const imageVideoItem = ref<CustomerImageVideoItem>({
  name: '',
  type: 0,
  media: [] as CustomerMediaUrlItem[],
  updateTime: ''
});

const openMediaModal = (imageVideoData: CustomerImageVideoItem) => {
  modalTitle.value = imageVideoData.name;
  imageVideoItem.value = imageVideoData;
  modalRef.value?.openModal();
};

const closeMediaModal = () => {
  modalRef.value?.closeModal();
};

const imageVideoInfo = computed(() => {
  return {
    name: imageVideoItem.value.name,
    type: imageVideoItem.value.type,
    media: imageVideoItem.value.media.map((media) => {
      return {
        url: media.url,
        description: media.description,
        updateTime: formatFullDateTimeWithDay(media.updateTime)
      };
    }),
    updateTime: formatFullDateTimeWithDay(imageVideoItem.value.updateTime)
  };
});

defineExpose({ openMediaModal });
</script>

<template>
  <DefaultModal :title="modalTitle" :show-close-button="true" :click-outside-close="true" ref="modalRef"
                @closeModal="closeMediaModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true">
          <swiper-slide v-for="(media,index) in [...imageVideoInfo.media].reverse()" :key="index" class="bg-center bg-auto">
            <p class="mb-2">{{ media.updateTime }}</p>
            <img v-if="imageVideoInfo.type === MediaTypeEnum.Image" :src="media.url" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <video v-else :src="media.url" muted controls
                   class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            </video>
            <p class="mt-2">{{ media.description }}</p>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>
</template>
