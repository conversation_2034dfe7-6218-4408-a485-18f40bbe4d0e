<script setup lang="ts">
import { useRouter } from 'vue-router';
import { toastInfo } from '@/utils/toastification.ts';
import { useDesignerInfoStore, useDesignerRedirectStore } from '@/stores/designerGlobal.ts';

const router = useRouter();
const redirectLink = useDesignerRedirectStore();
const designerInfoStore = useDesignerInfoStore();

const gotoDesignerAppDownload = () => {
  router.push({ name: 'designerappdownload' });
};

const gotoDesignerProfile = () => {
  if (!designerInfoStore.loginState) {
    redirectLink.setRedirectRoute('designerprofile');
    toastInfo('請先註冊或登入');
    router.push({ name: 'designerregister' });
  } else {
    router.push({ name: 'designerprofile' });
  }
};

const gotoDesignerPortfolio = () => {
  if (!designerInfoStore.loginState) {
    redirectLink.setRedirectRoute('designerportfolio');
    toastInfo('請先註冊或登入');
    router.push({ name: 'designerregister' });
  } else {
    router.push({ name: 'designerportfolio', params: { designerId: designerInfoStore.userId } });
  }
};

const gotoHome = () => {
  router.push({ name: 'designerhome' });
};

</script>

<template>
  <div class="py-8 w-full">
    <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
      <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 max-md:w-full"
           @click="gotoHome">
        <div
          class="flex flex-col max-md:w-full items-center bg-white text-black rounded-xl shadow-md shadow-gray-400 cursor-pointer hover-zoom">
          <img src="/vectors/designerStep/register.svg" alt="Step1" class="w-56 h-60 md:w-40 md:h-40 mx-4 my-8">
          <div class="cus-hezStep-color w-full text-center max-md:space-y-3 rounded-b-xl text-gray-600 md:py-4 py-8">
            <p class="font-bold text-xl md:text-lg">註冊帳號</p>
          </div>
        </div>
      </div>
      <svg class="h-12 w-12 md:h-16 md:w-16 max-md:rotate-90" fill="none" stroke="currentColor"
           viewBox="0 0 24 24"
           xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 max-md:w-full"
           @click="gotoDesignerProfile">
        <div
          class="flex flex-col max-md:w-full items-center bg-white text-black rounded-xl shadow-md shadow-gray-400 cursor-pointer hover-zoom">
          <img src="/vectors/designerStep/verify_data.svg" alt="Step2" class="w-56 h-60 md:w-40 md:h-40 mx-4 my-8">
          <div class="cus-hezStep-color w-full text-center max-md:space-y-3 rounded-b-xl text-gray-600 md:py-4 py-8">
            <p class="font-bold text-xl md:text-lg">驗證資料</p>
          </div>
        </div>
      </div>
      <svg class="h-12 w-12 md:h-16 md:w-16 max-md:rotate-90" fill="none" stroke="currentColor"
           viewBox="0 0 24 24"
           xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 max-md:w-full"
           @click="gotoDesignerPortfolio">
        <div
          class="flex flex-col max-md:w-full items-center bg-white text-black rounded-xl shadow-md shadow-gray-400 cursor-pointer hover-zoom">
          <img src="/vectors/designerStep/upload_portfolio.svg" alt="Step3" class="w-56 h-60 md:w-40 md:h-40 mx-4 my-8">
          <div class="cus-hezStep-color w-full text-center max-md:space-y-3 rounded-b-xl text-gray-600 md:py-4 py-8">
            <p class="font-bold text-xl md:text-lg">刊登作品</p>
          </div>
        </div>
      </div>
      <svg class="h-12 w-12 md:h-16 md:w-16 max-md:rotate-90" fill="none" stroke="currentColor"
           viewBox="0 0 24 24"
           xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 max-md:w-full"
           @click="gotoDesignerAppDownload">
        <div
          class="flex flex-col max-md:w-full items-center bg-white text-black rounded-xl shadow-md shadow-gray-400 cursor-pointer hover-zoom">
          <img src="/vectors/designerStep/get_order.svg" alt="Step4" class="w-56 h-60 md:w-40 md:h-40 mx-4 my-8">
          <div class="cus-hezStep-color w-full text-center max-md:space-y-3 rounded-b-xl text-gray-600 md:py-4 py-8">
            <p class="font-bold text-xl md:text-lg">即時接單</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
