<script setup lang="ts">
import { TextKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { getStyleConfig } from '@/model/general/textContentStyleConfig.ts';
import { computed, ref } from 'vue';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';
import TextContentUpdateModal from '@/components/Order/OrderDetail/UpdateModal/TextContentUpdateModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';

const textContent = defineModel<TextKeyContent>({ required: true }); //雙向綁定
const props = defineProps<{
  //單向傳入
  orderId: string;
  disabled: boolean;
  title: string;
  subTitle?: string;
  contentName: string;
  canNotDeleteCount: number; //從前往後數 有多少欄位不可刪除
  canAddUnit: boolean;
}>();
const updateModalRef = ref<InstanceType<typeof TextContentUpdateModal> | null>(null);

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const contentData = computed(() => {
  return textContent.value.content.map((item) => {
    const JsonStringContent = JSONStringToObject(item.text);
    return {
      updateTime: item.updateTime,
      name: item.name,
      key: item.key,
      text: {
        content: {
          hint: JsonStringContent.content.hint,
          text: JsonStringContent.content.text,
          ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
        },
        mode: JsonStringContent.mode
      }
    };
  });
});
</script>

<template>
  <div v-if="!(textContent.content.length === 0)">
    <div class="cus-border my-1 flex flex-col gap-4">
      <div class="flex items-center justify-center space-x-2 text-2xl">
        <p class="text-color-primary text-center font-bold">{{ title }}</p>
        <p class="text-color-primary text-center text-xl font-bold">{{ subTitle }}</p>
      </div>
      <HezDivider />
      <div class="flex justify-start">
        <div class="flex flex-col gap-y-2">
          <div
            v-for="infoData in contentData"
            :key="infoData.key"
            class="text-color-primary flex justify-start text-lg"
          >
            <div
              v-if="infoData.name !== '噪音評估' && infoData.name !== '濕度評估'"
              class="flex items-center gap-x-0.5 md:gap-x-2"
            >
              <p
                class="break-all p-2 font-bold"
                v-html="getStyleConfig('name', infoData.name, infoData.name, true)"
              ></p>
              <div v-if="infoData.text.content.text === ''" class="flex">
                <p class="break-all text-black">未上傳</p>
              </div>
              <div v-else class="flex items-center">
                <p
                  class="break-all font-bold text-black"
                  v-html="getStyleConfig('text', infoData.name, infoData.text.content.text, true)"
                ></p>
                <p
                  v-if="infoData.text.content.unit"
                  class="font-bold text-black"
                  v-html="getStyleConfig('unit', infoData.name, infoData.text.content.unit, true)"
                ></p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button class="cus-btn button-padding w-full text-xl" v-if="!disabled" @click="openUpdateModal()">
        資料上傳/修改
      </button>
    </div>
    <TextContentUpdateModal
      v-model="textContent"
      :title="props.title"
      :orderId="props.orderId"
      :contentName="props.contentName"
      :canNotDeleteCount="props.canNotDeleteCount"
      :canAddUnit="props.canAddUnit"
      ref="updateModalRef"
    />
  </div>
</template>
