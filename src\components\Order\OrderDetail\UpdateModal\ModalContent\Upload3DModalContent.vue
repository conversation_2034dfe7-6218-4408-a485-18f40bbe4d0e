<script setup lang="ts">
import { computed, nextTick, reactive, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import {
  ImageVideoKeyContent,
  ImageVideoKeyItem,
  MediaUrlKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { toastError, toastInfo, toastSuccess, toastWarning } from '@/utils/toastification.ts';
import { DesignOrderDetailService } from '@/api/designerOrder.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import { DesignOrderItemEnum } from '@/model/enum/orderUpdateType.ts';
import { EllipsisVerticalIcon, TrashIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import HezDivider from '@/components/General/HezDivider.vue';
import { UploadImageVideoType } from '@/utils/hezParameters.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { DesignOrderStatusEnum, DesignOrderSubStatusEnum } from '@/model/enum/orderStatus.ts';
import DesignerRemitModal from '@/components/Modal/DesignerRemitModal.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const design3D = defineModel<ImageVideoKeyContent>('design3D', { required: true });
const subStatus = defineModel<DesignOrderSubStatusEnum>('subStatus', { required: true });
const orderStatus = defineModel<DesignOrderStatusEnum>('orderStatus', { required: true });

const emits = defineEmits(['refreshData', 'changeApiLoading']);
const props = defineProps<{ orderId: string; }>();
const addItemModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const editItemTitleModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const mediaDetailEditModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const remitModal = ref<InstanceType<typeof DesignerRemitModal> | null>(null);
const addItemTitleAndType = ref({ title: '', type: MediaTypeEnum.Image });
const editItemTitleData = ref<{ key: string, title: string }>({ key: '', title: '' });
const mediaDetailEditData = ref<ImageVideoKeyItem>({
  key: '',
  name: '',
  type: MediaTypeEnum.Image,
  media: [] as MediaUrlKeyItem[],
  updateTime: ''
});
const isAPILoading = ref<boolean>(false);
const reFreshSwiper = ref<boolean>(false);
const fileInputElement = reactive<{
  //因為我用了v-for產生多個 檔案input 所以需要搞一個用media.key去便是他們各自獨立性的 reactive，不然所有input ref都是同一個會衝突
  [key: string]: HTMLInputElement | null;
}>({});

const clearMediaDetailEditData = () => {
  mediaDetailEditData.value = {
    key: '',
    name: '',
    type: MediaTypeEnum.Image,
    media: [] as MediaUrlKeyItem[],
    updateTime: ''
  };
};

const openAddItemModal = () => {
  addItemModalRef.value?.openModal();
};
const closeAddItemModal = () => {
  addItemTitleAndType.value = {
    title: '',
    type: MediaTypeEnum.Image
  };
  addItemModalRef.value?.closeModal();
};
const openEditTitleModal = (key: string, title: string) => {
  editItemTitleData.value = { key, title };
  editItemTitleModalRef.value?.openModal();
};
const closeEditTitleModal = () => {
  editItemTitleModalRef.value?.closeModal();
  editItemTitleData.value = { key: '', title: '' };
};
const openMediaDetailEditModal = (mediaItem: ImageVideoKeyItem) => {
  if (mediaItem.media.length === 0) {
    toastInfo('請先上傳圖片或影片');
    return;
  }
  mediaDetailEditData.value = mediaItem;
  mediaDetailEditModalRef.value?.openModal();
};
const closeMediaDetailEditModal = () => {
  mediaDetailEditModalRef.value?.closeModal();
  setTimeout(() => {
    clearMediaDetailEditData();
  }, 300);
};

const addItem = async () => {
  if (isAPILoading.value === false) {
    if (addItemTitleAndType.value.title === '') {
      toastInfo('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.add3DItem({
      orderId: props.orderId,
      items: [{
        name: addItemTitleAndType.value.title,
        type: addItemTitleAndType.value.type
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      closeAddItemModal();
    }
    isAPILoading.value = false;
  }
};

const editItemTitle = async () => {
  if (isAPILoading.value === false) {
    if (editItemTitleData.value.title === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update3D({
      orderId: props.orderId,
      builders: [{
        key: editItemTitleData.value.key,
        setName: editItemTitleData.value.title,
        pushMedia: [],
        pullMedia: [],
        setMediaDescription: []
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      closeEditTitleModal();
    }
    isAPILoading.value = false;
  }
};

const deleteItem = async (itemKey: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.deleteItem({
      orderId: props.orderId,
      keys: [itemKey]
    }, DesignOrderItemEnum.Design3D);
    switch (result.status) {
      case APIStatusCodeEnum.Success:
        emits('refreshData'); // 叫父元件刷新資料
        break;
      case APIStatusCodeEnum.OrderAcceptanceInProgressCannotDelete:
        toastInfo('客戶驗收中，無法刪除欄位');
        break;
      case APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete:
        toastInfo('客戶驗收完成，無法刪除欄位');
        break;
    }
    isAPILoading.value = false;
  }
};

const finish3D = async () => {
  if (orderStatus.value === DesignOrderStatusEnum.Contracting) {
    remitModal.value?.openModal();
    return;
  }

  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.finish3D({ orderId: props.orderId });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
    }
    isAPILoading.value = false;
  }
};

const deleteMediaDetail = async (mediaItemKey: string, mediaDetailKey: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update3D({
      orderId: props.orderId,
      builders: [{
        key: mediaItemKey,
        setName: '',
        pushMedia: [],
        pullMedia: [mediaDetailKey],
        setMediaDescription: []
      }]
    });

    let resultMedia, resultMediaLength;
    switch (result.status) {
      case APIStatusCodeEnum.Success:
        emits('refreshData'); // 叫父元件刷新資料
        resultMedia = result.content.design3D.content.find(item => item.key === mediaItemKey);
        console.log('resultMedia', resultMedia);
        resultMediaLength = resultMedia ? resultMedia.media.length : 0;
        console.log('resultMediaLenth', resultMediaLength);
        //如果刪掉項目最後一張照片就直接退出Modal
        if (resultMediaLength === 0) {
          closeMediaDetailEditModal();
          setTimeout(() => {
            clearMediaDetailEditData();
          }, 300);
        } else {
          reFreshSwiper.value = true;
          mediaDetailEditData.value = result.content.design3D.content.find(item => item.key === mediaItemKey) as ImageVideoKeyItem;
          await nextTick();
          reFreshSwiper.value = false;
        }
        break;
      case APIStatusCodeEnum.OrderAcceptanceInProgressCannotDelete:
        toastInfo('客戶驗收中，無法刪除檔案');
        break;
      case APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete:
        toastInfo('客戶驗收完成，無法刪除檔案');
        break;
    }
    isAPILoading.value = false;
  }
};

const editMediaDetailDescription = async (mediaDetailKey: string, description: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await DesignOrderDetailService.update3D({
      orderId: props.orderId,
      builders: [{
        key: mediaDetailEditData.value.key,
        setName: '',
        pushMedia: [],
        pullMedia: [],
        setMediaDescription: [{
          key: mediaDetailKey,
          description: description
        }]
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      emits('refreshData'); // 叫父元件刷新資料
      mediaDetailEditData.value = result.content.design3D.content.find(item => item.key === mediaDetailEditData.value.key) as ImageVideoKeyItem;
    }
    isAPILoading.value = false;
  }
};

const triggerFileInput = (key: string) => {
  fileInputElement[key]?.click(); // 因為實現點擊div觸發上傳圖片，觸發 input 的 fileUpload
};

const fileUpload = async (event: Event, mediaItem: ImageVideoKeyItem) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    emits('changeApiLoading', true);
    let uploadType = mediaItem.type === MediaTypeEnum.Image ? UploadFileEnum.Picture : UploadFileEnum.Video;
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, uploadType)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    const pushMedia = S3Urls.map(url => {
      return {
        url: url,
        description: ''
      };
    });
    const result = await DesignOrderDetailService.update3D({
      orderId: props.orderId,
      builders: [{
        key: mediaItem.key,
        setName: '',
        pushMedia: pushMedia,
        pullMedia: [],
        setMediaDescription: []
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      toastSuccess('檔案上傳成功');
      emits('refreshData'); // 叫父元件刷新資料
    }
    emits('changeApiLoading', false);
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const isDesign3DValid = computed(() => {
  if (design3D.value.content.length === 0) return false;

  if (!design3D.value || !design3D.value.content) return false;

  return design3D.value.content.every(item => item.media.length > 0);
});

const mediaDetailInfo = computed(() => {
  // console.log('mediaDetailEditData', mediaDetailEditData.value);
  return {
    key: mediaDetailEditData.value.key,
    name: mediaDetailEditData.value.name,
    type: mediaDetailEditData.value.type,
    media: mediaDetailEditData.value.media.map((media) => {
      return {
        key: media.key,
        url: media.url,
        description: media.description,
        updateTime: formatFullDateTimeWithDay(media.updateTime)
      };
    }),
    updateTime: formatFullDateTimeWithDay(mediaDetailEditData.value.updateTime)
  };
});
</script>

<template>
  <div class="flex-col m-3">
    <div v-for="mediaItem in design3D.content" :key="mediaItem.key" class="flex flex-col">
      <div class="flex justify-between px-5 mb-2">
        <p class="text-xl">{{ mediaItem.name }}</p>
        <Menu as="div" class="relative flex-none">
          <MenuButton class="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
            <span class="sr-only">Open options</span>
            <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
          </MenuButton>
          <transition enter-active-class="transition ease-out duration-100"
                      enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                      leave-active-class="transition ease-in duration-75"
                      leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
            <MenuItems
              class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
              <MenuItem v-slot="{ active }">
                <a
                  :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                  @click="openEditTitleModal(mediaItem.key, mediaItem.name)"
                >編輯項目名稱</a>
              </MenuItem>
              <MenuItem v-slot="{ active }">
                <a
                  :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                  @click="deleteItem(mediaItem.key)"
                >移除項目</a>
              </MenuItem>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <div class="flex justify-evenly">
        <div @click="mediaItem.media.length >0?openMediaDetailEditModal(mediaItem):triggerFileInput(mediaItem.key)"
             class="flex flex-col w-2/3 gap-y-0.5 p-8 bg-gray-200 justify-center items-center cursor-pointer max-md:p-2">
          <div v-if="mediaItem.type === MediaTypeEnum.Video" class="flex flex-col justify-center items-center">
            <img v-if="mediaItem.media.length >0" src="/vectors/order/uploadFile/video_uploaded.svg" alt=""
                 class="w-14 h-14" />
            <img v-else src="/vectors/order/uploadFile/video_xx.svg" alt="" class="w-14 h-14" />
            <p>影片預覽({{ mediaItem.media.length }})</p>
          </div>
          <div v-else class="flex flex-col justify-center items-center">
            <img v-if="mediaItem.media.length >0" src="/vectors/order/uploadFile/photo_uploaded.svg" alt=""
                 class="w-14 h-14" />
            <img v-else src="/vectors/order/uploadFile/photo_xx.svg" alt="" class="w-14 h-14" />
            <p>圖片預覽({{ mediaItem.media.length }})</p>
          </div>
        </div>
        <div @click="triggerFileInput(mediaItem.key)"
             class="flex flex-col w-1/4 gap-y-0.5 p-8 bg-color-secondary justify-center items-center cursor-pointer max-md:p-2">
          <img src="/vectors/general/upload.svg" alt="addImage" class="w-7 h-7" />
          <p v-if="mediaItem.type === MediaTypeEnum.Image">上傳圖片</p>
          <p v-else-if="mediaItem.type === MediaTypeEnum.Video">上傳影片</p>
        </div>
        <input type="file" :ref="el => (fileInputElement[mediaItem.key] = el as HTMLInputElement)"
               :accept="UploadImageVideoType"
               multiple required @change="event => fileUpload(event,mediaItem)" class="hidden">
      </div>
      <hez-divider />
    </div>
    <div
      class="flex max-md:flex-col w-full gap-2">
      <button type="button"
              class="button-basic w-full bg-color-selected hover:opacity-80 "
              @click="openAddItemModal()">新增項目
      </button>

      <template v-if="subStatus <= DesignOrderSubStatusEnum.Design2DAgree">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish3D()">請求撥款
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design3DUploaded">
        <button v-if="isDesign3DValid"
                type="button" class="button-basic w-full bg-color-selected hover:opacity-80 "
                @click="finish3D()">請求撥款
        </button>
        <button v-else type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish3D()">請求撥款
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design3DRequest">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish3D()">客戶驗收中
        </button>
      </template>
      <template v-if="subStatus === DesignOrderSubStatusEnum.Design3DReject">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish3D()">客戶要求修正
        </button>
      </template>
      <template v-if="subStatus >= DesignOrderSubStatusEnum.Design3DAgree">
        <button type="button" :disabled="true"
                class="button-basic w-full bg-gray-200"
                @click="finish3D()">客戶已同意撥款
        </button>
      </template>

    </div>
  </div>
  <!--新增項目-對話框-->
  <DefaultModal title="新增項目名稱" :showCloseButton="false" :click-outside-close="false" ref="addItemModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemTitleAndType.title"
               placeholder="請輸入項目名稱" />
      </div>
      <div class="flex justify-center mt-1 gap-x-2">
        <input type="radio" id="image" name="mediaType" :value="MediaTypeEnum.Image" v-model="addItemTitleAndType.type">
        <label for="image" class="mr-2">圖片</label>
        <input type="radio" id="video" name="mediaType" :value="MediaTypeEnum.Video" v-model="addItemTitleAndType.type">
        <label for="video">影片</label>
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="addItem()">確認
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeAddItemModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
  <!--編輯項目-對話框-->
  <DefaultModal title="編輯項目名稱" :showCloseButton="false" :click-outside-close="false" ref="editItemTitleModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="editItemTitleData.title"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="editItemTitle()">修改
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeEditTitleModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
  <!--預覽-對話框-->
  <DefaultModal title="預覽" :show-close-button="true" :click-outside-close="false" ref="mediaDetailEditModalRef"
                @closeModal="closeMediaDetailEditModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true" v-if="!reFreshSwiper">
          <swiper-slide v-for="mediaDetail in [...mediaDetailInfo.media].reverse()" :key="mediaDetail.key"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ mediaDetail.updateTime }}</p>
            <img v-if="mediaDetailEditData.type === MediaTypeEnum.Image" :src="mediaDetail.url" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <video v-else :src="mediaDetail.url" muted controls
                   class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            </video>
            <div class="flex justify-between items-center">
              <input type="text"
                     class="relative block border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
                     v-model="mediaDetail.description"
                     @change="editMediaDetailDescription(mediaDetail.key, mediaDetail.description)"
                     placeholder="請輸入註解文字" />
              <div class="flex flex-col justify-center cursor-pointer hover-zoom"
                   @click="deleteMediaDetail(mediaDetailInfo.key,mediaDetail.key)">
                <div class="flex justify-center">
                  <TrashIcon class="w-7 h-7" />
                </div>
                <p v-if="mediaDetailInfo.type === MediaTypeEnum.Video">刪除此影片</p>
                <p v-else>刪除此圖片</p>
              </div>
            </div>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>

  <DesignerRemitModal title="客戶尚未匯款" ref="remitModal" />

</template>
