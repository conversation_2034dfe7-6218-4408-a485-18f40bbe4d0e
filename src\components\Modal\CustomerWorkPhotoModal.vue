<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { computed, ref } from 'vue';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { CustomerMediaUrlItem } from '@/model/response/customerMeasureOrderResponse.ts';
import { CustomerConstructionProcess } from '@/model/response/customerConstructionOrderResponse.ts';
import { ConstructionProcessStatusEnum } from '@/model/enum/orderStatus.ts';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const workProcessData = ref<CustomerConstructionProcess>({
  updateTime: '',
  name: '',
  media: [] as CustomerMediaUrlItem[],
  key: '',
  updateCount: 0,
  status: ConstructionProcessStatusEnum.PendingDisbursement,
  amount: 0,
  isAppend: false,
  amountDocUrl: ''
});

const openModal = (workData: CustomerConstructionProcess) => {
  workProcessData.value = workData;
  modalRef.value?.openModal();
};

const closeMediaModal = () => {
  modalRef.value?.closeModal();
};


const workDataInfo = computed(() => {
  return {
    updateTime: formatFullDateTimeWithDay(workProcessData.value.updateTime),
    name: workProcessData.value.name,
    media: workProcessData.value.media.map((media) => {
      return {
        url: media.url,
        description: media.description,
        updateTime: formatFullDateTimeWithDay(media.updateTime)
      };
    }),
    key: workProcessData.value.key,
    updateCount: workProcessData.value.updateCount,
    status: workProcessData.value.status,
    amount: workProcessData.value.amount,
    isAppend: workProcessData.value.isAppend,
    amountDocUrl: workProcessData.value.amountDocUrl
  };
});

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="workDataInfo.name" :show-close-button="true" :click-outside-close="true" ref="modalRef"
                @closeModal="closeMediaModal">
    <div class="flex flex-col m-3">
      <div v-if="workDataInfo.media.length === 0">
        <p>目前沒有照片可顯示</p>
      </div>
      <div v-else>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true">
          <swiper-slide v-for="(media,index) in [...workDataInfo.media].reverse()" :key="index"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ media.updateTime }}</p>
            <img :src="media.url" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <p class="mt-2">{{ media.description }}</p>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>
</template>
