<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';
import { SecurityService } from '@/api/security.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { toastError, toastInfo } from '@/utils/toastification.ts';
import { piniaPasswordClean, piniaStoreClean } from '@/utils/piniaStoreClean.ts';
import { useRoute, useRouter } from 'vue-router';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { decryptAES } from '@/utils/crypto.ts';
import type { DeleteAccountResponse } from '@/model/response/securityResponse.ts';

const askModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const router = useRouter();
const route = useRoute();
const phoneNumber = ref('');
const isDesigner = route.path.includes('designer');

const userInfoStore = isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();

const deleteAccount = async () => {
  if (!userInfoStore.loginState) return;
  const userLoginPhone = decryptAES(userInfoStore.loginCipher.phone);
  if (userLoginPhone !== phoneNumber.value) {
    toastError('手機號碼不正確');
    return;
  }

  let res: DeleteAccountResponse;
  if (isDesigner) {
    res = await SecurityService.deleteAccountByDesigner({});
  } else {
    res = await SecurityService.deleteAccountByCustomer({});
  }

  if (res.status === APIStatusCodeEnum.Success) {
    // 清空Pinia資料 導向回對應的首頁
    piniaStoreClean(isDesigner);
    piniaPasswordClean(isDesigner);
    if (isDesigner) {
      await router.push('/designer/home');
    } else {
      await router.push('/');
    }
    toastInfo('帳號已刪除');
  } else if (res.status === APIStatusCodeEnum.OrderInProgressCannotDelete) {
    toastError('尚有訂單進行中，無法刪除帳號');
  }
};

const openModal = () => {
  askModalRef.value?.openModal();
};
</script>

<template>
  <a class="mt-8 text-2xl font-bold">家易（HomeEasy）服務條款</a>
  <div class="flex-center cus-border my-8 w-full flex-col items-start">
    <div class="flex flex-col items-center justify-center gap-2 md:items-start">
      <p>
        歡迎使用
        家易（HomeEasy）設計師媒合平台（以下簡稱「本平台」）。本平台提供設計委託者（以下稱「委託者」）與設計提供者（以下稱「設計師」）進行設計專案媒合、合作、交易與交付的服務。為保障您的權益，請詳閱以下條款，您一旦註冊或使用本平台，即表示您已閱讀、理解並同意受本條款之約束。
      </p>
      <br />

      <h2>一、會員註冊與帳號管理</h2>

      <p>1. 使用本平台前，您需完成註冊，並保證所提供之資訊為真實、完整與最新。</p>
      <p>2. 會員分為「委託者」與「設計師」，可依實際身份註冊與使用。</p>
      <p>3. 您應妥善保管帳號與密碼，任何以該帳號進行之操作，將視為您本人或您的公司行為。</p>
      <p>4. 若帳號有遭他人盜用之虞，請立即通知平台，平台有權暫停帳號以避免損害擴大。</p>
      <br />

      <h2>二、服務內容與媒合流程</h2>

      <p>1. 本平台提供設計需求發佈、提案投稿、報價、雙方溝通、進度追蹤、付款與交付機制。</p>
      <p>2. 委託者可刊登設計專案，設計師可就感興趣案件提交提案與報價。</p>
      <p>3. 雙方確認合作條件後，即進入平台管理之合作流程。平台建議所有關鍵溝通記錄應保留於平台內完成。</p>
      <br />

      <h2>三、付款機制與費用</h2>

      <p>1. 委託者應依平台指示完成預付款程序，款項將由平台代管，待專案成果驗收後支付予設計師。</p>
      <p>2. 設計師於每筆成功媒合案件須支付服務費，收費比例以本平台公告為準。</p>
      <p>3. 如專案中止或發生爭議，平台保留依據事實與協議處理退款、扣款或中止合作之最終決定權。</p>
      <br />

      <h2>四、智慧財產權與使用授權</h2>

      <p>1. 設計成果之智慧財產權歸屬，除雙方另有書面約定外，原則上在委託者完成付款後移轉予委託者。</p>
      <p>2. 設計師應保證其設計為原創，並不侵害第三方之智慧財產權，否則應自負一切法律責任。</p>
      <p>3. 委託者於未完成付款前，不得將設計成果用於任何商業或公開用途。</p>
      <br />

      <h2>五、禁止行為</h2>

      <p>會員不得從事以下行為，違者平台有權限制帳號使用、移除內容或終止服務，並保留法律追訴權：</p>
      <p>提供不實資料或冒用他人身分進行註冊；</p>
      <p>刊登虛構案件或以非合作為目的之溝通行為；</p>
      <p>抄襲、盜用設計作品，或未經授權擅自使用平台內容；</p>
      <p>散佈病毒、垃圾訊息、惡意程式或破壞平台系統之行為；</p>
      <p>干擾、攻擊其他用戶或平台正常運作。</p>
      <br />

      <h2>六、平台角色與責任限制</h2>

      <p>1. 本平台僅作為設計合作媒合與交易平台，無涉入實際設計合作內容，對於雙方履約結果不負擔保責任。</p>
      <p>2. 如會員間發生爭議，平台將依據溝通紀錄、驗收記錄及付款狀態協助調解，但不負有法律裁決義務。</p>
      <p>3. 因使用平台所生之間接、附帶、特殊或懲罰性損害賠償，平台不承擔責任。</p>
      <br />

      <h2>七、條款變更與服務終止</h2>

      <p>1. 家易（HomeEasy）保留隨時修改本服務條款之權利，更新後條款將公告於平台網站或APP中。</p>
      <p>2. 若您在條款變更後繼續使用本平台，則視為您已同意變更內容。</p>
      <p>3. 如會員違反本條款，平台得視情節停止服務、刪除帳號，並視情況移送司法機關處理。</p>
      <br />

      <h2>八、隱私權與資料保護</h2>

      <p>1. 本平台重視您的個人資料保護，相關蒐集、處理及使用，請詳見《隱私權政策》。</p>
      <p>2. 所有會員資訊將僅供本平台提供服務與客戶管理之必要用途使用，並依法保密。</p>
      <br />

      <h2>九、準據法與爭議解決</h2>

      <p>本條款依中華民國法律為準據法，因使用本平台所生之爭議，雙方同意以臺灣台北地方法院為第一審管轄法院。</p>
    </div>

    <div class="flex w-full justify-center">
      <button
        type="button"
        v-if="userInfoStore.loginState"
        class="mt-4 rounded-lg bg-gray-200 px-4 py-2 font-medium hover:bg-gray-300"
        @click="openModal()"
      >
        刪除帳號
      </button>
    </div>
  </div>

  <DefaultModal
    title="刪除帳號"
    :show-close-button="true"
    :click-outside-close="true"
    @closeModal="askModalRef?.closeModal()"
    modal-width="max-w-md"
    ref="askModalRef"
  >
    <div class="m-3 flex-col text-start text-black">
      <p>執行此動作之後</p>
      <p>家易會移除包含身分驗證在內的所有個人資訊</p>
      <p>若是您非常確定要刪除此帳號</p>
      <p>請輸入您註冊時使用的手機號碼</p>
      <input type="text" v-model="phoneNumber" class="input-basic mt-1 w-full" placeholder="請輸入手機號碼" />
      <div class="text-color-secondary mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3">
        <button
          type="button"
          class="button-basic w-full bg-red-300 hover:bg-red-400 md:col-start-2"
          @click="deleteAccount()"
        >
          確定刪除帳號
        </button>
        <button
          type="button"
          class="button-basic mt-1 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1 md:mt-0"
          @click="askModalRef?.closeModal()"
        >
          取消刪除帳號
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
