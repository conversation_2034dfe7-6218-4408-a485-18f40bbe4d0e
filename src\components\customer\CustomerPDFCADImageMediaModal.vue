<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { computed, ref } from 'vue';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { CustomerImagePdfCadItem, CustomerMediaUrlItem } from '@/model/response/customerMeasureOrderResponse.ts';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const modalTitle = ref<string>('');
const imagePdfCadItem = ref<CustomerImagePdfCadItem>({
  name: '',
  images: [] as CustomerMediaUrlItem[],
  pdf: {
    updateTime: '',
    url: ''
  },
  cad: {
    updateTime: '',
    url: ''
  },
  updateTime: ''
});

const openMediaModal = (imagePdfCadData: CustomerImagePdfCadItem) => {
  modalTitle.value = imagePdfCadData.name;
  imagePdfCadItem.value = imagePdfCadData;
  modalRef.value?.openModal();
};

const closeMediaModal = () => {
  modalRef.value?.closeModal();
};

const imageVideoInfo = computed(() => {
  return {
    name: imagePdfCadItem.value.name,
    media: imagePdfCadItem.value.images.map((images) => {
      return {
        url: images.url,
        description: images.description,
        updateTime: formatFullDateTimeWithDay(images.updateTime)
      };
    }),
    updateTime: formatFullDateTimeWithDay(imagePdfCadItem.value.updateTime)
  };
});

defineExpose({ openMediaModal });
</script>

<template>
  <DefaultModal :title="modalTitle" :show-close-button="true" :click-outside-close="true" ref="modalRef"
                @closeModal="closeMediaModal">
    <div class="flex flex-col m-3">
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          :navigation="true">
          <swiper-slide v-for="(media,index) in [...imageVideoInfo.media].reverse()" :key="index"
                        class="bg-center bg-auto">
            <p class="mb-2">{{ media.updateTime }}</p>
            <img :src="media.url" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
            <p class="mt-2">{{ media.description }}</p>
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </DefaultModal>
</template>
