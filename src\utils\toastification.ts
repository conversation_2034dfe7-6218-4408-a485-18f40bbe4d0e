import { useToast, POSITION } from 'vue-toastification';

const toast = useToast();

export const toastInfo = (message: string) => {
  toast.info(message, toastOptions);
};
export const toastSuccess = (message: string) => {
  toast.success(message, toastOptions);
};
export const toastWarning = (message: string) => {
  toast.warning(message, toastOptions);
};
export const toastError = (message: string) => {
  toast.error(message, toastOptions);
};

const toastOptions = {
  position: POSITION.TOP_CENTER,
  timeout: 2000,
  closeOnClick: true,
  pauseOnFocusLoss: false,
  pauseOnHover: false,
  draggable: false,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false
};

// 參考 URL：https://vue-toastification.maronato.dev/
