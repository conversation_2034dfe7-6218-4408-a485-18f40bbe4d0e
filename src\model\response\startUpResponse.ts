import type { BaseResponse } from './baseResponse.ts';

export interface GetQuickInfoResponse extends BaseResponse {
  appMinimumVersion: {
    androidCustomerAppVersion: string,
    androidDesignerAppVersion: string,
    appleCustomerAppVersion: string,
    appleDesignerAppVersion: string
  },
  customerFeatures: {
    customerStep1: boolean,
    customerStep2: boolean,
    customerStep3: boolean,
    customerStep4: boolean
  },
  designerFeatures: {
    designerStep1: boolean,
    designerStep2: boolean,
    designerStep3: boolean,
    designerStep4: boolean
  }
}