const selectWithTextOptions = {
  房屋類型: ['無電梯公寓', '電梯大樓', '透天厝', '別墅', '店面', '其他(請填寫)'],
  整修狀況: ['全屋翻新', '部分翻新', '格局固定', '其他(請填寫)'],
  排水管線: ['管線重做', '部分施作', '管線不變', '其他(請填寫)'],
  衛浴狀況: ['進口衛浴', '國產衛浴', '便宜衛浴', '完全不變', '其他(請填寫)'],
  衛浴間數: ['0', '1', '2', '3', '4', '5', '其他(請填寫)'],
  電力管線: ['全部重拉', '部分重拉', '燈具重拉', '完全不變', '其他(請填寫)'],
  隔間工程: ['磚牆隔間', '輕鋼水泥牆', '輕鋼隔音棉', '板材輕隔間', '部分施作', '完全不變', '其他(請填寫)'],
  窗戶窗簾: ['進口品牌', '國產高階', '國產初階', '完全不變', '其他(請填寫)'],
  大門門組: ['進口品牌', '國產門組', '完全不變', '其他(請填寫)'],
  屋內門片: ['進口品牌', '國產門片', '完全不變', '其他(請填寫)'],
  磁磚: ['進口瓷磚', '國產磁磚', '部分施作', '完全不變', '其他(請填寫)'],
  天花板: ['全部重做', '部分施作', '完全不變', '其他(請填寫)'],
  牆面油漆: ['全屋施作(高級打磨)', '全屋施作', '普通施作', '完全不變', '其他(請填寫)'],
  地板: ['進口品牌', '國產品牌', '便宜品牌', '完全不變', '其他(請填寫)'],
  廚房櫃體: ['進口品牌', '國產品牌', '便宜品牌', '完全不變', '其他(請填寫)'],
  客廳櫃體: ['全部重做', '部分施作', '完全不變', '其他(請填寫)'],
  空調管線: ['管線重做', '部分施作', '管線不變', '其他(請填寫)'],
  水質檢測: ['藍燈(極好)', '綠燈(好)', '黃燈(普通)', '紅燈(不好)'],
  空氣檢測: ['良好', '普通', '不良', '危害'],
  輻射評估: ['安全', '普通', '警戒']
};
/**
 * @description: 通過name取得所有selectWithText類型會用到的選項
 */
export function getSelectWithTextOptions(name: string): string[] {
  const options = selectWithTextOptions[name as keyof typeof selectWithTextOptions];
  return options || ['其他(請填寫)']; //什麼都沒抓到的例外狀況
}
