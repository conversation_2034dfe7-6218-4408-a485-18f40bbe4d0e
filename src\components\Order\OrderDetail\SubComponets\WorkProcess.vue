<script setup lang="ts">
import { computed, ref } from 'vue';
import { ProcessKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { ConstructionProcessStatusEnum } from '@/model/enum/orderStatus.ts';
import WorkProcessModal from '@/components/Order/OrderDetail/UpdateModal/WorkProcessModal.vue';

const workProcess = defineModel<ProcessKeyItem[]>('workProcess', { required: true });
const workDataAmount = defineModel<string[]>('workDataAmount', { required: true });
const emits = defineEmits(['refreshData']);
const updateWorkModalRef = ref<InstanceType<typeof WorkProcessModal> | null>(null);
const processIndex = ref<number>(0);

const props = defineProps<{
  orderId: string;
}>();

const workProcessClicked = (index: number) => {
  processIndex.value = index;
  updateWorkModalRef.value?.openModal();
};

const processData = computed(() => {
  return workProcess.value[processIndex.value];
});

const workAmount = computed(() => {
  return workDataAmount.value[processIndex.value];
});

const isAnyWorkChecking = computed(() => {
  return workProcess.value.some((process) => process.status === ConstructionProcessStatusEnum.PendingDisbursement);
});

</script>

<template>
  <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
       v-for="(process, index) in workProcess"
       :key="index" @click="workProcessClicked(index)">
    <div class="flex items-center">
      <div
        class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
        {{ index + 2 }}
      </div>
      <div class="flex flex-col gap-3">
        <div class="flex gap-3 ">
          <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
          <p class=" font-bold md:text-lg text-black">{{ process.name }}</p>
        </div>
        <div class="flex gap-3 ">
          <p class=" font-bold md:text-lg text-nowrap">工程款</p>
          <p class=" font-bold md:text-lg text-black"> {{ workDataAmount[index] }}</p>
        </div>
        <div class="flex gap-3">
          <p class=" font-bold md:text-lg text-nowrap">狀態</p>
          <p v-if="process.status === ConstructionProcessStatusEnum.NotRequested"
             class=" font-bold md:text-lg text-black">
            <span v-if="process.media.length === 0">此工程尚未完成</span>
            <span v-else class="text-blue-600">尚未請款</span>
          </p>
          <p v-else-if="process.status === ConstructionProcessStatusEnum.PendingDisbursement"
             class=" font-bold md:text-lg text-blue-600">驗收中</p>
          <p v-else-if="process.status === ConstructionProcessStatusEnum.Completed"
             class=" font-bold md:text-lg text-black">客戶已同意撥款</p>
          <p v-else-if="process.status === ConstructionProcessStatusEnum.Reject"
             class=" font-bold md:text-lg text-red-600">客戶要求修正，請上傳圖片</p>
          <p v-else-if="process.status === ConstructionProcessStatusEnum.InsufficientBalance">
            <span v-if="process.media.length !== 0 " class=" font-bold md:text-lg text-red-600">客戶匯款不足</span>
            <span v-else class=" font-bold md:text-lg text-black">此工程尚未完成</span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <WorkProcessModal :title="processData.name" :order-id="props.orderId"
                    v-model:work-process="processData"
                    v-model:work-amount="workAmount"
                    v-model:is-any-work-checking="isAnyWorkChecking"
                    @refresh-data="emits('refreshData')"
                    ref="updateWorkModalRef" />

</template>
