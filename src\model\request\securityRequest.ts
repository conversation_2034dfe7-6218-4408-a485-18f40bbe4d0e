import { DeviceTypeEnum } from '@/model/enum/deviceType.ts';

export interface GetGuestTokenRequest {
  deviceType: DeviceTypeEnum;
  deviceBrand: string;
  deviceModel: string;
  deviceVersion: string;
  deviceRegion: string;
  appVersion: string;
  fcmToken: string;
}

export interface MountEventRequest {
  deviceVersion: string;
  appVersion: string;
  fcmToken: string;
}

export interface SendVerifyCodeRequest {
  phone: string;
}

export interface CheckVerifyCodeRequest {
  verifyCode: string;
}

export interface RegisterAndLoginRequest {
  phoneVerifyId: string;
  password: string;
}

export interface UserLoginRequest {
  phone: string;
  password: string;
}

export interface UserLogoutRequest {
}//傳空物件就好，用 Token 判斷就好，除非未來有更動。

export interface DeleteAccountRequest {
}//傳空物件就好，用 Token 判斷就好，除非未來有更動。

export interface ResetPasswordRequest {
  password: string;
}
