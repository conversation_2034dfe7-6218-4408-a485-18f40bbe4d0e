<script setup lang="ts">
import { onMounted, ref, reactive, computed } from 'vue';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { TextKeyContent, TextKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { JSONStringToObject, ObjectToJSONString } from '@/utils/JsonStringFormat.ts';
import { TextModeEnum } from '@/model/enum/JsonTextModeEnum.ts';
import { toastInfo, toastWarning } from '@/utils/toastification.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { MeasureOrderDetailService } from '@/api/designerOrder.ts';
import { MeasureOrderItemEnum } from '@/model/enum/orderUpdateType.ts';
import { formatDecimal } from '@/utils/numberFormat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const houseTypeInputCheckList = ['無電梯公寓', '電梯大樓', '透天厝', '別墅', '店面'];
const houseTypeList = ['無電梯公寓', '電梯大樓', '透天厝', '別墅', '店面', '其他(請填寫)'];
const houseInfo = defineModel<TextKeyContent>({ required: true });
const props = defineProps<{ orderId: string; }>();
const selectedHouseType = ref<string>('');
const addItemModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const editItemTitleModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const addItemTitle = ref<string>('');
const editItemTitleData = ref<{ key: string, title: string }>({ key: '', title: '' });
const isAPILoading = ref<boolean>(false);
// const emit = defineEmits(['closeModal']); //保留儲存按鈕離開功能
const temp = reactive<{ [key: string]: { name: string, text: string } }>({});

const getHouseType = () => {
  const houseTypeData = houseInfoData.value.find(data => data.text.mode === 'selectWithText');
  if (houseTypeData && houseTypeList.includes(houseTypeData.text.content.text)) {
    selectedHouseType.value = houseTypeData.text.content.text;
  } else {
    selectedHouseType.value = '其他(請填寫)';
  }
};

const openAddItemModal = () => {
  addItemModalRef.value?.openModal();
};
const closeAddItemModal = () => {
  addItemTitle.value = '';
  addItemModalRef.value?.closeModal();
};
const openEditTitleModal = (key: string, title: string) => {
  editItemTitleData.value = { key, title };
  editItemTitleModalRef.value?.openModal();
};
const closeEditTitleModal = () => {
  editItemTitleModalRef.value?.closeModal();
  editItemTitleData.value = { key: '', title: '' };
};

const addItem = async () => {
  if (isAPILoading.value === false) {
    if (addItemTitle.value === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.addOtherMeasureDataItem({
      orderId: props.orderId,
      names: [addItemTitle.value]
    }, MeasureOrderItemEnum.HouseInfo);
    if (result.status === APIStatusCodeEnum.Success) {
      // 成功後更新 houseInfo
      houseInfo.value = result.content.houseInfo;
      closeAddItemModal();
    }
    isAPILoading.value = false;
  }
};

const editItemTitle = async () => {
  if (isAPILoading.value === false) {
    if (editItemTitleData.value.title === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.updateHouseInfo({
      orderId: props.orderId,
      builders: [{
        key: editItemTitleData.value.key,
        setName: editItemTitleData.value.title,
        setText: ''
      }]
    });
    if (result.status === APIStatusCodeEnum.Success) {
      // 成功後更新 houseInfo
      houseInfo.value = result.content.houseInfo;
      closeEditTitleModal();
    }
    isAPILoading.value = false;
  }
};

const deleteItem = async (key: string) => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.deleteItem({
      orderId: props.orderId,
      keys: [key]
    }, MeasureOrderItemEnum.HouseInfo);
    if (result.status === APIStatusCodeEnum.Success) {
      // 成功後更新 houseInfo
      houseInfo.value = result.content.houseInfo;
    } else if (result.status === APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete) {
      toastInfo('訂單已完成，無法刪除欄位');
    }
    isAPILoading.value = false;
  }
};

const saveData = async () => {
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.updateHouseInfo({
      orderId: props.orderId,
      builders: Object.keys(temp).map(key => {
        return {
          key,
          setName: temp[key].name,
          setText: temp[key].text
        };
      })
    });
    if (result.status === APIStatusCodeEnum.Success) {
      houseInfo.value = result.content.houseInfo; //成功後更新houseInfo
      Object.keys(temp).forEach(key => {
        delete temp[key];
      }); // 清空temp
    }
    // emit('closeModal'); //保留儲存按鈕離開功能
  }
  isAPILoading.value = false;
};

const dataUpdate = (data: {
  key: string,
  updateTime: string,
  name: string,
  text: { content: { hint: string, text: string, unit?: string }, mode: string }
}, otherHouseType?: string) => {
  // console.log('dataUpdate', data);
  // console.log('tempBefore', temp);
  let JsonText: string;
  //特別處理 selectWithText 資料
  if (data.text.mode === TextModeEnum.selectWithText) {
    if (selectedHouseType.value === '其他(請填寫)') {
      if (otherHouseType !== undefined) {
        //更新 要傳換成JSON 的 text.content.text 欄位 為 otherHouseType
        JsonText = ObjectToJSONString(data.text, otherHouseType);
      } else {
        //更新 要傳換成JSON 的 text.content.text 欄位 為空字串
        JsonText = ObjectToJSONString(data.text, '');
      }
    } else {
      //更新 text.content.text 為 selectedHouseType.value
      JsonText = ObjectToJSONString(data.text, selectedHouseType.value);
    }
  } else if (data.text.mode === TextModeEnum.textWithUnit) {
    // 如果 content.text 的輸入本來就是空字串就跳過贓髒資料檢查
    if (data.text.content.text === '') {
      JsonText = ObjectToJSONString(data.text);
    } else {
      // 要先檢查 content.text 的輸入(坪數、高度、屋齡有沒有髒資料 處理資料成乾淨的 浮點數字串格式)
      const formattedText = formatDecimal(data.text.content.text);
      JsonText = ObjectToJSONString({
        content: {
          hint: data.text.content.hint,
          text: formattedText,
          unit: data.text.content.unit
        },
        mode: data.text.mode
      });
    }
  } else {
    // 純文字正常做JSON轉換
    JsonText = ObjectToJSONString(data.text);
  }
  // 更新 Temp
  temp[data.key] = { name: data.name, text: JsonText };
  // console.log('tempAFTER', temp);
};

onMounted(() => {
  getHouseType(); // 初始化時取得房屋類型選項
});

/**
 * 比對Temp如果有值就取代HouseInfo顯示的值
 */
const compareHouseInfoWithTemp = computed(() => {
  // console.log('觸發比較');
  const contents = houseInfo.value.content;
  return contents.map(content => {
    const output: TextKeyItem = {
      key: content.key,
      updateTime: content.updateTime,
      name: content.name,
      text: content.text
    };
    const item = temp[content.key];
    if (item) {
      if (item.name !== '') output.name = item.name;
      if (item.text !== '') output.text = item.text;
    }
    return output;
  });
});

/**
 * 將 compareHouseInfoWithTemp 的資料轉換成可顯示的格式
 */
const houseInfoData = computed(() => {
  let infoData: {
    key: string,
    updateTime: string,
    name: string,
    text: { content: { hint: string, text: string, unit?: string, houseType?: string }, mode: string }
  }[] = [];
  compareHouseInfoWithTemp.value.forEach((item) => {
    const JsonStringContent = JSONStringToObject(item.text);
    infoData.push({
      key: item.key,
      updateTime: item.updateTime,
      name: item.name,
      text: {
        content: {
          hint: JsonStringContent.content.hint,
          text: JsonStringContent.content.text,
          ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
        },
        mode: JsonStringContent.mode
      }
    });
  });
  return infoData;
});

defineExpose({ saveData });

</script>

<template>
  <div class="flex flex-col m-3">
    <div class="flex flex-col gap-2 mx-auto">
      <div v-for="data in houseInfoData" :key="data.key" class="flex justify-start items-center">
        <div v-if="data.text.mode === TextModeEnum.textWithUnit" class="flex max-md:flex-col justify-center max-md:items-start items-center mb-1 gap-3">
          <p>{{ data.name }}</p>
          <div class="flex items-center gap-x-3">
            <input type="text"
                   class="relative block py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 text-sm sm:leading-6"
                   v-model="data.text.content.text"
                   @change="dataUpdate(data)"
                   /><!--:placeholder="data.text.content.hint" -->
            <p>{{ data.text.content.unit }}</p>
          </div>

        </div>
        <div v-else-if="data.text.mode === TextModeEnum.selectWithText"
             class="flex max-md:flex-col justify-center items-start mb-1 gap-3">
          <p class="md:mt-2">{{ data.name }}</p>
          <div class="flex flex-col items-start gap-3">
            <select v-model="selectedHouseType" @change="dataUpdate(data)" class="max-md:text-sm">
              <option v-for="type in houseTypeList" :value="type" :key="type" class="max-md:text-sm">
                {{ type }}
              </option>
            </select>
            <input type="text" v-if="!houseTypeInputCheckList.includes(selectedHouseType)"
                   class="relative block py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 text-sm sm:leading-6"
                   v-model="data.text.content.text"
                   @change="dataUpdate(data,data.text.content.text)"
                   /><!--:placeholder="data.text.content.hint" -->
          </div>
        </div>
        <div v-else-if="data.text.mode === TextModeEnum.text" class="flex max-md:flex-col justify-center max-md:items-start items-center mb-1 gap-3">
          <p class="break-all">{{ data.name }}</p>
          <input type="text"
                 class="relative block border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
                 v-model="data.text.content.text"
                 @change="dataUpdate(data)"
                 /><!--:placeholder="data.text.content.hint" -->
        </div>
        <div v-if="houseInfoData && houseInfoData.findIndex(item => item.key === data.key) > 3"
             class="ml-2">
          <Menu as="div" class="relative flex-none">
            <MenuButton class="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
              <span class="sr-only">Open options</span>
              <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
            </MenuButton>
            <transition enter-active-class="transition ease-out duration-100"
                        enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                        leave-active-class="transition ease-in duration-75"
                        leave-from-class="transform opacity-100 scale-100"
                        leave-to-class="transform opacity-0 scale-95">
              <MenuItems
                class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                <MenuItem v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="openEditTitleModal(data.key, data.name)"
                  >編輯項目名稱</a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="deleteItem(data.key)"
                  >移除項目</a>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <div
        class="mt-1 md:mt-4 md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 "
                @click="openAddItemModal()">新增項目
        </button>
        <!--      <button type="button"-->
        <!--              class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-2"-->
        <!--              @click="saveData()">儲存資訊-->
        <!--      </button>-->
        <!--      //保留儲存按鈕離開功能-->
      </div>
    </div>
  </div>
  <DefaultModal title="新增項目名稱" :showCloseButton="false" :click-outside-close="false" ref="addItemModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemTitle"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="addItem()">確認
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeAddItemModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="編輯項目名稱" :showCloseButton="false" :click-outside-close="false" ref="editItemTitleModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="editItemTitleData.title"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="editItemTitle()">修改
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeEditTitleModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
