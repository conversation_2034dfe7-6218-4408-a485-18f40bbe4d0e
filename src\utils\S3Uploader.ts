import { S3Client, PutObjectCommand, ObjectCannedACL } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-provider-cognito-identity';
import { CognitoIdentityClient } from '@aws-sdk/client-cognito-identity';
import { v4 as uuidv4 } from 'uuid';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import Compressor from 'compressorjs';
import { IsProduction } from '@/utils/hezParameters.ts';

//S3上傳通用方法
export const s3Client = new S3Client({
  region: import.meta.env.VITE_AWS_S3_REGION,
  credentials: fromCognitoIdentityPool({
    client: new CognitoIdentityClient({ region: import.meta.env.VITE_AWS_S3_REGION }),
    identityPoolId: import.meta.env.VITE_AWS_S3_IDENTITY_POOL_ID
  })
});

export const S3uploader = async (uploadFiles: File[]) => {
  const imageUrls: string[] = [];
  for (const file of uploadFiles) {
    let compressedFile = file;
    if (fileTypeCheck(file, UploadFileEnum.Picture)) {
      compressedFile = await compressImage(file);
    }

    const fileInfo = S3UploadInfo(compressedFile.name);
    const uploadParams = {
      Bucket: import.meta.env.VITE_AWS_S3_BUCKET_NAME,
      Key: fileInfo.uploadPath,
      Body: compressedFile,
      ContentType: compressedFile.type,
      ACL: ObjectCannedACL.public_read
    };

    try {
      await s3Client.send(new PutObjectCommand(uploadParams));
      const imageUrl = fileInfo.fileURL; // 更新 imageUrl 以顯示 S3 上的圖片
      imageUrls.push(imageUrl);
    } catch (error) {
      console.error('S3檔案上傳失敗');
      return [];
    }
  }
  console.log('S3檔案上傳成功');
  return imageUrls;
};

const S3UploadInfo = (fileName: string) => {
  const extension = fileName.slice(fileName.lastIndexOf('.'));
  let folder = 'file'; // 預設資料夾為 file
  const pictureExtension = ['.bmp', '.jpg', '.jpeg', '.png', '.gif'];
  const voiceExtension = ['.mp3', '.3gpp'];
  const videoExtension = ['.mp4'];

  if (pictureExtension.includes(extension)) {
    folder = 'picture';
  } else if (voiceExtension.includes(extension)) {
    folder = 'voice';
  } else if (videoExtension.includes(extension)) {
    folder = 'video';
  }

  const environment = IsProduction ? 'prod' : 'dev';
  const uuidString = uuidv4();
  const uploadPath = `${environment}/${folder}/${uuidString}${extension}`;
  const fileURL = `${import.meta.env.VITE_AWS_S3_URL}/${folder}/${uuidString}${extension}`;

  return { uploadPath, fileURL };
};

export const fileTypeCheck = (file: File, type: UploadFileEnum) => {
  switch (type) {
    case UploadFileEnum.Picture:
      return file.type === 'image/bmp' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
    case UploadFileEnum.Voice:
      return file.type === 'audio/mp3' || file.type === 'audio/3gpp';
    case UploadFileEnum.Video:
      return file.type === 'video/mp4';
    case UploadFileEnum.File:
      return true;
  }
};
// 圖片壓縮
const compressImage = (file: File) => {
  return new Promise<File>((resolve, reject) => {
    new Compressor(file, {
      quality: 0.8, // 壓縮質量
      width: 1280, // 壓縮寬度
      mimeType: 'image/png', // 輸出類型
      success(result) {
        resolve(result as File);
      },
      error(err) {
        console.error('壓縮圖片失敗:', err);
        reject(err);
      }
    });
  });
};
