import type { WorkType } from '@/model/general/account';
import { RegionEnum } from '@/model/enum/taiwanRegion.ts';

export interface GetInfoRequest {
}//傳空物件就好，除非未來有更動。

export interface UpdateAvatarRequest {
  avatarUrl: string;
}

export interface UpdateTaiwanIdRequest {
  username: string;
  frontSideUrl: string;
  backSideUrl: string;
}

export interface UpdateCompanyRequest {
  name: string;
  guiNumber: string; // 統一編號
  docUrls: string[];
  description: string;
  logo: string;
  serviceTime: string;
  address: string;
}

export interface UpdateWorkRequest {
  workType: WorkType;
  workRegion: RegionEnum[];
}

export interface EmailCheckRequest {
  email: string;
}

export interface EmailVerifyRequest {
  verifyCode: string;
}

export interface updateWebsiteRequest {
  websiteUrl: string;
}

export interface getRedirectDesignerPageRequest {
  targetUrl: string;
}
