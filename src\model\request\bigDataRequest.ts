export interface BigDataEstimateRequest {
  orderId: string;
  areaPing: number;
  bathroomCount: number;
  constructionArea: number;
  demolitionProject: number;
  drainagePipeline: number;
  bathroomFacilities: number;
  powerPipeline: number;
  partitionProject: number;
  windowCurtain: number;
  doorSet: number;
  tiles: number;
  ceiling: number;
  paintLevel: number;
  livingRoomFloor: number;
  kitchenCabinet: number;
  livingRoomCabinet: number;
  wardrobe: number;
}
