<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import { toastInfo } from '@/utils/toastification.ts';
import {
  CustomerConstructionOrderDetailService, CustomerConstructionOrderService
} from '@/api/customerOrder.ts';
import {
  AcceptanceProcessStatusEnum, AmountStatusEnum, AppendProcessStatusEnum,
  ConstructionOrderStatusEnum, ConstructionProcessStatusEnum,
  RemittanceStatusEnum, SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import CustomerHousePhoto from '@/components/customer/OrderDetail/CustomerHousePhoto.vue';
import CustomerHouseCheck from '@/components/customer/OrderDetail/CustomerHouseCheck.vue';
import CustomerFloorPlan from '@/components/customer/OrderDetail/CustomerFloorPlan.vue';
import CustomerHouseNote from '@/components/customer/OrderDetail/CustomerHouseNote.vue';
import CustomerStep1TextContent from '@/components/customer/OrderDetail/CustomerStep1TextContent.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  UserCircleIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import HezDivider from '@/components/General/HezDivider.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import GoToAppModal from '@/components/Modal/GoToAppModal.vue';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import Customer2D from '@/components/customer/OrderDetail/Customer2D.vue';
import Customer3D from '@/components/customer/OrderDetail/Customer3D.vue';
import { budgetCombineLowToHigh, calculatePercentage, moneyAddCommas } from '@/utils/budgetFormat.ts';
import CustomerSignModal from '@/components/Modal/CustomerSignModal.vue';
import CustomerRemitModal from '@/components/Modal/CustomerRemitModal.vue';
import { CustomerOrderConstructionItem } from '@/model/response/customerConstructionOrderResponse.ts';
import CustomerHouseInfoForStep3 from '@/components/customer/OrderDetail/CustomerHouseInfoForStep3.vue';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import CustomerCheckProcessListModal from '@/components/Modal/CustomerCheckProcessListModal.vue';
import CustomerCheckWorkModal from '@/components/Modal/CustomerCheckWorkModal.vue';
import CustomerNoMoneyModal from '@/components/Modal/CustomerNoMoneyModal.vue';
import CustomerWaitProcessListModal from '@/components/Modal/CustomerWaitProcessListModal.vue';
import CustomerCheckProcessGoToAppModal from '@/components/Modal/CustomerCheckProcessGoToAppModal.vue';
import CustomerWorkPhotoModal from '@/components/Modal/CustomerWorkPhotoModal.vue';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import CustomerRateModal from '@/components/Modal/CustomerRateModal.vue';

const route = useRoute();
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const chatHubStore = useChatHubStore();
const orderId = route.params.id as string;
const designNotAssignModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const deleteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const goToAppModalRef = ref<InstanceType<typeof GoToAppModal> | null>(null);
const signModalRef = ref<InstanceType<typeof CustomerSignModal> | null>(null);
const remitModalRef = ref<InstanceType<typeof CustomerRemitModal> | null>(null);
const checkProcessListModalRef = ref<InstanceType<typeof CustomerCheckProcessListModal> | null>(null);
const checkWorkModalRef = ref<InstanceType<typeof CustomerCheckWorkModal> | null>(null);
const noMoneyModalRef = ref<InstanceType<typeof CustomerNoMoneyModal> | null>(null);
const waitProcessListModalRef = ref<InstanceType<typeof CustomerWaitProcessListModal> | null>(null);
const checkProcessGoToAppModalRef = ref<InstanceType<typeof CustomerCheckProcessGoToAppModal> | null>(null);
const workPhotoModalRef = ref<InstanceType<typeof CustomerWorkPhotoModal> | null>(null);
const deleteHasDesignerModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const appendProcessModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const rateModalRef = ref<InstanceType<typeof CustomerRateModal> | null>(null);
const goToAppModalProps = ref<{ title: string; content: string; content2?: string }>({
  title: '',
  content: '',
  content2: ''
});
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal> | null>(null);
const completeModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const elapsedMinutes = ref(0);
const elapsedSeconds = ref(0);
const interval = ref<number | null>(null);
const isoTimestamp = ref<string | null>(null);
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);

const orderData = ref<CustomerOrderConstructionItem>({
  orderId: '',
  createTime: '',
  refreshTime: '',
  status: ConstructionOrderStatusEnum.Comparing,
  contract: {
    signingStatus: SigningStatusEnum.Init,
    acceptanceProcessStatus: AcceptanceProcessStatusEnum.Init,
    remittanceStatus: RemittanceStatusEnum.NotRemitted,
    constructionEstimate: {
      upper: 0,
      lower: 0
    },
    constructionAmount: 0,
    constructionDiscountAmount: 0,
    amountStatus: AmountStatusEnum.NotQuoted,
    constructionAmounts: [],
    remittedAmount: 0,
    verifiedAmount: 0,
    bigDataEstimate: {
      upper: 0,
      lower: 0
    },
    appendProcess: {
      updateTime: '',
      status: AppendProcessStatusEnum.InitOrCompleted,
      times: 0,
      process: {
        name: '',
        amount: 0,
        amountDocUrl: ''
      }
    }
  },
  isDeleted: false,
  designerId: '',
  designerName: '',
  designerAvatar: '',
  designerPhone: '',
  customerId: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  publishInfo: {
    isContinuedDesigner: false,
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    constructionNote: '',
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    keyDesignDetail: '',
    referenceImages: []
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
  },
  designContent: {
    design2D: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    design3D: {
      updateTime: '',
      updateCount: 0,
      content: []
    }
  },
  constructionContent: {
    process: {
      updateTime: '',
      updateCount: 0,
      content: []
    }
  },
  quotationCount: 0,
  chatRoomId: '',
  orderRatingId: '',
  invoiceId: ''
});

const goHome = () => {
  router.push({
    name: 'customerhome'
  });
};

const constructionDetail = computed(() => {
  return {
    orderId: orderData.value.orderId,
    createTime: orderData.value.createTime,
    refreshTime: orderData.value.refreshTime,
    status: orderData.value.status,
    contract: {
      signingStatus: orderData.value.contract.signingStatus,
      acceptanceProcessStatus: orderData.value.contract.acceptanceProcessStatus,
      remittanceStatus: orderData.value.contract.remittanceStatus,
      constructionEstimate: budgetCombineLowToHigh(orderData.value.contract.constructionEstimate),
      constructionAmount: moneyAddCommas(orderData.value.contract.constructionAmount),
      constructionDiscountAmount: moneyAddCommas(orderData.value.contract.constructionDiscountAmount),
      amountStatus: orderData.value.contract.amountStatus,
      constructionAmounts: orderData.value.contract.constructionAmounts,
      remittedAmount: moneyAddCommas(orderData.value.contract.remittedAmount),
      verifiedAmount: moneyAddCommas(orderData.value.contract.verifiedAmount),
      appendProcess: {
        updateTime: formatFullDateTimeWithDay(orderData.value.contract.appendProcess.updateTime),
        status: orderData.value.contract.appendProcess.status,
        times: orderData.value.contract.appendProcess.times,
        process: {
          name: orderData.value.contract.appendProcess.process.name,
          amount: moneyAddCommas(orderData.value.contract.appendProcess.process.amount),
          amountDocUrl: orderData.value.contract.appendProcess.process.amountDocUrl
        }
      }
    },
    isDeleted: orderData.value.isDeleted,
    designerId: orderData.value.designerId,
    designerName: orderData.value.designerName,
    designerAvatar: orderData.value.designerAvatar,
    customerId: orderData.value.customerId,
    customerName: orderData.value.customerName,
    address: orderData.value.address,
    publishInfo: orderData.value.publishInfo,
    measureContent: {
      houseInfo: orderData.value.measureContent.houseInfo,
      photos: orderData.value.measureContent.photos,
      houseCheck: orderData.value.measureContent.houseCheck,
      floorPlan: orderData.value.measureContent.floorPlan,
      note: orderData.value.measureContent.note,
      constructionRequest: orderData.value.measureContent.constructionRequest,
      waterQuality: orderData.value.measureContent.waterQuality,
      airQuality: orderData.value.measureContent.airQuality,
      noise: orderData.value.measureContent.noise,
      humidity: orderData.value.measureContent.humidity,
      radiation: orderData.value.measureContent.radiation,
    },
    designContent: {
      design2D: orderData.value.designContent.design2D,
      design3D: orderData.value.designContent.design3D
    },
    constructionContent: {
      process: {
        updateTime: orderData.value.constructionContent.process.updateTime,
        updateCount: orderData.value.constructionContent.process.updateCount,
        content: orderData.value.constructionContent.process.content.map((item) => {
          return {
            updateTime: formatFullDateTimeWithDay(item.updateTime),
            name: item.name,
            media: item.media,
            key: item.key,
            updateCount: item.updateCount,
            status: item.status,
            amount: `${moneyAddCommas(item.amount)} (${calculatePercentage(item.amount, orderData.value.contract.constructionAmount)})`,
            isAppend: item.isAppend,
            amountDocUrl: item.amountDocUrl
          };
        })
      }
    },
    quotationCount: orderData.value.quotationCount,
    chatRoomId: orderData.value.chatRoomId,
    nameAddress: {
      name: orderData.value.customerName,
      address: orderData.value.address.fullName
    },
    latestConstructionAmount: orderData.value.contract.constructionAmounts.length === 0 ? {
      amount: '0',
      documentUrl: ''
    } : {
      amount: moneyAddCommas(orderData.value.contract.constructionAmounts.slice(-1)[0].amount),
      documentUrl: orderData.value.contract.constructionAmounts.slice(-1)[0].documentUrl
    }
  };
});

const showSignOrProcessModal = () => {
  if (constructionDetail.value.contract.signingStatus === SigningStatusEnum.Init || constructionDetail.value.contract.signingStatus === SigningStatusEnum.VerifyFail) {
    signModalRef.value?.openModal();
  } else if (constructionDetail.value.contract.signingStatus === SigningStatusEnum.Verifying) {
    goToAppModalProps.value = { title: '合約簽署', content: '合約尚在審核中' };
    goToAppModalRef.value?.openModal();
  } else if (constructionDetail.value.contract.acceptanceProcessStatus === AcceptanceProcessStatusEnum.Init) {
    checkProcessListModalRef.value?.openModal();
  } else if (constructionDetail.value.contract.acceptanceProcessStatus < AcceptanceProcessStatusEnum.AgreementReached) {
    goToAppModalProps.value = { title: '驗收流程', content: '驗收流程確認中' };
    goToAppModalRef.value?.openModal();
  }
};

//Step3 聯絡功能只要設計師報價了就能用
const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      switch (constructionDetail.value.status) {
        case ConstructionOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '免費語音', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          appUsePhoneModalRef.value?.openModal();
          break;
      }
      break;
    case contactItemEnum.Meet:
      switch (constructionDetail.value.status) {
        case ConstructionOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '視訊會議', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          createMeet(constructionDetail.value.chatRoomId, false, customerInfoStore.userId);
      }
      break;
    case contactItemEnum.Chat:
      switch (constructionDetail.value.status) {
        case ConstructionOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '聊天室', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          if (chatHubStore._worker === null) {
            sharedWorkerNotSupportModalRef.value?.openModal();
          } else {
            chatRoomRef.value?.showChatRoom();
          }
      }
      break;
    case contactItemEnum.Note:
      switch (constructionDetail.value.status) {
        case ConstructionOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '紀錄本', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        default:
          noteBookRef.value?.openModal();
      }
  }
};

const topInfoClicked = () => {
  switch (constructionDetail.value.status) {
    case ConstructionOrderStatusEnum.Comparing:
      designNotAssignModalRef.value?.openModal();
      break;
    case ConstructionOrderStatusEnum.Quoting:
      switch (constructionDetail.value.contract.amountStatus) {
        case AmountStatusEnum.NotQuoted:
          waitProcessListModalRef.value?.openModal();
          break;
        case AmountStatusEnum.Quoted:
          checkProcessGoToAppModalRef.value?.openModal();
          break;
        case AmountStatusEnum.CustomerDisagreed:
          goToAppModalProps.value = { title: '家易App', content: '設計師正在修正中' };
          goToAppModalRef.value?.openModal();
          break;
      }
      break;
    case ConstructionOrderStatusEnum.Contracting:
      showSignOrProcessModal();
      break;
    case ConstructionOrderStatusEnum.Working:
      if (orderData.value.contract.remittedAmount === 0) {
        remitModalRef.value?.openModal();
      }
      break;
    default:
      break;
  }
};

const customerInfoClicked = () => {
  switch (constructionDetail.value.status) {
    case ConstructionOrderStatusEnum.Comparing:
      deleteModalRef.value?.openModal();
      break;
    case ConstructionOrderStatusEnum.Quoting:
      deleteHasDesignerModalRef.value?.openModal();
      break;
    case ConstructionOrderStatusEnum.Contracting:
      showSignOrProcessModal();
      break;
    case ConstructionOrderStatusEnum.Working:
      if (orderData.value.contract.remittedAmount === 0) {
        remitModalRef.value?.openModal();
      }
      break;
    default:
      break;
  }
};

const workProcessClicked = (index: number) => {
  const work = orderData.value.constructionContent.process.content[index];
  switch (work.status) {
    case ConstructionProcessStatusEnum.NotRequested:
      if (work.media.length > 0) {
        workPhotoModalRef.value?.openModal(work);
      } else {
        goToAppModalProps.value = {
          title: work.name,
          content: '目前設計師尚未上傳完工證明圖片',
          content2: '請透過視訊會議與設計師溝通'
        };
        goToAppModalRef.value?.openModal();
      }
      break;
    case ConstructionProcessStatusEnum.PendingDisbursement:
      checkWorkModalRef.value?.openModal(work, orderData.value.orderId);
      break;
    case ConstructionProcessStatusEnum.Reject:
      goToAppModalProps.value = {
        title: work.name,
        content: '設計師正在修正中',
        content2: '請透過視訊會議與設計師溝通'
      };
      goToAppModalRef.value?.openModal();
      break;
    case ConstructionProcessStatusEnum.InsufficientBalance:
      noMoneyModalRef.value?.openModal();
      break;
    default:
      workPhotoModalRef.value?.openModal(work);
      break;
  }
};

const deleteOrder = async () => {
  await CustomerConstructionOrderService.deleteOrder({ orderId: orderId });
  toastInfo('訂單已刪除');
  //回訂單紀錄
  await router.push({
    name: 'customerorderlist'
  });
};

const getOrderData = async () => {
  const response = await CustomerConstructionOrderDetailService.getOneOrderDetail({ orderId: orderId });
  if (response.status !== APIStatusCodeEnum.Success) {
    toastInfo('找不到訂單資料');
    goHome();
  } else {
    orderData.value = response.result;
    if (orderData.value.status === ConstructionOrderStatusEnum.Completed) {
      //TODO Step3 完成之後應該不用跳對話框 除非未來要導到發票
      // completeModalRef.value?.openModal();

      if (orderData.value.orderRatingId) rateModalRef.value?.openModal();
    }
    if (orderData.value.status === ConstructionOrderStatusEnum.Comparing) {
      isoTimestamp.value = orderData.value.createTime;
      updateElapsedTime();
    }
  }
};

const updateElapsedTime = () => {
  if (isoTimestamp.value) {
    const startTime = new Date(isoTimestamp.value).getTime();
    const now = Date.now();
    const elapsedTime = now - startTime;

    elapsedMinutes.value = Math.floor(elapsedTime / 1000 / 60);
    elapsedSeconds.value = Math.floor((elapsedTime / 1000) % 60);
  }
};

const appendProcessClicked = () => {
  appendProcessModalRef.value?.openModal();
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

watch(isoTimestamp, (newTimestamp) => {
  if (newTimestamp) {
    updateElapsedTime();
    interval.value = setInterval(updateElapsedTime, 1000) as unknown as number;
  }
});

onMounted(async () => {
  if (!customerInfoStore.loginState) {
    goHome();
    return;
  }
  await getOrderData();
});

onBeforeUnmount(() => {
  if (interval.value !== null) {
    clearInterval(interval.value);
  }
});

</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <!--    基礎資訊區-->
    <div class="cus-border text-lg">
      <div class="flex flex-row justify-center ">
        <p class="text-2xl font-bold text-black">裝潢施工</p>
      </div>
      <div class="flex p-2 gap-x-4 justify-start items-start max-md:flex-col"
           @click="topInfoClicked()">
        <img v-if="constructionDetail.designerAvatar" class="w-20 h-20 rounded-full"
             :src="constructionDetail.designerAvatar" alt="designerAvatar" />
        <UserCircleIcon v-else
                        class="h-24 max-md:h-12 w-24 max-md:w-12 flex-none" />
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class=" font-medium text-nowrap">設計師姓名</p>
            <p v-if="constructionDetail.status !== ConstructionOrderStatusEnum.Comparing"
               class=" font-bold">
              {{ constructionDetail.designerName }}</p>
            <p v-else class=" font-bold text-red-600">(未確定)</p>
          </div>
          <!--          簽合約之前才出現-->
          <div class="flex gap-3" v-if="constructionDetail.status < ConstructionOrderStatusEnum.Contracting">
            <p class=" font-medium text-nowrap">設計師報價</p>

            <p v-if="constructionDetail.status === ConstructionOrderStatusEnum.Comparing"
               class=" font-bold text-red-600"> (比價中) </p>
            <p v-else class=" font-bold ">{{ constructionDetail.contract.constructionEstimate }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-medium text-nowrap">裝潢施工費</p>
            <p v-if="constructionDetail.status === ConstructionOrderStatusEnum.Comparing"
               class="font-bold text-red-600"> (比價中) </p>
            <p v-else>
              <span v-if="orderData.contract.constructionAmount === -1" class="font-bold text-red-600">(未提供)</span>
              <span v-else class="font-bold text-black">{{ constructionDetail.contract.constructionAmount }}</span>
            </p>
          </div>
          <!--          接續設計師才出現-->
          <div class="flex gap-3"
               v-if="constructionDetail.publishInfo.isContinuedDesigner">
            <p class=" font-medium text-nowrap">優惠總金額</p>
            <p class=" font-bold"> {{ constructionDetail.contract.constructionDiscountAmount }} </p>
          </div>
        </div>
      </div>
      <HezDivider />
      <div class="flex gap-2 justify-evenly">
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Phone)">
          <PhoneIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">免費語音</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Meet)">
          <VideoCameraIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">視訊會議</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Chat)">
          <ChatBubbleLeftEllipsisIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">聊天室</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Note)">
          <ClipboardDocumentListIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">紀錄本</p>
        </div>
      </div>
    </div>

    <div v-if="orderData.chatRoomId && chatHubStore._worker !== null"
         class="fixed flex flex-col justify-end bottom-0 right-0 w-80 z-40"
         :class="isChatRoomShow ? 'h-3/5' : 'h-auto' ">
      <ChatRoom :is-for-meet="false"
                :room-id="orderData.chatRoomId" :is-designer="false"
                @show-chat-room="changeChatRoomShow"
                ref="chatRoomRef" />
    </div>

    <!--    固定客戶匯款資訊區同時也是簽約&驗收流程狀態區 -->
    <!-- CustomerInfo Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="customerInfoClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          1
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3">
            <p class="font-bold md:text-lg">客戶需支付裝潢費用</p>
            <p v-if="constructionDetail.status >= ConstructionOrderStatusEnum.Contracting"
               class="font-bold md:text-lg">
              <span
                v-if="!constructionDetail.publishInfo.isContinuedDesigner">
                {{ constructionDetail.contract.constructionAmount }}
              </span>
              <span v-else>{{ constructionDetail.contract.constructionDiscountAmount }} (已折抵)</span>
            </p>
            <p v-else-if="constructionDetail.contract.amountStatus === AmountStatusEnum.NotQuoted"
               class=" font-bold md:text-lg text-black">
              (未提供)</p>
            <p v-else-if="constructionDetail.contract.amountStatus === AmountStatusEnum.Quoted"
               class=" font-bold md:text-lg text-black">
              {{ constructionDetail.contract.constructionDiscountAmount }}</p>
            <p v-else class=" font-bold md:text-lg text-red-600">(比價中)</p>
          </div>
          <div class="flex gap-3" v-if="constructionDetail.status === ConstructionOrderStatusEnum.Contracting">
            <p class=" font-bold md:text-lg text-nowrap">合約狀態</p>
            <p v-if="constructionDetail.contract.signingStatus === SigningStatusEnum.Init"
               class=" font-bold md:text-lg text-red-600">未簽署</p>
            <p v-else-if="constructionDetail.contract.signingStatus === SigningStatusEnum.Verifying"
               class=" font-bold md:text-lg text-blue-600">已簽署(審核中)</p>
            <p v-else-if="constructionDetail.contract.signingStatus === SigningStatusEnum.VerifyFail"
               class=" font-bold md:text-lg text-red-600">已簽署(審核失敗)</p>
            <p v-else class=" font-bold md:text-lg text-black">已簽署</p>
          </div>
          <div class="flex gap-3" v-if="constructionDetail.status === ConstructionOrderStatusEnum.Contracting">
            <p class=" font-bold md:text-lg text-nowrap">流程狀態</p>
            <p v-if="constructionDetail.contract.acceptanceProcessStatus < AcceptanceProcessStatusEnum.AgreementReached"
               class=" font-bold md:text-lg text-red-600">未確認</p>
            <p v-else class=" font-bold md:text-lg text-black">已確認</p>
          </div>
          <div class="flex gap-3" v-if="constructionDetail.status <= ConstructionOrderStatusEnum.Quoting">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <p class=" font-bold md:text-lg text-black">(未確認)</p>
          </div>
          <div class="flex gap-3" v-if="constructionDetail.status === ConstructionOrderStatusEnum.Contracting">
            <p class=" font-bold md:text-lg text-nowrap">匯款狀態</p>
            <p v-if="constructionDetail.contract.remittanceStatus === RemittanceStatusEnum.NotRemitted"
               class=" font-bold md:text-lg text-red-600">未匯款</p>
            <p v-else-if="constructionDetail.contract.remittanceStatus === RemittanceStatusEnum.InProgress"
               class=" font-bold md:text-lg text-blue-600">已匯款(審核中)</p>
            <p v-else class=" font-bold md:text-lg text-black">已匯款</p>
          </div>
          <div class="flex gap-3" v-if="constructionDetail.status >= ConstructionOrderStatusEnum.Working">
            <p class=" font-bold md:text-lg text-nowrap">匯款狀態</p>
            <p v-if="orderData.contract.verifiedAmount === 0"
               class=" font-bold md:text-lg text-red-600">已匯款 NT$ 0</p>
            <p v-else class=" font-bold md:text-lg text-black">已匯款
              {{ constructionDetail.contract.verifiedAmount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!--    施工流程區-->
    <template v-if="constructionDetail.status >= ConstructionOrderStatusEnum.Working">
      <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
           v-for="(process, index) in constructionDetail.constructionContent.process.content"
           :key="index" @click="workProcessClicked(index)">
        <div class="flex items-center">
          <div
            class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
            {{ index + 2 }}
          </div>
          <div class="flex flex-col gap-3">
            <div class="flex gap-3 ">
              <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
              <p class=" font-bold md:text-lg text-black">{{ process.name }}</p>
            </div>
            <div class="flex gap-3 ">
              <p class=" font-bold md:text-lg text-nowrap">工程款</p>
              <p class=" font-bold md:text-lg text-black">{{ process.amount }}</p>
            </div>
            <div class="flex gap-3">
              <p class=" font-bold md:text-lg text-nowrap">狀態</p>
              <p v-if="process.status === ConstructionProcessStatusEnum.NotRequested"
                 class=" font-bold md:text-lg">
                <!--                //TODO 看要不要改成設計師尚未請款-->
                <span v-if="process.media.length > 0" class="text-blue-600">未同意撥款</span>
                <span v-else>未同意撥款</span>
              </p>
              <p v-else-if="process.status === ConstructionProcessStatusEnum.PendingDisbursement"
                 class=" font-bold md:text-lg text-blue-600">設計師要求撥款</p>
              <p v-else-if="process.status === ConstructionProcessStatusEnum.Completed"
                 class=" font-bold md:text-lg text-black">完成撥款</p>
              <p v-else-if="process.status === ConstructionProcessStatusEnum.Reject"
                 class=" font-bold md:text-lg text-red-600">使用者要求修正</p>
              <p v-else-if="process.status === ConstructionProcessStatusEnum.InsufficientBalance">
                <span v-if="process.media.length !== 0 " class=" font-bold md:text-lg text-red-600">餘額不足</span>
                <span v-else class=" font-bold md:text-lg text-black">未同意撥款</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!--      追加工程 (如果有)-->
      <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
           v-if="constructionDetail.contract.appendProcess.process.name !== '' && constructionDetail.contract.appendProcess.status !== AppendProcessStatusEnum.CustomerDisagreed"
           @click="appendProcessClicked()">
        <div class="flex items-center">
          <div
            class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
            {{ (constructionDetail.constructionContent.process.content.length) + 2 }}
          </div>
          <div class="flex flex-col gap-3">
            <div class="flex gap-3 ">
              <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
              <p class=" font-bold md:text-lg text-black">
                {{ constructionDetail.contract.appendProcess.process.name }}</p>
            </div>
            <div class="flex gap-3 ">
              <p class=" font-bold md:text-lg text-nowrap">工程款</p>
              <p class=" font-bold md:text-lg text-black">
                {{ constructionDetail.contract.appendProcess.process.amount }}</p>
            </div>
            <div class="flex gap-3">
              <p class=" font-bold md:text-lg text-nowrap">狀態</p>
              <p class=" font-bold md:text-lg text-blue-600">設計師要求同意追加工程</p>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!--固定資訊區-->
    <Customer2D :model-value="constructionDetail.designContent.design2D" />
    <Customer3D :model-value="constructionDetail.designContent.design3D" />
    <CustomerHouseInfoForStep3 v-model:house-info="constructionDetail.measureContent.houseInfo"
                               v-model:publish-info="constructionDetail.publishInfo"
                               v-model:name-address="constructionDetail.nameAddress" />
    <CustomerHousePhoto :model-value="constructionDetail.measureContent.photos" />
    <CustomerHouseCheck :model-value="constructionDetail.measureContent.houseCheck" />
    <CustomerFloorPlan :model-value="constructionDetail.measureContent.floorPlan" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.constructionRequest.content.length > 0 || new Date(constructionDetail.measureContent.constructionRequest.updateTime) > new Date(0)" title="裝潢需求" :data="constructionDetail.measureContent.constructionRequest" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.waterQuality.content.length > 0 || new Date(constructionDetail.measureContent.waterQuality.updateTime) > new Date(0)" title="水質檢測" :data="constructionDetail.measureContent.waterQuality" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.airQuality.content.length > 0 || new Date(constructionDetail.measureContent.airQuality.updateTime) > new Date(0)" title="空氣檢測" :data="constructionDetail.measureContent.airQuality" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.noise.content.length > 0 || new Date(constructionDetail.measureContent.noise.updateTime) > new Date(0)" title="噪音檢測" :data="constructionDetail.measureContent.noise" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.humidity.content.length > 0 || new Date(constructionDetail.measureContent.humidity.updateTime) > new Date(0)" title="濕度檢測" :data="constructionDetail.measureContent.humidity" />
    <CustomerStep1TextContent v-if="constructionDetail.measureContent.radiation.content.length > 0 || new Date(constructionDetail.measureContent.radiation.updateTime) > new Date(0)" title="電磁輻射" :data="constructionDetail.measureContent.radiation" />
    <CustomerHouseNote :model-value="constructionDetail.measureContent.note" />
  </div>

  <!--  對話框區-->
  <DefaultModal title="裝潢施工報價" :show-close-button="true" :click-outside-close="true" ref="designNotAssignModalRef"
                @closeModal="designNotAssignModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">因網頁無法自動刷新</p>
        <p class="font-bold md:text-lg">請在家易App獲得即時報價</p>
        <p class="font-bold md:text-lg">超過百位設計師方便您比價</p>
        <p class="font-bold md:text-lg">找尋時間：<span>{{ elapsedMinutes }} 分鐘 {{ elapsedSeconds }} 秒</span></p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <DefaultModal title="刪除訂單" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
                ref="deleteModalRef" @closeModal="deleteModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">確認刪除此訂單?</p>
        <p class="font-bold md:text-lg">
          若超過三次會被封鎖帳號!</p>
        <button type="button"
                class="button-basic w-full mt-2 ring-1 bg-red-300 ring-inset ring-gray-300 hover:bg-red-400"
                @click="deleteOrder()">確認刪除訂單
        </button>
      </div>
    </div>
  </DefaultModal>

  <!--  step3才用得到-->
  <DefaultModal title="刪除訂單" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
                ref="deleteHasDesignerModalRef" @closeModal="deleteHasDesignerModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">如果您刪除此訂單</p>
        <p class="font-bold md:text-lg">此設計師將不會再為你服務</p>
        <p class="font-bold md:text-lg">你可以選擇其他設計師進行報價</p>
        <p class="font-bold md:text-lg">但為了避免浪費設計師時間</p>
        <p class="font-bold md:text-lg">建議不要超過五次</p>
        <button type="button"
                class="button-basic w-full mt-2 ring-1 bg-red-300 ring-inset ring-gray-300 hover:bg-red-400"
                @click="deleteOrder()">確認刪除訂單
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="裝潢施工已完成" :show-close-button="true" :click-outside-close="true" ref="completeModalRef"
                @close-modal="completeModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="p-4 m-2 border-black border">
        <!--        //TODO-->
        <p class="font-bold md:text-lg">等漢祥畫圖</p>
      </div>
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">建議下載家易App</p>
        <p class="font-bold md:text-lg">查看發票節稅</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <DefaultModal title="追加工程" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="appendProcessModalRef" @closeModal="appendProcessModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">如果您需要確認追加工程</p>
        <p class="font-bold md:text-lg">請至App端操作</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <GoToAppModal :title="goToAppModalProps.title" :content="goToAppModalProps.content" ref="goToAppModalRef" />
  <AppUsePhoneModal :user-type="UserTypeEnum.Customer" ref="appUsePhoneModalRef" />
  <CustomerSignModal title="合約簽署" ref="signModalRef" />
  <CustomerRemitModal title="匯款狀態" ref="remitModalRef" />
  <CustomerCheckProcessListModal title="驗收流程" ref="checkProcessListModalRef" />
  <CustomerNoMoneyModal title="餘額不足" ref="noMoneyModalRef" />
  <CustomerWaitProcessListModal ref="waitProcessListModalRef" />
  <CustomerCheckProcessGoToAppModal ref="checkProcessGoToAppModalRef" />
  <CustomerWorkPhotoModal ref="workPhotoModalRef" />
  <CustomerCheckWorkModal ref="checkWorkModalRef" @refresh-data="getOrderData()" />
  <NoteBook :room-id="orderData.chatRoomId" :is-designer="false" ref="noteBookRef" />
  <AndroidNotSupportSharedWorkerModal :is-designer="false" ref="sharedWorkerNotSupportModalRef" />
  <CustomerRateModal :order-type="OrderTypeEnum.Construction"
                     :deisgner-name="orderData.designerName"
                     :deisgner-avatar="orderData.designerAvatar"
                     :order-rating-id="orderData.orderRatingId"
                     ref="rateModalRef" />
</template>
