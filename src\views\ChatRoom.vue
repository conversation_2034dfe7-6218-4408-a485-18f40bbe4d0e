<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { toastInfo } from '@/utils/toastification.ts';
import { ChatService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import {
  ChatRoomResponse, GetChatRoomInfoResponse,
  GetChatRoomMsgResponse,
  MessageData,
  RoomData,
  SendMsgResponse, UnSendMsgResponse
} from '@/model/response/chatResponse.ts';
import {
  MeetTypeEnum,
  MsgFileTypeEnum,
  MsgSendTypeEnum,
  MsgStatusEnum,
  MsgTypeEnum
} from '@/model/enum/chatEnum.ts';
import { FileItem } from '@/model/request/chatRequest.ts';
import { formatDuration, formatMonthDateTime, formatMonthDateWithDay } from '@/utils/timeFormat.ts';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { IsProduction } from '@/utils/hezParameters.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import { downloadFile } from '@/utils/fileDownloader.ts';
import ImageModal from '@/components/ChatRoom/ImageModal.vue';
import ImageUpload from '@/components/ChatRoom/ImageUpload.vue';
import FileUpload from '@/components/ChatRoom/FileUpload.vue';
import { useRoute } from 'vue-router';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import NoteUpdate from '@/components/ChatRoom/NoteUpdate.vue';
import { getFileExtension, isMsgLink, openNewTab } from '@/utils/chatRoomUtils.ts';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import { InvokeSignalRMethodEnum, OnSignalRMethodEnum, WorkerEventEnum } from '@/model/enum/sharedWorkerEnum.ts';

const props = defineProps({
  roomId: {
    type: String,
    required: true
  },
  isForMeet: {
    type: Boolean,
    required: true
  },
  isDesigner: {
    type: Boolean,
    required: true
  }
});
const emit = defineEmits(['showChatRoom']);
const userInfoStore = props.isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();

//決定聊天室區塊是否隱藏
const isShow = ref(true);
const isApiLoading = ref(false);
const msgSkip = ref(0);
const roomData = ref<RoomData>();
// msgDataList 會因為 SignalR 的即時更新或是往上拉取得歷史訊息而改變，但順序會是亂的，所以只要任何有關順序的操作，使用 sortMsgList
const msgDataList = ref<MessageData[]>([]);
const sendMsgText = ref('');
const chatContainer = ref<HTMLDivElement | null>(null);
const imageModalRef = ref<InstanceType<typeof ImageModal> | null>(null);
const updateRef = ref<InstanceType<typeof NoteUpdate> | null>(null);
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const route = useRoute();
const meetInfo = ref<{
  isMeetRunning: boolean;
  lastMeetStartTime: string;
  lastMeetId: string;
}>(
  {
    isMeetRunning: false,
    lastMeetStartTime: '',
    lastMeetId: ''
  }
);

const chatHubStore = useChatHubStore();

chatHubStore.worker.port.onmessage = (e) => {
  if (e.data.isDesigner !== props.isDesigner) return;
  if (e.data.type === WorkerEventEnum.OnSignalR) {
    const payload = e.data.payload;
    switch (payload.method) {
      case OnSignalRMethodEnum.Room: { //args: room: RoomData
        if (payload.args[0].roomId === props.roomId) {
          console.log('SignalR-OnRoomMsg', payload.args[0]);
          roomData.value = payload.args[0];
        }
        break;
      }
      case OnSignalRMethodEnum.Message: { //args: data: MessageData,asyncType: MsgAsyncTypeEnum, progressId: string
        if (payload.args[0].roomId === props.roomId) {
          msgDataList.value.push(payload.args[0]);
          if (payload.args[0].message.meet) {
            if (payload.args[0].message.meet.type === MeetTypeEnum.ConferenceStart) {
              meetInfo.value.isMeetRunning = true;
              meetInfo.value.lastMeetStartTime = payload.args[0].createTime;
              meetInfo.value.lastMeetId = payload.args[0].message.meet.meetId;
            } else if (payload.args[0].message.meet.type === MeetTypeEnum.ConferenceEnd) {
              meetInfo.value.isMeetRunning = false;
              meetInfo.value.lastMeetStartTime = '';
              meetInfo.value.lastMeetId = '';
            }
          }
        }
        break;
      }
      case OnSignalRMethodEnum.MessageRead: { //args: userId: string, roomId: string, messageId: string
        if (payload.args[1] === props.roomId) {
          // 去RoomData的 userConfigs 找出對應的 UserConfig 去更新 readLastMessageId
          if (roomData.value?.userConfigs[payload.args[0]]) {
            roomData.value.userConfigs[payload.args[0]].readLastMessageId = payload.args[2];
          }
        }
        break;
      }
      case OnSignalRMethodEnum.MessageUnsend: { //args: messageId: string, progressId: string
        const msg = msgDataList.value.find((msg) => msg.messageId === payload.args[0]);
        if (msg) {
          msg.status = MsgStatusEnum.Unsend;
        }
        break;
      }
      case OnSignalRMethodEnum.SyncSelfSentMsg: {
        msgDataList.value.push(payload.args[0]);
        scrollToEnd();
        break;
      }
      case OnSignalRMethodEnum.Test: { //args: msg: any
        console.log('SignalR-OnTestMsg', payload.args);
        break;
      }
      default: {
        console.log('chatHubWorker-signalR Event', e.data);
        break;
      }
    }
  } else {
    console.log('chatHubWorker-notOnSignalR event', e.data);
  }
};

// 發送已讀事件給 ShareWorker 處理
const readMsg = (msgId: string) => {
  chatHubStore.worker.port.postMessage({
    type: WorkerEventEnum.InvokeToSignalR,
    isDesigner: props.isDesigner,
    payload: {
      method: InvokeSignalRMethodEnum.ReadMessage,
      args: [props.roomId, msgId]
    }
  });
};

const showChatRoom = () => {
  isShow.value = true;
  emit('showChatRoom', true);
};

const hideChatRoom = () => {
  isShow.value = false;
  emit('showChatRoom', false);
};

const unsendMsg = async (msgId: string) => {
  let res: UnSendMsgResponse;
  if (props.isDesigner) {
    res = await ChatService.UnSendMsgByDesigner({ roomId: props.roomId, messageId: msgId });
  } else {
    res = await ChatService.UnSendMsgByCustomer({ roomId: props.roomId, messageId: msgId });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    const msg = msgDataList.value.find((msg) => msg.messageId === msgId);
    if (msg) {
      msg.status = MsgStatusEnum.Unsend;
      chatHubStore.worker.port.postMessage({//通知Worker 轉發所有分頁同步收回
        type: WorkerEventEnum.SyncSelfUnsendMsg,
        isDesigner: props.isDesigner,
        payload: {
          args: [msgId]
        }
      });
    }
  } else if (res.status === APIStatusCodeEnum.ServerConnectFail) {//連線失敗 通知Worker重連
      chatHubStore.worker.port.postMessage({
        type: WorkerEventEnum.SignalRConnectionError,
        isDesigner: props.isDesigner,
        payload: {
          args: [res.errInfo, userInfoStore.guestToken]
        }
      });
    } else {
    toastInfo('訊息收回失敗');
  }
};

const saveMsg = (text: string = '', files: FileItem[] = []) => {
  console.log('saveMsg', text, files);
  // 輪詢 files 的 type 來判斷是圖片還是檔案 如果是檔案就挑掉
  // 如果 files 是 null imageUrls 就會維持空陣列
  let imgUrls: string[] = [];
  if (files) {
    imgUrls = files.filter((file) => file.type === MsgFileTypeEnum.Image).map((file) => file.url);
  }
  updateRef.value?.openModal({ text, imageUrls: imgUrls });
};

const sendText = async () => {
  //避免重覆發送
  if (isApiLoading.value) return;
  if (sendMsgText.value) {
    isApiLoading.value = true;
    let res: SendMsgResponse;
    if (props.isDesigner) {
      res = await ChatService.SendMsgByDesigner({
        roomId: props.roomId,
        message: {
          type: MsgSendTypeEnum.Text,
          sticker: '',
          text: sendMsgText.value,
          files: []
        }
      });
    } else {
      res = await ChatService.SendMsgByCustomer({
        roomId: props.roomId,
        message: {
          type: MsgSendTypeEnum.Text,
          sticker: '',
          text: sendMsgText.value,
          files: []
        }
      });
    }

    if (res.status === APIStatusCodeEnum.Success) {
      chatHubStore.worker.port.postMessage({
        type: WorkerEventEnum.SyncSelfSentMsg,
        isDesigner: props.isDesigner,
        payload: {
          args: [res.message]
        }
      });

      sendMsgText.value = '';
    } else if (res.status === APIStatusCodeEnum.ServerConnectFail) {//連線失敗 通知Worker重連
      chatHubStore.worker.port.postMessage({
        type: WorkerEventEnum.SignalRConnectionError,
        isDesigner: props.isDesigner,
        payload: {
          args: [res.errMsg, userInfoStore.guestToken]
        }
      });
    }
    isApiLoading.value = false;
  }
};

const getRoomData = async () => {
  let res: ChatRoomResponse;
  if (props.isDesigner) {
    res = await ChatService.GetOneRoomByDesigner({ roomId: props.roomId });
  } else {
    res = await ChatService.GetOneRoomByCustomer({ roomId: props.roomId });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    roomData.value = res.room;
  }
};

const getRoomInfo = async () => {
  if (!chatContainer.value) return;
  let res: GetChatRoomInfoResponse;
  if (props.isDesigner) {
    res = await ChatService.GetOneRoomInfoByDesigner({ roomId: props.roomId });
  } else {
    res = await ChatService.GetOneRoomInfoByCustomer({ roomId: props.roomId });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    meetInfo.value.isMeetRunning = res.isMeetRunning;
    meetInfo.value.lastMeetStartTime = res.lastMeetStartTime;
    meetInfo.value.lastMeetId = res.lastMeetId;
  }
};

const getRoomMsgData = async () => {
  if (!chatContainer.value) return;
  const previousHeight = chatContainer.value?.scrollHeight;
  let res: GetChatRoomMsgResponse;
  if (props.isDesigner) {
    res = await ChatService.GetOneRoomMsgByDesigner({
      roomId: props.roomId,
      skip: msgSkip.value,
      limit: 20
    });
  } else {
    res = await ChatService.GetOneRoomMsgByCustomer({
      roomId: props.roomId,
      skip: msgSkip.value,
      limit: 20
    });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    msgSkip.value += 20;
    msgDataList.value.unshift(...res.messages);
    await nextTick();
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight - previousHeight;
  }
};

const updateMsg = (msg: MessageData) => {
  msgDataList.value.push(msg);
  // 讓視窗畫面到最底
  scrollToEnd();
};

// 當聊天室滑到頂時再叫資料
const onScroll = async () => {
  if (chatContainer.value) {
    if (chatContainer.value.scrollTop === 0) {
      await getRoomMsgData();
    }
  }
};

const scrollToEnd = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    }
  });
};

const hasCrossDay = (index: number) => {
  // 這邊要拿sortMsgList去找 不然順序會錯
  if (index === 0) return true;
  const current = new Date(sortMsgList.value[index].createTime);
  const previous = new Date(sortMsgList.value[index - 1].createTime);
  // 使用 toLocaleDateString 方法根據當地時區格式化日期

  const currentDateString = current.toLocaleDateString('zh-TW');
  const previousDateString = previous.toLocaleDateString('zh-TW');
  return currentDateString !== previousDateString;
};

const canUnsend = (time: string) => {
  const createTime = new Date(time);
  const now = new Date();
  const diff = now.getTime() - createTime.getTime();
  //dev超過5分鐘就不能收回 正式版超過24小時不能收回
  if (IsProduction) {
    return diff < 60 * 60 * 60 * 1000;
  } else {
    return diff < 5 * 60 * 1000;
  }
};

const openImageModal = (url: string) => {
  imageModalRef.value?.openModal(url);
};

const getInMeet = () => {
  // 路由在meeting就不要再跳轉
  if (route.path.includes('meeting')) {
    toastInfo('您已在會議內');
  } else {
    createMeet(props.roomId, props.isDesigner, userInfoStore.userId);
  }
};

const showMsgReaded = (msg: MessageData) => {
  // 去RoomData的 userConfigs 找出對應的 UserConfig.readLastMessageId
  // 然後根據這個判斷在此訊息之前的訊息都顯示已讀
  // 發送該筆訊息的userId要跳過計算已讀
  let readCount = 0;

  for (const userId in roomData.value?.userConfigs) {
    const userConfig = roomData.value?.userConfigs[userId];
    // 跳過發送該訊息的userId
    if (userId === msg.userId) {
      continue;
    }

    // 如果此用戶的 readLastMessageId 等於或在 msg.messageId 之前，則計算已讀
    const readLastMessageIndex = sortMsgList.value.findIndex(
      (m) => m.messageId === userConfig.readLastMessageId
    );
    const msgIndex = sortMsgList.value.findIndex((m) => m.messageId === msg.messageId);

    if (readLastMessageIndex >= msgIndex) {
      readCount++;
    }
  }

  if (readCount > 0) {
    return `已讀${readCount}`;
  } else {
    return '';
  }
};

const openNote = () => {
  noteBookRef.value?.openModal();
};

const intervalId = ref<number | null>(null);
const formattedMeetingDuration = ref('');

const startMeetingTimer = () => {
  const startTime = new Date(meetInfo.value.lastMeetStartTime).getTime();

  intervalId.value = setInterval(() => {
    const now = Date.now();
    const duration = Math.floor((now - startTime) / 1000);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    formattedMeetingDuration.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, 1000) as unknown as number;
};

const stopMeetingTimer = () => {
  if (intervalId.value !== null) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  formattedMeetingDuration.value = '';
};

// 更新"ConferenceStart"的MeetEndTime
const updateConferenceStartMsgMeetEndTime = () => {
  sortMsgList.value.forEach((everyMsg) => {
    if (everyMsg.message.type === MsgTypeEnum.Meet && everyMsg.message.meet?.type === MeetTypeEnum.ConferenceEnd) {
      const thisMeetSatrtMsgIndex = sortMsgList.value.findIndex((msg) => msg.message.meet?.meetId === everyMsg.message.meet.meetId
        && msg.message.meet?.type === MeetTypeEnum.ConferenceStart);
      if (thisMeetSatrtMsgIndex !== -1) sortMsgList.value[thisMeetSatrtMsgIndex].message.meet.endTime = everyMsg.message.meet.endTime;
    }
  });
};

// 處理已讀訊息操作
const processReadMsg = () => {
  if (sortMsgList.value.length > 0) {
    // 聊天室關的就不已讀
    if (!isShow.value) return;

    // 找出最新一則別人發送的訊息
    const lastestOtherMsgIndex = sortMsgList.value.slice().reverse().findIndex((msg) => msg.userId !== userInfoStore.userId);
    console.log('最新一則別人的訊息', lastestOtherMsgIndex);
    if (lastestOtherMsgIndex === -1) return;
    // 如果還沒已讀才已讀
    if (sortMsgList.value.slice().reverse()[lastestOtherMsgIndex].messageId !== roomData.value?.userConfigs[userInfoStore.userId]?.readLastMessageId) {
      console.log('還沒讀的訊息', lastestOtherMsgIndex);
      readMsg(sortMsgList.value.slice().reverse()[lastestOtherMsgIndex].messageId);
      scrollToEnd();
    }
  }
};

const sortMsgList = computed(() => {
  return msgDataList.value.slice().sort((a, b) => {
    return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
  });
});

// 監聽開啟聊天室時要將沒已讀的訊息都已讀
watch(() => isShow.value, (newVal) => {
  if (newVal) {
    processReadMsg();
  }
});

watch(() => meetInfo.value.isMeetRunning, (newVal) => {
  if (newVal) {
    startMeetingTimer();
  } else {
    stopMeetingTimer();
  }
});

// 監聽會議訊息的資料
watch(() => sortMsgList.value.length, () => {
  processReadMsg();
  updateConferenceStartMsgMeetEndTime();
});

onMounted(async () => {
  if (props.roomId) {
    await getRoomData();
    await getRoomMsgData();
    await getRoomInfo();
    if (chatContainer.value) {
      // 將滾動條設置到最底部
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    }
  } else {
    console.error('沒有RoomId');
  }
});

onUnmounted(() => {
  stopMeetingTimer();
});

defineExpose({ showChatRoom });
</script>

<template>
  <div v-show="!isShow"
       :class='[!isForMeet && meetInfo.isMeetRunning? "bg-amber-300 hover:bg-amber-400" :
       "bg-gray-200 hover:bg-gray-300","w-full rounded-t-xl border border-black cursor-pointer"]'
       @click="showChatRoom">
    <div class="px-2 py-4 flex w-full justify-between items-center ">
      <p class="font-bold">{{ roomData?.roomName }} ({{ roomData?.userIds.length }})
        <span v-if="!isForMeet && meetInfo.isMeetRunning">會議進行中</span></p>
    </div>
  </div>

  <div v-show="isShow" class="w-full h-full flex flex-col rounded-t-xl border border-black bg-white">
    <!--    上方控制區塊-->
    <div class="px-1 flex w-full justify-between items-center bg-gray-200 rounded-t-xl shadow">
      <p class="font-bold">{{ roomData?.roomName }} ({{ roomData?.userIds.length }})</p>
      <div class="py-2 px-1 flex gap-x-2 items-center justify-end">
        <button class="bg-color-button rounded-xl text-xl button-basic text-black items-center hover:bg-color-selected"
                @click="openNote()"
                v-if="isForMeet">
          <span class="font-bold">紀錄本</span>
        </button>
        <img v-if="!isForMeet" src="/vectors/general/close.svg" alt=""
             @click="hideChatRoom" class="w-8 h-8 cursor-pointer">
      </div>
    </div>
    <!--    進行中會議正數顯示區塊-->
    <div v-if="!isForMeet && meetInfo.isMeetRunning"
         class="px-1 flex w-full justify-between items-center bg-gray-300 cursor-pointer"
         @click="getInMeet">
      <div class="flex justify-start items-center gap-x-2">
        <img src="/vectors/contact/meet.svg" alt="" class="w-7 h-7">
        <p class="w-full py-1 flex items-center justify-start text-red-600">會議進行中
          <span class="break-all ml-1">{{ formattedMeetingDuration }}</span>
        </p>
      </div>

      <div class="my-1 py-2 px-4 bg-amber-300 rounded-lg">
        <p class="font-bold">加入會議</p>
      </div>
    </div>
    <div class="w-full h-full p-2 bg-white overflow-y-auto" @scroll="onScroll" ref="chatContainer">
      <!--      訊息區塊-->
      <div v-for="(msg,index) in sortMsgList" :key="msg.messageId"
           class="w-full flex flex-col gap-y-1 justify-center mt-2">
        <!--          檢查時間是否換日-->
        <div class="flex justify-center">
          <p v-if="hasCrossDay(index)" class="py-1 px-2 text-center text-sm rounded-xl bg-gray-100">
            {{ formatMonthDateWithDay(msg.createTime) }}</p>
        </div>
        <!--          檢查是自己的訊息還是別人的-->
        <div>
          <!--            自己的訊息-->
          <div v-if="msg.userId === userInfoStore.userId">
            <!--              收回-->
            <div v-if="msg.status === MsgStatusEnum.Unsend" class="flex justify-center">
              <p class="p-2 rounded-xl text-sm bg-gray-100">您已收回訊息</p>
            </div>
            <!--              沒收回-->
            <div v-else>
              <!--                自己的訊息靠右-->
              <div class="flex justify-end items-end gap-x-2">
                <!--                  選項-->
                <div>
                  <Menu as="div" class="relative flex-none"
                        v-if="canUnsend(msg.createTime) || msg.message.type === MsgTypeEnum.Text || msg.message.type === MsgTypeEnum.FilesAndText">
                    <MenuButton class="block text-gray-500 hover:text-gray-900">
                      <span class="sr-only">Open options</span>
                      <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
                    </MenuButton>
                    <transition enter-active-class="transition ease-out duration-100"
                                enter-from-class="transform opacity-0 scale-95"
                                enter-to-class="transform opacity-100 scale-100"
                                leave-active-class="transition ease-in duration-75"
                                leave-from-class="transform opacity-100 scale-100"
                                leave-to-class="transform opacity-0 scale-95">
                      <MenuItems
                        class="absolute left-0 z-10 bottom-full -mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                        <template v-if="canUnsend(msg.createTime)">
                          <MenuItem v-slot="{ active }">
                            <a
                              :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                              @click="unsendMsg(msg.messageId)"
                            >收回訊息</a>
                          </MenuItem>
                        </template>
                        <template
                          v-if="msg.message.type === MsgTypeEnum.Text || msg.message.type === MsgTypeEnum.FilesAndText">
                          <MenuItem
                            v-slot="{ active }">
                            <a
                              :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                              @click="saveMsg(msg.message.text, msg.message.files)"
                            >儲存訊息</a>
                          </MenuItem>
                        </template>
                      </MenuItems>
                    </transition>
                  </Menu>
                </div>
                <!--                  已讀&時間戳-->
                <div class="flex flex-col items-end">
                  <p class="text-xs text-gray-500">{{ showMsgReaded(msg) }}</p>
                  <p class="text-xs text-gray-500">{{ formatMonthDateTime(msg.createTime) }}</p>
                </div>
                <!--                  訊息本體-->
                <div class="max-w-32">
                  <div v-if="msg.message.type === MsgTypeEnum.Text">
                    <p v-if="isMsgLink(msg.message.text)" @click="openNewTab(msg.message.text)"
                       class="p-2 bg-gray-200 break-all cursor-pointer text-blue-600 rounded-xl">
                      {{ msg.message.text }}</p>
                    <p v-else class="p-2 bg-gray-200 break-all rounded-xl">{{ msg.message.text }}</p>
                  </div>
                  <div v-else-if="msg.message.type === MsgTypeEnum.Meet">
                    <div
                      class="p-2 flex flex-col items-center text-center gap-x-2 bg-gray-200 rounded-xl cursor-pointer"
                      @click="getInMeet">
                      <img src="/vectors/contact/meet.svg" alt="" class="w-10 h-10">
                      <p>會議已<span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">開始</span>
                        <span v-else-if="msg.message.meet.type === MeetTypeEnum.ConferenceEnd">結束</span>
                      </p>

                      <p v-if="msg.message.meet.meetId === meetInfo.lastMeetId && meetInfo.isMeetRunning">
                          <span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">
                            進行中{{ formattedMeetingDuration }}
                          </span>
                      </p>

                      <p v-else>
                          <span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">
                            總時長{{ formatDuration(msg.message.meet.startTime, msg.message.meet.endTime) }}
                          </span>
                        <span v-else-if="msg.message.meet.type === MeetTypeEnum.ConferenceEnd">
                            總時長{{ formatDuration(msg.message.meet.startTime, msg.message.meet.endTime) }}
                          </span>
                      </p>
                    </div>
                  </div>
                  <div v-else-if="msg.message.type === MsgTypeEnum.FilesAndText">
                    <div class="flex flex-col gap-y-2">
                      <div v-if="msg.message.text" class="w-full flex justify-end">
                        <p v-if="isMsgLink(msg.message.text)" @click="openNewTab(msg.message.text)"
                           class="p-2 bg-gray-200 break-all cursor-pointer text-blue-600 rounded-xl">
                          {{ msg.message.text }}</p>
                        <p v-else class="p-2 bg-gray-200 break-all rounded-xl">{{ msg.message.text }}</p>
                      </div>
                      <div class="flex flex-col gap-y-1">
                        <div v-for="(file,index) in msg.message.files" :key="index"
                             class="mr-2 cursor-pointer flex justify-end"
                             @click="openImageModal(file.url)">
                          <img v-if="file.type === MsgFileTypeEnum.Image" :src="file.url" alt=""
                               class="w-32 h-32 rounded object-contain">
                        </div>
                      </div>
                      <div class="flex flex-col gap-y-1">
                        <div v-for="(file,index) in msg.message.files" :key="index" class="bg-gray-200 rounded-xl">
                          <div v-if="file.type === MsgFileTypeEnum.File"
                               class="p-2 items-center flex flex-col cursor-pointer"
                               @click="downloadFile(file.url)">
                            <img src="/vectors/chatRoom/file_icon.svg" alt="" class="w-10 h-10">
                            <p class="truncate text-center text-xs w-full">
                              {{ file.name }}.{{ getFileExtension(file.url) }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--別人的訊息-->
          <div v-else>
            <!--              收回-->
            <div v-if="msg.status === MsgStatusEnum.Unsend" class="flex justify-center">
              <p class="p-2 rounded-xl text-sm bg-gray-100">對方已收回訊息</p>
            </div>
            <!--              沒收回-->
            <div v-else>
              <!--                對方的訊息靠左-->
              <div class="flex justify-start gap-x-2">
                <img v-if="roomData?.userConfigs[msg.userId]?.avatarUrl"
                     :src="roomData?.userConfigs[msg.userId]?.avatarUrl" alt="" class="w-10 h-10 rounded-full">
                <UserCircleIcon v-else class="w-10 h-10 rounded-full" />
                <div class="flex flex-col">
                  <p v-if="roomData?.userConfigs[msg.userId]?.username">{{ roomData?.userConfigs[msg.userId]?.username
                    }}</p>
                  <p v-else>找不到使用者</p>
                  <div class="flex items-end justify-start gap-x-2">
                    <!--                  訊息本體-->
                    <div class="max-w-32">
                      <div v-if="msg.message.type === MsgTypeEnum.Text">
                        <p v-if="isMsgLink(msg.message.text)" @click="openNewTab(msg.message.text)"
                           class="p-2 bg-gray-200 break-all cursor-pointer text-blue-600 rounded-xl">
                          {{ msg.message.text }}</p>
                        <p v-else class="p-2 bg-gray-200 break-all rounded-xl">{{ msg.message.text }}</p>
                      </div>
                      <div v-else-if="msg.message.type === MsgTypeEnum.Meet">
                        <div
                          class="p-2 flex flex-col items-center text-center gap-x-2 bg-gray-200 rounded-xl cursor-pointer"
                          @click="getInMeet">
                          <img src="/vectors/contact/meet.svg" alt="" class="w-10 h-10">
                          <p>會議已<span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">開始</span>
                            <span v-else-if="msg.message.meet.type === MeetTypeEnum.ConferenceEnd">結束</span>
                          </p>

                          <p v-if="msg.message.meet.meetId === meetInfo.lastMeetId && meetInfo.isMeetRunning">
                          <span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">
                            進行中{{ formattedMeetingDuration }}
                          </span>
                          </p>

                          <p v-else>
                          <span v-if="msg.message.meet.type === MeetTypeEnum.ConferenceStart">
                            總時長{{ formatDuration(msg.message.meet.startTime, msg.message.meet.endTime) }}
                          </span>
                            <span v-else-if="msg.message.meet.type === MeetTypeEnum.ConferenceEnd">
                            總時長{{ formatDuration(msg.message.meet.startTime, msg.message.meet.endTime) }}
                          </span>
                          </p>
                        </div>
                      </div>
                      <div v-else-if="msg.message.type === MsgTypeEnum.FilesAndText">
                        <div class="flex flex-col gap-y-2">
                          <p v-if="isMsgLink(msg.message.text)" @click="openNewTab(msg.message.text)"
                             class="p-2 bg-gray-200 break-all cursor-pointer text-blue-600 rounded-xl">
                            {{ msg.message.text }}</p>
                          <p v-else-if="msg.message.text" class="p-2 bg-gray-200 w-fit rounded-xl">
                            {{ msg.message.text }}</p>
                          <div class="flex flex-col gap-y-1">
                            <div v-for="(file,index) in msg.message.files" :key="index"
                                 @click="openImageModal(file.url)" class="cursor-pointer">
                              <img v-if="file.type === MsgFileTypeEnum.Image" :src="file.url" alt=""
                                   class="w-32 h-32 rounded object-contain">
                            </div>
                          </div>
                          <div class="flex flex-col gap-y-1">
                            <div v-for="(file,index) in msg.message.files" :key="index"
                                 class="bg-gray-200 rounded-xl">
                              <div v-if="file.type === MsgFileTypeEnum.File"
                                   class="p-2 items-center flex flex-col cursor-pointer"
                                   @click="downloadFile(file.url)">
                                <img src="/vectors/chatRoom/file_icon.svg" alt="" class="w-10 h-10">
                                <p class="truncate text-center text-xs w-full">
                                  {{ file.name }}.{{ getFileExtension(file.url) }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!--                  已讀&時間戳-->
                    <div class="flex flex-col">
                      <!--                        <p class="text-xs text-gray-500">{{ showMsgReaded(msg)}}</p>-->
                      <p class="text-xs text-gray-500">{{ formatMonthDateTime(msg.createTime) }}</p>
                    </div>
                    <!--                  選項-->
                    <div>
                      <Menu as="div"
                            v-if="msg.message.type === MsgTypeEnum.Text || msg.message.type === MsgTypeEnum.FilesAndText"
                            class="relative flex-none">
                        <MenuButton class="block text-gray-500 hover:text-gray-900">
                          <span class="sr-only">Open options</span>
                          <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
                        </MenuButton>
                        <transition enter-active-class="transition ease-out duration-100"
                                    enter-from-class="transform opacity-0 scale-95"
                                    enter-to-class="transform opacity-100 scale-100"
                                    leave-active-class="transition ease-in duration-75"
                                    leave-from-class="transform opacity-100 scale-100"
                                    leave-to-class="transform opacity-0 scale-95">
                          <MenuItems
                            class="absolute right-0 z-10 bottom-full -mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                            <MenuItem
                              v-slot="{ active }">
                              <a
                                :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                                @click="saveMsg(msg.message.text, msg.message.files)"
                              >儲存訊息</a>
                            </MenuItem>
                          </MenuItems>
                        </transition>
                      </Menu>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--      輸入區塊-->
    <div class="w-full h-1/6 p-2 items-center flex gap-x-2 max-md:p-2 bg-gray-100">
      <!--        圖片上傳器-->
      <div class="w-2/12">
        <ImageUpload :room-id="roomId" @update-msg="updateMsg" :is-designer="isDesigner" />
      </div>

      <!--        檔案上傳器-->
      <div class="w-2/12">
        <FileUpload :room-id="roomId" @update-msg="updateMsg" :is-designer="isDesigner" />
      </div>
      <!--        文字送出器-->
      <div class="w-8/12">
        <input type="text" v-model="sendMsgText"
               @keyup.enter="sendText"
               class="bg-gray-200 p-2 rounded-xl w-full">
      </div>
      <div class="w-2/12">
        <img src="/vectors/chatRoom/send.svg" alt="" class="w-full cursor-pointer rounded-md hover:bg-gray-200"
             @click="sendText">
      </div>
    </div>
  </div>

  <!--  紀錄本對話框預留區-->
  <NoteBook :room-id="roomId" ref="noteBookRef" :is-designer="isDesigner" />
  <!--  紀錄本新增'記事'對話框預留區-->
  <NoteUpdate :room-id="roomId" :is-add-from-chat="true" @note-result="toastInfo('新增成功')" :is-designer="isDesigner"
              ref="updateRef" />
  <ImageModal ref="imageModalRef" />
</template>
