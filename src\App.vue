<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, RouterView } from 'vue-router';
import CustomerLayout from '@/layouts/CustomerLayout.vue';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import { WorkerEventEnum } from '@/model/enum/sharedWorkerEnum.ts';

const designerInfoStore = useDesignerInfoStore();
const customerInfoStore = useCustomerInfoStore();
const chatHubStore = useChatHubStore();

const route = useRoute();
const isRouterAlive = ref<boolean>(true);

/**
 * 因為安卓行動端不支援 SharedWorker，所以這邊要做判斷，如果不支援就不要使用 SharedWorker
 */
let chatHubWorker: SharedWorker | null = null;
if (typeof SharedWorker !== 'undefined') {
  chatHubWorker = new SharedWorker(//建立Worker實例 此時就會建立signalR第一層連線
    new URL('@/workers/chatHubWorker.ts', import.meta.url),
    { type: 'module' }
  );

  chatHubStore.setWorker(chatHubWorker);

  window.onbeforeunload = () => {
    chatHubWorker!.port.postMessage({ type: 'windowUnload' });
  };
}
/**
 * 每次新增、刷新頁面、登入、登出都會觸發
 * 如果已登入會執行InvokeConnectToSignalR 進行第二層連線
 * 未登入執行SetSignalRAuthFalse
 */
const invokeDesginerConnectToSignalR = async () => {
  if (!chatHubWorker) return;
  if (designerInfoStore.loginState) {
    chatHubStore.worker.port.postMessage({
      isDesigner: true,
      type: WorkerEventEnum.InvokeConnectToSignalR,
      payload: {
        args: designerInfoStore.guestToken
      }
    });
  } else {
    chatHubStore.worker.port.postMessage({
      isDesigner: true,
      type: WorkerEventEnum.SetSignalRAuthFalse,
      payload: {
        args: {}
      }
    });
  }
};
/**
 * 每次新增、刷新頁面、登入、登出都會觸發
 * 如果已登入會執行InvokeConnectToSignalR 進行第二層連線
 * 未登入執行SetSignalRAuthFalse 
 */
const invokeCustomerConnectToSignalR = async () => {
  if (!chatHubWorker) return;
  if (customerInfoStore.loginState) {
    chatHubStore.worker.port.postMessage({
      isDesigner: false,
      type: WorkerEventEnum.InvokeConnectToSignalR,
      payload: {
        args: customerInfoStore.guestToken
      }
    });
  } else {
    chatHubStore.worker.port.postMessage({
      isDesigner: false,
      type: WorkerEventEnum.SetSignalRAuthFalse,
      payload: {
        args: {}
      }
    });
  }
};

designerInfoStore.$subscribe(async () => {//登入登出觸發
  console.log('設計師觸發');
  await reload();
  await invokeDesginerConnectToSignalR();
});

customerInfoStore.$subscribe(async () => {//登入登出觸發
  console.log('客戶端觸發');
  await reload();
  await invokeCustomerConnectToSignalR();
});

window.addEventListener('storage', (event) => {
  if (event.key === 'needToBeRefresh') {
    const routesToRefresh = JSON.parse(event.newValue || '[]');
    if (routesToRefresh.some((e: string) => route.path.startsWith(`/${e}`))) {
      console.log('全局方法觸發：刷新');
      console.log(routesToRefresh);
      localStorage.removeItem('needToBeRefresh'); // 刪除物件
      window.location.reload();
    }
  }
});

const reload = async () => {//通過v-if直接重新掛載組件 會觸發組件onMounted 
  isRouterAlive.value = false;
  await nextTick();
  isRouterAlive.value = true;
};

document.addEventListener('resume', () => {
  console.log('resume了');
  // window.location.reload();
});

const layout = computed(() => {
  return route.meta.layout || CustomerLayout;
});

onMounted(async () => {//新增、刷新觸發
  await invokeDesginerConnectToSignalR();
  await invokeCustomerConnectToSignalR();
});

</script>

<template>
  <component :is="layout">
    <RouterView v-if="isRouterAlive" />
  </component>
</template>
