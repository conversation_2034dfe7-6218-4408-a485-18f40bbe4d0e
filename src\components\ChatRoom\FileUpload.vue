<script setup lang="ts">
import { ref } from 'vue';
import { FileItem } from '@/model/request/chatRequest.ts';
import { ChatService } from '@/api/chat.ts';
import { MsgFileTypeEnum, MsgSendTypeEnum } from '@/model/enum/chatEnum.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { toastError } from '@/utils/toastification.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { SendMsgResponse } from '@/model/response/chatResponse.ts';

const sendMsgFileItem = ref<FileItem[]>([]);
const fileInput = ref<HTMLInputElement | null>(null);

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);

const emit = defineEmits(['update-msg']);

const triggerFileInput = () => {
  fileInput.value?.click(); // 因為實現點擊div觸發上傳圖片，觸發 input 的 fileUpload
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.File)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass){
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    //將 files 的每個file.name的副檔名去掉
    S3Urls.forEach((url, index) => {
      const fileName = files[index].name.split('.').slice(0, -1).join('.');
      sendMsgFileItem.value.push({ url: url, name: fileName, type: MsgFileTypeEnum.File });
    });
    await sendFile();
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const sendFile = async () => {
  let res: SendMsgResponse;
  if (props.isDesigner) {
    res = await ChatService.SendMsgByDesigner({
      roomId: props.roomId,
      message: {
        type: MsgSendTypeEnum.FilesAndText,
        sticker: '',
        text: '',
        files: sendMsgFileItem.value
      }
    });
  } else {
    res = await ChatService.SendMsgByCustomer({
      roomId: props.roomId,
      message: {
        type: MsgSendTypeEnum.FilesAndText,
        sticker: '',
        text: '',
        files: sendMsgFileItem.value
      }
    });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    sendMsgFileItem.value = [];
    emit('update-msg', res.message);
  }
};

</script>

<template>
  <input type="file" ref="fileInput" multiple required
         @change="handleFileChange" class="hidden">
  <img src="/vectors/chatRoom/attach_file.svg" alt="" class="w-full cursor-pointer rounded-md hover:bg-gray-200" @click="triggerFileInput">
</template>
