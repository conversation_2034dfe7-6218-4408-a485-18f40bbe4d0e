<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { MeasureContentKeyItem, TextKeyContent, TextKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { JSONStringToObject, ObjectToJSONString } from '@/utils/JsonStringFormat.ts';
import { TextModeEnum } from '@/model/enum/JsonTextModeEnum.ts';
import { toastInfo, toastWarning } from '@/utils/toastification.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { MeasureOrderDetailService } from '@/api/designerOrder.ts';
import { formatDecimal } from '@/utils/numberFormat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { getSelectWithTextOptions } from '@/model/general/selectWithTextOptions.ts';

const textContent = defineModel<TextKeyContent>({ required: true });
const props = defineProps<{ 
orderId: string; 
contentName: string;
canNotDeleteCount: number;//從前往後數 有多少欄位不可刪除
canAddUnit: boolean;//新增欄位是否可以加單位
}>();
//const selectedHouseType = ref<string>('');
const addItemModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const addItemModalWithUnitRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const editItemTitleModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const addItemTitle = ref<string>('');
const addItemUnit = ref<string>('');
const editItemTitleData = ref<{ key: string, title: string }>({ key: '', title: '' });
const isAPILoading = ref<boolean>(false);
// const emit = defineEmits(['closeModal']); //保留儲存按鈕離開功能
const temp = reactive<{ [key: string]: { name: string, text: string } }>({});

const openAddItemModal = (withUnit: boolean) => {
  if(withUnit){
    addItemModalWithUnitRef.value?.openModal();
  } else {
    addItemModalRef.value?.openModal();
  }
};
const closeAddItemModal = () => {
  addItemTitle.value = '';
  addItemUnit.value = '';
  addItemModalRef.value?.closeModal();
  addItemModalWithUnitRef.value?.closeModal();
};
const openEditTitleModal = (key: string, title: string) => {
  editItemTitleData.value = { key, title };
  editItemTitleModalRef.value?.openModal();
};
const closeEditTitleModal = () => {
  editItemTitleModalRef.value?.closeModal();
  editItemTitleData.value = { key: '', title: '' };
};

const addItem = async (withUnit: boolean) => {//新增欄位
  const contentName = props.contentName as keyof MeasureContentKeyItem;
  if (isAPILoading.value === false) {
    if (addItemTitle.value === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    var result = await MeasureOrderDetailService.addOtherMeasureDataItem({
      orderId: props.orderId,
      names: [addItemTitle.value]
    }, contentName);
    if (withUnit && addItemUnit.value !== "") {
      result = await MeasureOrderDetailService.updateSetUnit({
        orderId: props.orderId,
        key: "",
        setUnit: addItemUnit.value,
      }, contentName);
    }
    if (result.status === APIStatusCodeEnum.Success && isTextKeyContent(result.content[contentName])) {
      textContent.value = result.content[contentName] as TextKeyContent;
      closeAddItemModal();
    }
    isAPILoading.value = false;
  }
};

const editItemTitle = async () => {//編輯欄位標題
  const contentName = props.contentName as keyof MeasureContentKeyItem;
  if (isAPILoading.value === false) {
    if (editItemTitleData.value.title === '') {
      toastWarning('請輸入項目名稱');
      return;
    }
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.updateTextContent({
      orderId: props.orderId,
      builders: [{
        key: editItemTitleData.value.key,
        setName: editItemTitleData.value.title,
        setText: ''
      }]
    }, contentName);
    if (result.status === APIStatusCodeEnum.Success && isTextKeyContent(result.content[contentName])) {
      // 成功後更新
      textContent.value = result.content[contentName] as TextKeyContent;
      closeEditTitleModal();
    }
    isAPILoading.value = false;
  }
};

const deleteItem = async (key: string) => {//刪除欄位
  const contentName = props.contentName as keyof MeasureContentKeyItem;
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.deleteItem({
      orderId: props.orderId,
      keys: [key]
    }, contentName);
    if (result.status === APIStatusCodeEnum.Success && isTextKeyContent(result.content[contentName])) {
      // 成功後更新
      textContent.value = result.content[contentName] as TextKeyContent; 
      delete temp[key];//刪除temp中資料 
    } else if (result.status === APIStatusCodeEnum.OrderAlreadyAcceptedCannotDelete) {
      toastInfo('訂單已完成，無法刪除欄位');
    }
    isAPILoading.value = false;
  }
};

const handleDeleteItem = async (key: string) => {//刪除欄位
  await deleteItem(key);
  switch(props.contentName) {
    case 'noise'://計算噪音評估
      extremeValueCalculate(/^(\d+(\.\d+)?)(dB)?$/, 'dB', '噪音評估', '請輸入噪音評估');
      break;
    case 'humidity'://計算濕度評估
      extremeValueCalculate(/^(\d+(\.\d+)?)(%)?$/, '%', '濕度評估', '請輸入濕度評估');
      break;
    default://正常情況不用特殊處理
      break;
  }
};


const saveData = async () => {//上傳資料
  const contentName = props.contentName as keyof MeasureContentKeyItem;
  if (isAPILoading.value === false) {
    isAPILoading.value = true;
    const result = await MeasureOrderDetailService.updateTextContent({
      orderId: props.orderId,
      builders: Object.keys(temp).map(key => {
        return {
          key,
          setName: temp[key].name,
          setText: temp[key].text
        };
      })
    }, contentName);
    if (result.status === APIStatusCodeEnum.Success && isTextKeyContent(result.content[contentName])) {
       //成功後更新
      textContent.value = result.content[contentName] as TextKeyContent;
      Object.keys(temp).forEach(key => {
        delete temp[key];
      }); // 清空temp
    }
    // emit('closeModal'); //保留儲存按鈕離開功能
  }
  isAPILoading.value = false;
};

const dataUpdate = (data: {
  key: string,
  updateTime: string,
  name: string,
  text: { content: { hint: string, text: string, unit?: string, option?:string }, mode: string }
}, otherText?: string) => {
  // console.log('dataUpdate', data);
  // console.log('tempBefore', temp);
  let JsonText: string;
  //特別處理 selectWithText 資料
  if (data.text.mode === TextModeEnum.selectWithText) {
    if (data.text.content.option !== '其他(請填寫)') { //選擇了某個預設選項
      JsonText = ObjectToJSONString(data.text, data.text.content.option);//更新為選項內容
    } else {
      if (!otherText){//將選項切到"其他(請填寫)" 
        JsonText = ObjectToJSONString(data.text, "");;//更新為空
      }else{//實際填寫
        JsonText = ObjectToJSONString(data.text, otherText);//更新為填入字串
      }
    }
  } else if (data.text.mode === TextModeEnum.textWithUnit) {
    // 如果 content.text 的輸入本來就是空字串就跳過贓髒資料檢查
    if (data.text.content.text === '') {
      JsonText = ObjectToJSONString(data.text);
    } else {
      // 要先檢查 content.text 的輸入 處理資料成乾淨的 浮點數字串格式
      const formattedText = formatDecimal(data.text.content.text);
      JsonText = ObjectToJSONString({
        content: {
          hint: data.text.content.hint,
          text: formattedText,
          unit: data.text.content.unit
        },
        mode: data.text.mode
      });
    }
  } else {
    // 純文字正常做JSON轉換
    JsonText = ObjectToJSONString(data.text);
  }
  // 更新或新增 Temp
  temp[data.key] = { name: data.name, text: JsonText };
  // console.log('tempAFTER', temp);
};

const handleDataUpdate = (data: {
  key: string,
  updateTime: string,
  name: string,
  text: { content: { hint: string, text: string, unit?: string, option?:string }, mode: string }
}, otherText?: string) => {
  dataUpdate(data, otherText);//先更新欄位內容
  switch(props.contentName) {
    case 'noise'://計算噪音評估
      extremeValueCalculate(/^(\d+(\.\d+)?)(dB)?$/, 'dB', '噪音評估', '請輸入噪音評估');
      break;
    case 'humidity'://計算濕度評估
      extremeValueCalculate(/^(\d+(\.\d+)?)(%)?$/, '%', '濕度評估', '請輸入濕度評估');
      break;
    default://正常情況不用特殊處理
      break;
  }
};

const extremeValueCalculate = (
  pattern: RegExp,//取值的正規表達式
  unit: string,//單位
  name: string,//欄位名稱(中文)
  hint: string,
) => {
  const data = {
    key: '',
    updateTime: new Date().toISOString(),
    name: name,
    text: {
      content: {
        hint: hint,
        text: '',
      },
      mode: 'text'
    }
  };

  data.key = textContentData.value.find(e => e.name === name)?.key || '';
  if(data.key !== ''){//確定目標欄位存在
    const matchedValues = textContentData.value.filter(e => e.name !== name)//取出所有的合法值
                                               .map(e => e.text.content.text.match(pattern))
                                               .filter(match => match !== null)
                                               .map(match => parseFloat(match![1]));
  
    if (matchedValues.length === 0) {//沒有合法值
      data.text.content.text = '';
    } else {//根據最大值和最小值設定格式
      const minValue = Math.min(...matchedValues);
      const maxValue = Math.max(...matchedValues);
      data.text.content.text = minValue === maxValue ? `${minValue}${unit}` : `${minValue}${unit} - ${maxValue}${unit}`;
    }

    dataUpdate(data);
  }
}

function isTextKeyContent(content: any): content is TextKeyContent {//TS要求的型別檢查
  return content && Array.isArray(content.content) && content.content.every((item: any) => 'text' in item);
}

/**
 * 比對Temp如果有值就取代TextContent顯示的值
 */
const compareTextContentWithTemp = computed(() => {
  // console.log('觸發比較');
  const contents = textContent.value.content;//API進來的資料
  return contents.map(content => {
    const output: TextKeyItem = {
      key: content.key,
      updateTime: content.updateTime,
      name: content.name,
      text: content.text
    };
    const item = temp[content.key];
    if (item) {
      if (item.name !== '') output.name = item.name;
      if (item.text !== '') output.text = item.text;
    }
    return output;
  });
});

/**
 * 將 compareTextContentWithTemp 的資料轉換成可顯示的格式
 */
const textContentData = computed(() => {
  let infoData: {
    key: string,
    updateTime: string,
    name: string,
    text: { content: { hint: string, text: string, unit?: string, option?:string}, mode: string }
  }[] = [];
  
  compareTextContentWithTemp.value.forEach((item) => {
    const JsonStringContent = JSONStringToObject(item.text);//反序列化
    const options = getSelectWithTextOptions(item.name);
    let content: {
      hint: string;
      text: string;
      unit?: string;//textWithUnit才有的欄位
      option?: string;//selectWithText才有的欄位 負責存選項
    } = {
      hint: JsonStringContent.content.hint,
      text: JsonStringContent.content.text,
      ...(JsonStringContent.mode === 'textWithUnit' && { unit: JsonStringContent.content.unit })
    };

    if (JsonStringContent.mode === 'selectWithText') {
      if (JsonStringContent.content.text === "") {//初始狀態
        content.option = "其他(請填寫)";
      } else if (options.includes(JsonStringContent.content.text)) {//選擇了預設選項的狀態
        content.option = JsonStringContent.content.text; 
        content.text = "";
      } else {//填寫了其他文字的狀態
        content.option = "其他(請填寫)";
      }
    }

    infoData.push({
      key: item.key,
      updateTime: item.updateTime,
      name: item.name,
      text: {
        content,
        mode: JsonStringContent.mode
      }
    });
  });

  return infoData;
});

defineExpose({ saveData });

</script>

<template>
  <div class="flex flex-col m-3">
    <div class="flex flex-col gap-2 mx-auto">
      <div v-for="data in textContentData" :key="data.key" class="flex justify-start items-center">
        <div v-if="data.text.mode === TextModeEnum.textWithUnit" class="flex max-md:flex-col justify-center max-md:items-start items-center mb-1 gap-3">
          <p>{{ data.name }}</p>
          <div class="flex items-center gap-x-3">
            <input type="text"
                   class="relative block py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 text-sm sm:leading-6"
                   v-model="data.text.content.text"
                   @change="handleDataUpdate(data)"
                   /><!--:placeholder="data.text.content.hint" -->
            <p>{{ data.text.content.unit }}</p>
          </div>

        </div>
        <div v-else-if="data.text.mode === TextModeEnum.selectWithText"
             class="flex max-md:flex-col justify-center items-start mb-1 gap-3">
          <p class="md:mt-2">{{ data.name }}</p>
          <div class="flex flex-col items-start gap-3">
            <select v-model="data.text.content.option" @change="handleDataUpdate(data)" class="max-md:text-sm">
              <option v-for="option in getSelectWithTextOptions(data.name)" :value="option" :key="option" class="max-md:text-sm">
                {{ option }}
              </option>
            </select>
            <input type="text" v-if="data.text.content.option === '其他(請填寫)'"
                   class="relative block py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 text-sm sm:leading-6"
                   v-model="data.text.content.text"
                   @change="handleDataUpdate(data,data.text.content.text)"
                   /><!--:placeholder="data.text.content.hint" -->
          </div>
        </div>
        <div v-else-if="data.text.mode === TextModeEnum.text" class="flex max-md:flex-col justify-center max-md:items-start items-center mb-1 gap-3">
          <template v-if="data.name !== '濕度評估' && data.name !== '噪音評估'">
            <p class="break-all">{{ data.name }}</p>
            <input type="text"
                   class="relative block border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-100 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
                   v-model="data.text.content.text"
                   @change="handleDataUpdate(data)"
                   :disabled="data.name === '濕度評估' || data.name === '噪音評估'"/><!--:placeholder="data.text.content.hint" -->
          </template>
        </div>
        <div v-if="textContentData && textContentData.findIndex(item => item.key === data.key) >= props.canNotDeleteCount"
             class="ml-2">
          <Menu as="div" class="relative flex-none">
            <MenuButton class="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
              <span class="sr-only">Open options</span>
              <EllipsisVerticalIcon class="h-5 w-5" aria-hidden="true" />
            </MenuButton>
            <transition enter-active-class="transition ease-out duration-100"
                        enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                        leave-active-class="transition ease-in duration-75"
                        leave-from-class="transform opacity-100 scale-100"
                        leave-to-class="transform opacity-0 scale-95">
              <MenuItems
                class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                <MenuItem v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="openEditTitleModal(data.key, data.name)"
                  >編輯項目名稱</a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="handleDeleteItem(data.key)"
                  >移除項目</a>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <div
        class="mt-1 md:mt-4 md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 "
                @click="openAddItemModal(props.canAddUnit)">新增項目
        </button>
        <!--      <button type="button"-->
        <!--              class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-2"-->
        <!--              @click="saveData()">儲存資訊-->
        <!--      </button>-->
        <!--      //保留儲存按鈕離開功能-->
      </div>
    </div>
  </div>
  <DefaultModal title="新增項目" :showCloseButton="false" :click-outside-close="false" ref="addItemModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemTitle"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="addItem(false)">確認
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeAddItemModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="新增項目" :showCloseButton="false" :click-outside-close="false" ref="addItemModalWithUnitRef">
    <div class="flex-col m-3 ">
      <div class="flex justify-center items-center mb-3">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemTitle"
               placeholder="請輸入項目名稱" />
      </div>
      <div class="flex justify-center items-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="addItemUnit"
               placeholder="請輸入項目單位(可選)" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="addItem(true)">確認
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeAddItemModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="編輯項目名稱" :showCloseButton="false" :click-outside-close="false" ref="editItemTitleModalRef">
    <div class="flex-col m-3">
      <div class="flex justify-center">
        <input type="text"
               class="relative block w-1/2 border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-black placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-gray-600 text-sm sm:leading-6"
               v-model="editItemTitleData.title"
               placeholder="請輸入項目名稱" />
      </div>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="editItemTitle()">修改
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="closeEditTitleModal()">返回
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
