<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import { ref } from 'vue';

defineProps<{ title: string; content: string; content2?: string; }>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="title" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">{{ content }}</p>
        <p v-if="content2" class="font-bold md:text-lg">{{ content2 }}</p>
        <p class="font-bold md:text-lg">如果要更好的互動體驗</p>
        <p class="font-bold md:text-lg">可以在App端操作</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>
</template>
