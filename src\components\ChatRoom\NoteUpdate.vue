<script setup lang="ts">
import { computed, ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { UploadImageType } from '@/utils/hezParameters.ts';
import Hez<PERSON><PERSON>ider from '@/components/General/HezDivider.vue';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { toastError } from '@/utils/toastification.ts';
import { NoteService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { OneNoteResponse } from '@/model/response/noteResponse.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isAddFromChat: {
      type: Boolean,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const emit = defineEmits(['note-result']);
const noteData = ref<{ text: string, imageUrls: string[] }>({ text: '', imageUrls: [] });
const updateNoteId = ref<string>('');
const noteBookRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isAPILoading = ref(false);
const isEdit = ref(false);
const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.Picture)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass){
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    S3Urls.forEach(url => {
      noteData.value.imageUrls.push(url);
    });
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const removeOneImage = (url: string) => {
  noteData.value.imageUrls = noteData.value.imageUrls.filter(image => image !== url);
};

const openModal = (data?: { text: string, imageUrls: string[] }, noteId?: string) => {
  console.log(data, noteId);
  if (props.isAddFromChat) {
    if (data) {
      noteData.value = data;
    }
  } else {
    if (data !== undefined && noteId !== undefined) {
      noteData.value = data;
      updateNoteId.value = noteId;
      isEdit.value = true;
    } else {
      isEdit.value = false;
    }
  }
  noteBookRef.value?.openModal();
};

const closeModal = () => {
  noteData.value = { text: '', imageUrls: [] };
  noteBookRef.value?.closeModal();
};

const addNote = async () => {
  if (isAPILoading.value === true) return; // API還在跑 不執行動作
  if (noteData.value.text === '') {
    toastError('請輸入記事本內容');
    return;
  }
  isAPILoading.value = true;
  let noteResult: OneNoteResponse;
  if (isEdit.value === true) {
    if (props.isDesigner) {
      noteResult = await NoteService.EditNoteByDesigner({
        roomId: props.roomId,
        noteId: updateNoteId.value,
        text: noteData.value.text,
        medias: noteData.value.imageUrls
      });
    } else {
      noteResult = await NoteService.EditNoteByCustomer({
        roomId: props.roomId,
        noteId: updateNoteId.value,
        text: noteData.value.text,
        medias: noteData.value.imageUrls
      });
    }

  } else {
    if (props.isDesigner) {
      noteResult = await NoteService.AddNoteByDesigner({
        roomId: props.roomId,
        text: noteData.value.text,
        medias: noteData.value.imageUrls
      });
    } else {
      noteResult = await NoteService.AddNoteByCustomer({
        roomId: props.roomId,
        text: noteData.value.text,
        medias: noteData.value.imageUrls
      });
    }

  }
  if (noteResult.status === APIStatusCodeEnum.Success) {
    emit('note-result', noteResult.note);
    closeModal();
  }

  isAPILoading.value = false;
};

const updatedMediaCount = computed(() => {
  if (!noteData.value.imageUrls) return 0;
  return noteData.value.imageUrls.length;
});

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="新增記事本" ref="noteBookRef"
                :click-outside-close="false"
                :showCloseButton="true"
                modal-width="max-w-6xl"
                @closeModal="closeModal">
    <div class="flex-col m-3">
      <!--      //TODO 最後了 加油-->
      <div class="flex ">
        <div class="flex flex-col gap-y-2 items-start">
          <label class="text-base items-start font-semibold sr-only">作品圖片</label>
          <div @click="triggerFileInput"
               class="flex flex-col gap-y-0.5 p-8 max-md:p-4 bg-gray-200 justify-center items-center cursor-pointer">
            <img src="/vectors/general/addImage.svg" alt="addImage" class="w-7 h-7" />
            <p>新增圖片</p>
          </div>
        </div>

        <div class="grow flex flex-col gap-y-2 justify-start text-black">
          <input type="file" ref="fileInput" :accept="UploadImageType" multiple required
                 @change="handleFileChange" class="hidden">
          <div class="flex flex-grow ml-2">
            <label for="about" class="sr-only">workInfo</label>
            <div class="flex flex-grow text-start">
                          <textarea id="description" name="description" rows="4" placeholder="新增描述"
                                    v-model="noteData.text"
                                    class="resize-none input-basic rounded-md input-focus" />
            </div>
          </div>
        </div>
      </div>
      <HezDivider />
      <div class="flex flex-col gap-x-1 text-start">
        <p class="text-base font-semibold text-black">您選擇的照片({{ updatedMediaCount }})</p>
        <div class="flex flex-wrap gap-x-1">
          <div v-for="image in noteData.imageUrls" :key="image" class="relative">
            <img :src="image" alt="image" class="w-24 h-24" v-if="image" />
            <button class="absolute top-0 right-0  w-6 h-6 flex justify-center items-center"
                    @click="removeOneImage(image)">
              <img src="/vectors/general/crossCircle.svg" alt="crossCircle" class="object-cover" />
            </button>
          </div>
        </div>
      </div>

      <div
        class="mt-1 md:mt-6 md:gap-3">
        <button type="button"
                class="cus-btn button-padding w-full"
                @click="addNote">
          <span v-if="!isEdit">新增記事</span>
          <span v-else>修改記事</span>
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
