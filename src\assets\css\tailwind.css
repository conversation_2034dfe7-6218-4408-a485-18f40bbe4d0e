@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes breathing {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
    }
    50% {
        box-shadow: 0 0 10px 10px rgba(255, 0, 0, 0);
    }
}

@keyframes flashing {
    0%, 100% {
        background-color: #F9E19B ;
    }
    50% {
        background-color: #F3EBE4 ;
    }
}

@layer components {
    .breathing-light {
        animation: breathing 2s infinite;
    }

    .flashing-light {
        animation: flashing 2s infinite;
    }

    .cus-border {
        @apply
        p-8
        border-gray-400
        border
        rounded-xl
    }

    .new-bg-color{
        background-color: #424242
    }

    .cus-btn {
        background-color: #424242;
        @apply
        tracking-widest
        text-white
        rounded-xl
        shadow-md
        hover-zoom
        md:py-6 py-4
        disabled:cursor-not-allowed
        disabled:bg-gray-400
        disabled:text-black
    }

    .radio-btn {
        @apply
        border-gray-600
        border-2
        text-gray-600
        focus:ring-gray-600
    }

    .check-btn {
        @apply
        border-gray-600
        border-2
        text-gray-600
        rounded
        focus:ring-transparent
    }

    .check-btn-selected {
        background-color: #424242;
        @apply
        text-white
    }

    .hez-item-bg-color{
        background-color: #F5F4F2;
    }

    .focus-border-color {
        @apply
        focus:ring-gray-600
        focus:border-gray-600
    }

    .flex-center {
        @apply
        flex
        justify-center
        items-center
    }

    .moblie-warp-start {
        @apply
        max-md:flex-wrap
        max-md:justify-start
    }

    .basic-page-padding {
        @apply
        py-6
        sm:px-6
        lg:px-8
        max-sm:px-4
    }

    .button-padding {
        @apply
        py-2
        px-4
    }

    .selected-bg-dark {
        background-color: #ffd559;
    }

    .button-basic {
        @apply
        inline-flex
        justify-center
        rounded-md
        button-padding
        text-sm
        font-semibold
        shadow-sm
    }

    .input-basic {
        @apply
        relative
        block
        w-full
        border-0
        py-1.5
        ring-1
        ring-inset
        ring-gray-300
        placeholder:text-gray-400
    }

    .input-focus {
        @apply
        focus:z-10
        focus:ring-2
        focus:ring-inset
        focus:ring-gray-600
    }

    .hover-zoom {
        @apply
        transition
        ease-in-out
        hover:scale-105
    }

    .hez-component {
        @apply
        gap-4
        mb-4
        basic-page-padding
        border-b
        border-gray-300
        bg-white
        max-md:flex-col
        max-md:flex
    }

    .hez-disable {
        @apply
        disabled:cursor-not-allowed
        disabled:opacity-50
    }

    .border-secondary {
        border-color: #635B4B;
    }

    .bg-color-primary {
        background-color: #FFF;
    }

    .bg-color-secondary {
        background-color: #F3EBE4;
    }

    .bg-color-dark {
        background-color: #635B4B;
    }

    .bg-color-third {
        background-color: #EDEDED;
    }

    .bg-color-selected {
        background-color: #F9E19B;
    }

    .bg-color-button {
        background-color: #FBE9B4;
    }

    .cus-hezStep-color {
        background-color: #F6D575;
    }

    .text-color-primary {
        color: #1B1B1B;
    }

    .text-color-secondary {
        color: #635B4B;
    }

    .text-color-light {
        color: #F9E19B;
    }

    /*.text-en {*/
    /*    @apply tracking-widest;*/
    /*    font-family: 'Roboto', sans-serif;*/
    /*}*/
    .text-ch {
        @apply tracking-widest;
        font-family: 'Noto Sans TC', sans-serif;
    }

    /*ToastInfo套件區*/
    .Vue-Toastification__toast--info.hez {
        @apply
        bg-blue-500
    }

    .Vue-Toastification__toast--success.hez {
        @apply
        bg-green-500
    }

    .Vue-Toastification__toast--warning.hez {
        @apply
        bg-amber-500
    }

    .Vue-Toastification__toast--error.hez {
        @apply
        bg-red-500
    }
}
