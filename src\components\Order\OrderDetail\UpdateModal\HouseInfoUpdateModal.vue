<script setup lang="ts">
import { ref } from 'vue';
import HouseInfoModalContent from '@/components/Order/OrderDetail/UpdateModal/ModalContent/HouseInfoModalContent.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { TextKeyContent } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';

const props = defineProps<{ title: string; orderId: string; }>();
const houseInfo = defineModel<TextKeyContent>({ required: true });
const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const houseInfoContentRef = ref<InstanceType<typeof HouseInfoModalContent> | null>(null);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  updateModalRef.value?.closeModal();
};

const handleDefaultModalClose = async () => {
  await houseInfoContentRef.value?.saveData();
  closeModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" :showCloseButton="true"
                :click-outside-close="false"
                @closeModal="handleDefaultModalClose()" ref="updateModalRef">
    <HouseInfoModalContent v-model="houseInfo" :orderId="props.orderId"
                           ref="houseInfoContentRef" />
  </DefaultModal>
</template>
