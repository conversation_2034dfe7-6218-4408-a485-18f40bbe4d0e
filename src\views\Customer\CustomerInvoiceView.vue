<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import { toastInfo } from '@/utils/toastification.ts';
import { CustomerInvoiceDetailService } from '@/api/customerOrder.ts';
import { InvoiceOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import HezDivider from '@/components/General/HezDivider.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { Invoice } from '@/model/response/customerInvoiceResponse.ts';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { moneyAddCommas } from '@/utils/budgetFormat.ts';
import CustomerInvoiceModal from '@/components/Modal/CustomerInvoiceModal.vue';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

const route = useRoute();
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const invoiceId = route.params.id as string;
const applicationInput = ref({
  taxId: '',
  title: '',
  note: ''
});
const applyInvoiceModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const invoiceIssueModalRef = ref<InstanceType<typeof CustomerInvoiceModal> | null>(null);
const invoiceData = ref<Invoice>({
  invoiceId: '',
  createTime: '',
  refreshTime: '',
  status: InvoiceOrderStatusEnum.NotIssued,
  orderCreateTime: '',
  orderType: OrderTypeEnum.Measure, //本次發票對應的訂單
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  amount: 0,
  orderId: '',
  designerId: '',
  designerName: '',
  designerAvatar: '',
  application: {
    taxId: '',
    title: '',
    note: ''
  },
  issueResults: []
});

const goHome = () => {
  router.push({
    name: 'customerhome'
  });
};

const invoiceDetail = computed(() => {
  return {
    status: invoiceData.value.status,
    invoiceId: invoiceData.value.invoiceId,
    serviceTime: formatFullDateTimeWithDay(invoiceData.value.createTime),
    designerName: invoiceData.value.designerName,
    designerAvatar: invoiceData.value.designerAvatar,
    serviceAmout: moneyAddCommas(invoiceData.value.amount),
    application: invoiceData.value.application,
    orderType: invoiceData.value.orderType,
    issueResults: invoiceData.value.issueResults.map((issueResult) => {
      return {
        fileUrl: issueResult.fileUrl,
        amount: moneyAddCommas(issueResult.amount),
        createTime: formatFullDateTimeWithDay(issueResult.createTime)
      };
    })
  };
});

const getInvoiceData = async () => {
  const response = await CustomerInvoiceDetailService.getOneInvoice({ invoiceId: invoiceId });
  if (response.status !== APIStatusCodeEnum.Success) {
    toastInfo('找不到訂單資料');
    goHome();
  } else {
    invoiceData.value = response.invoice;
  }
};

const checkIusseInvoice = (index: number) => {
  if (invoiceDetail.value.status === InvoiceOrderStatusEnum.Issued) {
    invoiceIssueModalRef.value?.openModal({
      invoiceIndex: index,
      fileUrl: invoiceDetail.value.issueResults[index].fileUrl,
      amount: invoiceDetail.value.issueResults[index].amount,
      createTime: invoiceDetail.value.issueResults[index].createTime
    });
  }
};

const getInvoiceClicked = () => {
  if (invoiceDetail.value.status === InvoiceOrderStatusEnum.NotIssued) {
    applyInvoiceModalRef.value?.openModal();
  }
};

const applyInvoice = async () => {
  const res = await CustomerInvoiceDetailService.CompleteInfo({
    invoiceId: invoiceDetail.value.invoiceId,
    taxId: applicationInput.value.taxId,
    title: applicationInput.value.title,
    note: applicationInput.value.note
  });
  if (res.status === APIStatusCodeEnum.Success) {
    toastInfo('發票申請成功');
    invoiceData.value = res.invoice;
    applyInvoiceModalRef.value?.closeModal();
  }
};

onMounted(async () => {
  if (!customerInfoStore.loginState) {
    goHome();
    return;
  }
  await getInvoiceData();
});
</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <div class="p-4 cus-border text-lg">
      <div class="flex flex-row justify-center ">
        <p class="text-2xl font-bold text-black">發票收據</p>
      </div>
      <div class="flex p-2 gap-x-4 justify-start items-start max-md:flex-col">
        <img v-if="invoiceDetail.designerAvatar" class="w-20 h-20 rounded-full"
             :src="invoiceDetail.designerAvatar" alt="designerAvatar" />
        <UserCircleIcon v-else
                        class="h-24 max-md:h-12 w-24 max-md:w-12 flex-none" />
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class=" font-medium text-nowrap">
              <span v-if="invoiceDetail.orderType === OrderTypeEnum.Measure">丈量師姓名</span>
              <span v-else>設計師姓名</span>
            </p>
            <p class="font-bold">{{ invoiceDetail.designerName }}</p>
          </div>
          <div class="flex gap-3">
            <p class="font-medium text-nowrap">
              <span v-if="invoiceDetail.orderType === OrderTypeEnum.Measure">到府丈量費</span>
              <span v-else-if="invoiceDetail.orderType === OrderTypeEnum.Design">室內設計費</span>
              <span v-else-if="invoiceDetail.orderType === OrderTypeEnum.Construction">裝潢施工費</span>
            </p>
            <p class="font-bold">{{ invoiceDetail.serviceAmout }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="flex cus-border flex-col my-1 gap-4">
      <div class="flex justify-evenly text-2xl items-center">
        <p class="font-bold text-color-primary text-center">發票明細</p>
      </div>
      <HezDivider />
      <div class="flex justify-start">
        <div class="flex flex-col">
          <div class="flex justify-start text-lg  mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">服務日期</p>
              <p class=" font-bold">{{ invoiceDetail.serviceTime }}</p>
            </div>
          </div>
          <div class="flex justify-start text-lg mb-4">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">發票金額</p>
              <p class=" font-bold">{{ invoiceDetail.serviceAmout }}</p>
            </div>
          </div>
          <div class="flex justify-start text-lg mb-4"
               v-if="invoiceDetail.application.taxId">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">統一編號</p>
              <p class=" font-bold">{{ invoiceDetail.application.taxId }}</p>
            </div>
          </div>
          <div class="flex justify-start text-lg mb-4"
               v-if="invoiceDetail.application.title">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">發票抬頭</p>
              <p class=" font-bold">{{ invoiceDetail.application.title }}</p>
            </div>
          </div>
          <div class="flex justify-start text-lg mb-4"
               v-if="invoiceDetail.application.note">
            <div class="flex md:gap-x-2 gap-x-0.5">
              <p class="font-bold text-nowrap">備註細節</p>
              <p class=" font-bold">{{ invoiceDetail.application.note }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-4 cus-border md:py-8 md:px-8 rounded-lg flex items-center justify-between"
         @click="getInvoiceClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          1
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3">
            <p class="font-bold md:text-lg">發票申請</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <p class=" font-bold md:text-lg">
              <span v-if="invoiceDetail.status === InvoiceOrderStatusEnum.NotIssued" class="text-red-600">未開立</span>
              <span v-else-if="invoiceDetail.status === InvoiceOrderStatusEnum.Pending"
                    class="text-blue-600">待開立</span>
              <span v-else class="text-black">已開立</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="cus-border p-4 md:py-8 md:px-8 rounded-lg flex items-center justify-between"
         v-for="(issueResult, index) in invoiceDetail.issueResults"
         :key="index" @click="checkIusseInvoice(index)">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          {{ index + 2 }}
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">已開立發票 (第{{ index + 1 }}張)</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">金額</p>
            <p class=" font-bold md:text-lg text-black">{{ issueResult.amount }}</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">日期</p>
            <p class=" font-bold md:text-lg text-black">{{ issueResult.createTime }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <DefaultModal title="發票申請" :show-close-button="true" :click-outside-close="true"
                ref="applyInvoiceModalRef"
                @close-modal="applyInvoiceModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <div class="flex items-center max-md:flex-col gap-2">
          <p class="font-bold text-nowrap">統一編號</p>
          <input v-model="applicationInput.taxId" placeholder="(可選)請輸入統一編號"
                 type="text" class="border border-gray-500 p-2 rounded w-full">
        </div>
      </div>
      <div class="flex flex-col p-2 items-start">
        <div class="flex items-center max-md:flex-col gap-2">
          <p class="font-bold text-nowrap">發票抬頭</p>
          <input v-model="applicationInput.title" placeholder="(可選)請輸入發票抬頭"
                 type="text" class="border border-gray-500 p-2 rounded w-full">
        </div>
      </div>
      <div class="flex flex-col p-2 items-start">
        <div class="flex items-center max-md:flex-col gap-2">
          <p class="font-bold text-nowrap">備註細節</p>
          <div class="flex flex-grow text-start">
                          <textarea id="note" name="note" rows="3" placeholder="(可選)請輸入備註細節"
                                    v-model="applicationInput.note"
                                    class="resize-none input-basic rounded-md input-focus " />
          </div>
        </div>
      </div>
      <button type="button"
              class="button-padding cus-btn w-1/2"
              @click="applyInvoice()">送出申請
      </button>
    </div>
  </DefaultModal>

  <CustomerInvoiceModal ref="invoiceIssueModalRef" />
</template>
