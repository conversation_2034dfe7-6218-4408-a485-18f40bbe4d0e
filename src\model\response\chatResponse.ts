import { BaseResponse } from '@/model/response/baseResponse.ts';
import {
  ChatRoomOrderStateEnum,
  ChatRoomTypeEnum,
  ChatRoomUserTypeEnum, MeetTypeEnum,
  MsgStatusEnum, MsgTypeEnum, RecordTypeEnum
} from '@/model/enum/chatEnum.ts';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import { FileItem } from '@/model/request/chatRequest.ts';


export interface MeetDialResponse extends BaseResponse {
  meetId: string;
  meetToken: string;
}

export interface MeetAnswerResponse extends BaseResponse {
  meetId: string;
  meetToken: string;
}

export interface ChatRoomResponse extends BaseResponse {
  room: RoomData;
}

export interface GetChatRoomMsgResponse extends BaseResponse {
  messageCount: number;
  messages: MessageData[];
}

export interface GetChatRoomInfoResponse extends BaseResponse {
  isMeetRunning: boolean;
  lastMeetStartTime: string;
  lastMeetId: string;
}

export interface SendMsgResponse extends BaseResponse {
  message: MessageData;
}

export interface UnSendMsgResponse extends BaseResponse {
}

export interface MessageData {
  messageId: string;
  createTime: string;
  refreshTime: string;
  status: MsgStatusEnum;
  roomId: string;
  userId: string;
  message: MessageItem;
}

export interface MessageItem {
  type: MsgTypeEnum;
  sticker: string;
  text: string;
  files: FileItem[];
  meet: MeetItem;
}

interface MeetItem {
  meetId: string;
  startTime: string;
  endTime: string;
  type: MeetTypeEnum;
  recordType: RecordTypeEnum;
}

export interface RoomData {
  roomId: string;
  createTime: string;
  refreshTime: string;
  type: ChatRoomTypeEnum;
  //房主的UserId
  ownerId: string;
  userIds: string[];
  roomName: string;
  roomIcon: string;
  notify: boolean;
  hidden: boolean;
  userConfigs: UserConfigs;
  orderInfo: OrderInfo;
}

export interface OrderInfo {
  createTime: string;
  refreshTime: string;
  orderType: OrderTypeEnum;
  orderState: ChatRoomOrderStateEnum;
  orderId: string;
  customerId: string;
  customerName: string;
  designerId: string;
}

//key: userId
export interface UserConfigs {
  [key: string]: UserConfig;
}

export interface UserConfig {
  userType: ChatRoomUserTypeEnum;
  username: string;
  avatarUrl: string;
  readLastMessageId: string;
}
