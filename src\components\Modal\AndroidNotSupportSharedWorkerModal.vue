<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import { ref } from 'vue';
import DesignerAppDownload from '@/components/General/DesignerAppDownload.vue';

defineProps({
  isDesigner: {
    type: Boolean,
    required: true
  }
});
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal title="您的裝置不支援聊天室" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2">
        <p class="font-bold md:text-lg">因Andriod行動端不支援聊天室功能</p>
        <p class="font-bold md:text-lg">請使用電腦開啟網頁或切換至家易App使用聊天室功能</p>
      </div>
      <DesignerAppDownload v-if="isDesigner"/>
      <CustomerAppDownload v-else />
    </div>
  </DefaultModal>
</template>
