import type { BaseResponse } from './baseResponse.ts';
import { ProfileGenderEnum, ProfileVerifyStatusEnum } from '@/model/enum/profileVerify';
import type { WorkType } from '@/model/general/account';
import { RegionEnum } from '@/model/enum/taiwanRegion.ts';

export interface GetInfoResponse extends BaseResponse {
  result: DesignerInfo;
}

export interface DesignerInfo {
  refreshTime: string;
  userId: string;
  phone: string;
  avatar: {
    updateTime: string;
    url: string;
  };
  taiwanId: {
    updateTime: string;
    verifyStatus: ProfileVerifyStatusEnum;
    username: string;
    gender: ProfileGenderEnum
    idNumber: string;
    frontSideUrl: string;
    backSideUrl: string;
  };
  email: {
    updateTime: string;
    address: string;
    isVerify: boolean;
  };
  company: {
    updateTime: string;
    verifyStatus: ProfileVerifyStatusEnum;
    companyName: string;
    unifiedBusinessNumber: string;
    companyDocumentUrls: string[];
    description: string;
    logo: string
    serviceTime: string
    address: string
  };
  verifyStatus: ProfileVerifyStatusEnum; // 這邊是所有驗證通過的狀態 過了才能接單
  work: {
    updateTime: string;
    type: WorkType;
    region: RegionEnum[];
  };
  website: {
    updateTime: string;
    targetUrl: string; // 客戶自行設定的網址名稱 ex:https://www.homeeasy.app/targetUrl
  };
}

export interface getRedirectDesignerPageResponse extends BaseResponse {
  userId: string;
}
