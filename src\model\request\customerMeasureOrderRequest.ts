export interface CustomerMeasureOrderPublishRequest {
  address: {
    name: string;
    location: {
      lat: number;
      lng: number;
    }
  };
  username: string;
  measureTimes: string[];
}

export interface CustomerMeasureOrderBindRequest {
  orderIds: string[];
}

export interface refillMeasureOrderTimeRequest {
  orderId: string;
  measureTimes: string[];
}

export interface CustomerGetManyOrderDetailRequest {
}

export interface CustomerGetOneOrderDetailRequest {
  orderId: string;
}

export interface CustomerGetOneInvoiceRequest {
  invoiceId: string;
}

export interface CompleteInvoiceInfoRequest {
  invoiceId: string;
  taxId: string;
  title: string;
  note: string;
}

export interface deleteOrderRequest {
  orderId: string;
}
