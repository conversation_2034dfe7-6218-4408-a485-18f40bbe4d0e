import type { BaseResponse } from './baseResponse.ts';

export interface GetGuestTokenResponse extends BaseResponse {
  token: string;
}

export interface MountEventResponse extends BaseResponse {
  isLogin: boolean;
}

export interface SendVerifyCodeResponse extends BaseResponse {
  isRegister: boolean;
}

export interface CheckVerifyCodeResponse extends BaseResponse {
  phoneVerifyId: string; //電話驗證事件ID
}

export interface RegisterAndLoginResponse extends BaseResponse {
  userId: string;
  isResetPassword: boolean;
}

export interface UserLoginResponse extends BaseResponse {
  userId: string;
}

export interface UserLogoutResponse extends BaseResponse {
}

export interface DeleteAccountResponse extends BaseResponse {
}

export interface ResetPasswordResponse extends BaseResponse {
}
