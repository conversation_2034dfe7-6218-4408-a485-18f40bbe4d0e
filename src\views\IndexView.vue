<script setup lang="ts">
import HezStep from '@/components/customer/HezStep.vue';
import { useRouter } from 'vue-router';
import { onMounted } from 'vue';
import { deviceInfo } from '@/utils/userDeviceInfo.ts';
import { SecurityService } from '@/api/security.ts';
import { useCustomerInfoStore, useStep1PublishTitleStore } from '@/stores/customerGlobal.ts';

const router = useRouter();
const titleStore = useStep1PublishTitleStore();
const customerInfoStore = useCustomerInfoStore();

const goToMeasurePublish = () => {
  titleStore.setTitle('', '');
  router.push({ name: 'customermeasurepublish' });
};

onMounted(() => {
  const mountData = {
    deviceVersion: deviceInfo.deviceVersion,
    appVersion: deviceInfo.appVersion,
    fcmToken: deviceInfo.fcmToken
  };
  SecurityService.mountCheckLoginByCustomer(mountData);

  if (customerInfoStore.guestToken === '') {
    console.log('客戶Token空的');
    customerInfoStore.getGuestToken();
  }
});

</script>

<template>
  <!-- Hero Section -->
  <div class="relative bg-cover bg-center cursor-pointer w-full mt-8 rounded-3xl max-md:hidden"
       style="background-image: url('/image/login_beauty_pic.jpg'); height: 350px;"
       @click="goToMeasurePublish()">
    <div class="absolute inset-0 flex items-center justify-center rounded-3xl bg-gray-800 bg-opacity-50">
      <div class="text-center text-white mx-4">
        <h1 class="text-4xl font-bold">家易，室內裝潢施工比價平台</h1>
        <button class="mt-6 px-6 py-2 bg-white font-bold text-black rounded-xl shadow-lg">預約到府丈量服務 →</button>
      </div>
    </div>
  </div>

  <div class="relative bg-cover bg-center cursor-pointer w-full mt-8 rounded-3xl md:hidden"
       style="background-image: url('/image/login_beauty_pic.jpg'); height: 350px;"
       @click="goToMeasurePublish()">
    <div class="absolute inset-0 flex items-center justify-center rounded-3xl bg-gray-800 bg-opacity-50">
      <div class="text-center text-white mx-4">
        <h1 class="text-2xl font-bold">家易，室內裝潢施工比價平台</h1>
        <button class="mt-4 px-6 py-2 bg-white font-bold text-black rounded-xl shadow-lg">預約到府丈量服務 →</button>
      </div>
    </div>
  </div>

  <HezStep />

  <!-- Services Section -->
  <div class="py-8 w-full">
    <h2 class="text-2xl font-bold text-center mb-8">家易提供的服務</h2>
    <div class="border border-gray-400 shadow-md shadow-gray-300 p-4 max-md:w-full rounded-lg mx-auto md:text-lg">
      <div class="flex flex-col justify-start gap-2">
        <div class="flex gap-2">
          <p>1.</p>
          <p>到府空間丈量：專業丈量及房屋健檢服務，提供您專業的室內平面圖和屋況健檢報告，特價2,000元(原價2萬)。</p>
        </div>
        <div class="flex gap-2">
          <p>2.</p>
          <p>
            室內設計比價：透過設計師比價和報價媒合服務，您可以選擇適合您的設計師，為您繪製2D室內設計圖與完工後3D預覽圖。</p>
        </div>
        <div class="flex gap-2">
          <p>3.</p>
          <p>
            裝潢施工細節：裝潢施工分期驗收，確保可以達到您的裝潢品質，如果選擇相同設計師，可享有裝潢施工費用抵免優惠，也可以透過施工團隊報價與比價系統，選擇適合您的施工團隊。</p>
        </div>
        <div class="flex gap-2">
          <p>4.</p>
          <p>發票開立節稅：全程開立發票，提供您專業的節稅服務。</p>
        </div>
      </div>
    </div>
  </div>
</template>
