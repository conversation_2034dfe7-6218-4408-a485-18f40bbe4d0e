<script setup lang="ts">
import { ref } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

const isOpen = ref(false);
const VideoUrl = ref('');

const openModal = (url: string) => {
  isOpen.value = true;
  VideoUrl.value = url;
};

const closeModal = () => {
  isOpen.value = false;
  VideoUrl.value = '';
};

defineExpose({ openModal });
</script>

<template>
  <TransitionRoot as="template" @close="closeModal" :show="isOpen">
    <Dialog as="div" static class="relative z-50">
      <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100"
                       leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-gray-800 bg-opacity-90 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto ">
        <div class="flex min-h-full justify-center p-8 text-center items-center">
          <TransitionChild as="template" enter="ease-out duration-300"
                           enter-from="opacity-0 translate-y-4 md:translate-y-0 md:scale-95"
                           enter-to="opacity-100 translate-y-0 md:scale-100" leave="ease-in duration-200"
                           leave-from="opacity-100 translate-y-0 md:scale-100"
                           leave-to="opacity-0 translate-y-4 md:translate-y-0 md:scale-95">
            <DialogPanel
              class="relative transform overflow-hidden rounded-lg bg-transparent transition-all flex items-center justify-center">
              <div class="relative flex flex-col items-center justify-center w-full h-full">
                <DialogTitle as="div" class="absolute top-0 right-0 m-4">
<!--                  <button @click="closeModal" class="bg-gray-200 bg-opacity-50 z-50">-->
<!--                    <img src="/vectors/general/close.svg" alt="star" class=" w-10 h-10" />-->
<!--                  </button>-->
                </DialogTitle>
                <video :src="VideoUrl" autoplay controls class="max-w-full lg:max-h-[800px] max-h-[600px] object-contain" />
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
