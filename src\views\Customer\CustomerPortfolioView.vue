<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { UserCircleIcon } from '@heroicons/vue/24/outline';
import { portfolioDataFormat } from '@/utils/portfolioFormat.ts';
import { PortfolioService } from '@/api/portfolio.ts';
import { PortfolioItem } from '@/model/response/portfolioResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { IntroductionFormated } from '@/model/formatted/portolio.ts';
import SatisfactionModal from '@/components/Portfolio/SatisfactionModal.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

const route = useRoute();
const isUserFound = ref<boolean>(true);
const modalSatisfactionRef = ref<InstanceType<typeof SatisfactionModal> | null>(null);
const portfolioData = ref<{
  introduction: IntroductionFormated,
  portfolios: PortfolioItem[],
}>();
const modalTextAndAvatar = ref<{
  avatar: string;
  text: string;
}>({ avatar: '', text: '' });
const router = useRouter();
const designerId = ref<string>(route.params.designerId as string);
const directToStep1Modal = ref<InstanceType<typeof DefaultModal> | null>(null);

const openSatisfactionModal = () => {
  modalSatisfactionRef.value?.openSatisfactionModal();
};

const openDirectToStep1Modal = (avatar: string | undefined, text: string) => {
  if (avatar === undefined) return;
  modalTextAndAvatar.value = { avatar: avatar, text: text };
  directToStep1Modal.value?.openModal();
};

const closeDirectToStep1Modal = () => {
  goToAskService();
  directToStep1Modal.value?.closeModal();
};

const goToAskService = () => {
  router.push({
    name: 'askservice'
  });
};

const goToMeasurePublish = () => {
  router.push({ name: 'customermeasurepublish' });
};

const getPortfolioData = async () => {
  designerId.value = route.params.designerId as string;
  const responseData = await PortfolioService.getSingleDesignerPageByCustomer({ designerId: designerId.value });
  if (responseData.status === APIStatusCodeEnum.DataOrFormatError || responseData.status === APIStatusCodeEnum.UserNotFound) {
    isUserFound.value = false;
    return;
  } else {
    isUserFound.value = true;
  }
  portfolioData.value = portfolioDataFormat(responseData);
};

onMounted(async () => {
  await getPortfolioData();
});
watch(() => route.params, async () => {
  await getPortfolioData();
});
</script>

<template>
  <div class="w-full">
    <div v-if="!isUserFound" class="max-md:p-4 md:my-12 text-center cus-border">
      <p class="text-lg font-bold">查無設計師</p>
    </div>

    <div v-else class="flex flex-col space-y-12 max-lg:p-4 max-md:p-4 md:my-12 md:text-lg">
      <div class="cus-border flex flex-col md:gap-y-8">
        <div class="flex justify-between gap-x-2 max-md:flex-col md:divide-x max-md:divide-y divide-black">
          <div class="flex flex-col justify-center items-center md:text-lg md:p-4 max-md:py-4 gap-4">
            <div class="h-20 w-20 md:h-40 md:w-40">
              <img v-if="portfolioData?.introduction.avatarUrl" :src="portfolioData?.introduction.avatarUrl" alt=""
                   class="w-full h-full rounded-full">
              <UserCircleIcon v-else class="w-full h-full rounded-full" />
            </div>
            <p>設計師</p>
            <p>{{ portfolioData?.introduction.designerName }}</p>
            <div class="flex items-center">
              <p class="mr-4">通過公司認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
            <div class="flex items-center">
              <p class="mr-4">通過身份認證</p>
              <img src="/vectors/general/pass.svg" alt="" class="h-6 w-6">
            </div>
          </div>

          <div class="md:w-4/5 flex flex-col justify-center md:text-lg md:p-4 max-md:py-4 gap-2">
            <div class="w-full flex gap-x-2 items-center">
              <p class="flex-shrink-0">滿意度</p>
              <p class="flex-shrink-0">{{ portfolioData?.introduction.satisfaction }}</p>
              <button class="cus-btn rounded-lg md:px-4 px-2 py-1 text-base"
                      v-if="portfolioData?.introduction.satisfaction !=='尚無評價'"
                      @click="openSatisfactionModal()">
                <span class="">查看滿意度</span>
              </button>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">作品數量</p>
              <p class="break-all">{{ portfolioData?.introduction.portfolioCount }}件</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">公司名稱</p>
              <p class="break-all">{{ portfolioData?.introduction.companyName }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">統一編號</p>
              <p class="break-all">{{ portfolioData?.introduction.companyUnifiedBusinessNumber }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">服務時間</p>
              <p class="break-all">{{ portfolioData?.introduction.companyServiceTime }}</p>
            </div>
            <div class="w-full flex gap-x-2">
              <p class="flex-shrink-0">施工團隊</p>
              <p class="break-all">{{ portfolioData?.introduction.hasConstructionTeam }}</p>
            </div>
            <p>服務地區</p>
            <div class="w-full flex gap-2 items-center flex-wrap">
              <div v-for="city in portfolioData?.introduction.region" :key="city"
                   class=" p-2 border border-gray-400 rounded-xl text-nowrap">
                <p>{{ city }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex gap-2 md:px-2 justify-between md:text-lg text-nowrap">
          <button
            class="cus-btn w-full"
            @click="goToMeasurePublish()">
            <span class="max-md:hidden">我需要到府丈量服務，了解更多服務</span>
            <span class="md:hidden">我需要到府丈量服務</span>
          </button>
        </div>
      </div>

      <div v-for="portfolio in portfolioData?.portfolios" :key="portfolio.portfolioId"
           class="cus-border w-full flex flex-col divide-y divide-black">
        <div class="flex flex-col gap-2">
          <div>
            <swiper-container class="fix-pagination" :pagination="true" space-between="30">
              <swiper-slide v-for="images in portfolio.media" :key="images">
                <img :src="images.url" alt="作品集" class="object-contain block mx-auto h-96 max-md:h-64">
              </swiper-slide>
            </swiper-container>
          </div>
          <div class="flex py-8 space-x-4 justify-center md:justify-start">
            <div class="flex max-md:flex-col items-center gap-4">
              <p style="color: #56440E">用途</p>
              <p class="break-all">{{ portfolio.usage }}</p>
            </div>
            <div class="flex max-md:flex-col items-center gap-4">
              <p style="color: #56440E">風格</p>
              <p class="break-all">{{ portfolio.style }}</p>
            </div>
            <div class="flex max-md:flex-col items-center gap-4">
              <p style="color: #56440E">主題</p>
              <p class="break-all">{{ portfolio.theme }}</p>
            </div>
          </div>
        </div>

        <div class="flex flex-col py-4 gap-2">
          <div class="mb-4">
            <p class="w-full whitespace-pre-line">{{ portfolio.description }}</p>
          </div>
          <div class="flex space-x-4 md:space-x-28 justify-between md:text-lg text-nowrap">
            <button class="cus-btn w-full"
                    @click="openDirectToStep1Modal(portfolioData?.introduction.avatarUrl,'電話聯絡')">電話聯絡
            </button>
            <button class="cus-btn w-full"
                    @click="openDirectToStep1Modal(portfolioData?.introduction.avatarUrl,'立即聯絡')">立即聯絡
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <DefaultModal :title="modalTextAndAvatar.text" :click-outside-close="false" :show-close-button="true"
                @closeModal="directToStep1Modal?.closeModal()" modalWidth="max-w-lg" ref="directToStep1Modal">
    <div class="flex flex-col gap-y-2 m-3 text-black items-center">
      <img v-if="modalTextAndAvatar.avatar" :src="modalTextAndAvatar.avatar" alt=""
           class="h-16 w-16 mr-4 rounded-full">
      <p>在{{ modalTextAndAvatar.text }}設計師之前</p>
      <p>請讓我們先了解您的裝潢需求</p>
      <p>這些資訊將幫助設計師更完整了解需求</p>
      <button type="button"
              class="button-basic w-full bg-color-selected hover:opacity-80"
              @click="closeDirectToStep1Modal()">下一步
      </button>
    </div>
  </DefaultModal>

  <SatisfactionModal ref="modalSatisfactionRef" :designer-id="designerId" :is-designer="false" />
</template>
