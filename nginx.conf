worker_processes 1;
events { worker_connections 1024; }
http {
    include       mime.types;
    default_type  application/octet-stream;
    map $request_uri $x_robots {
    default "";
    ~^/customer/(?:orderList|register)(?:/|$) "noindex, nofollow, noarchive";
    }
    server {
        listen       3030;
        server_name  _;
        port_in_redirect off;
        root /usr/share/nginx/html;
        add_header X-Robots-Tag $x_robots always;
        location / {
            try_files $uri $uri.html /index.html;
        }
        # 可選：gzip 支援
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
}
