export const downloadFile = async (url: string) => {
  // 下方是直接下載檔案的方式
  // try {
  //   // 從S3 URL 獲取檔案
  //   const response = await fetch(url);
  //   if (!response.ok) throw new Error('Network response was not ok');
  //
  //   // 將響應轉換為 Blob 對象
  //   const blob = await response.blob();
  //   const downloadUrl = URL.createObjectURL(blob);
  //
  //
  //   // 創建一個隱藏的 <a> 標籤
  //   const link = document.createElement('a');
  //   link.href = downloadUrl;
  //   link.download = url.split('/').pop() || 'download'; // 設置預設檔名
  //   document.body.appendChild(link);
  //   // 觸發點擊事件下載文件
  //   link.click();
  //   // 移除 <a> 標籤並釋放 URL 對象
  //   document.body.removeChild(link);
  //   URL.revokeObjectURL(downloadUrl);
  // } catch (error) {
  //   console.error('Download failed:', error);
  // }

  // 下方是先預覽檔案 格式不支援預覽才下載的方式
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.download = url.split('/').pop() || 'download'; // 設置默認文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};