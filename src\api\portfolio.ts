import BaseApiService from './baseApiService.ts';
import {
  DeletePortfolioResponse,
  GetDesignCompanyListResponse,
  GetDesignerRateResponse,
  GetSingleDesignerPageResponse,
  UploadPortfolioResponse
} from '@/model/response/portfolioResponse.ts';
import {
  DeletePortfolioRequest,
  EditPortfolioRequest,
  GetDesignCompanyListRequest,
  GetDesignerRateRequest,
  GetSingleDesignerPageRequest,
  PublishPortfolioRequest
} from '@/model/request/protfolioRequest.ts';
import { getRedirectDesignerPageRequest } from '@/model/request/accountRequest.ts';
import { getRedirectDesignerPageResponse } from '@/model/response/accountResponse.ts';

export class PortfolioService {
  // /**
  //  * @description: 取得設計師作品集
  //  */
  // static async getDesignerPortfolio(requestData: GetDesignerPortfolioRequest): Promise<GetDesignerPortfolioResponse> {
  //   const response = await BaseApiService.Customer.post(('/Portfolio/Get/Many/Post'), requestData);
  //   return response.data as GetDesignerPortfolioResponse;
  // }

  /**
   * @description: 客戶查看設計公司頁面
   */
  static async getDesignCompanyList(requestData: GetDesignCompanyListRequest): Promise<GetDesignCompanyListResponse> {
    const response = await BaseApiService.Customer.post(('/Portfolio/Get/Many/DesignerCompany'), requestData);
    return response.data as GetDesignCompanyListResponse;
  }

  /**
   * @description: 客戶查看指定設計師頁面
   */
  static async getSingleDesignerPageByCustomer(requestData: GetSingleDesignerPageRequest): Promise<GetSingleDesignerPageResponse> {
    const response = await BaseApiService.Customer.post(('/Portfolio/Get/Page'), requestData);
    return response.data as GetSingleDesignerPageResponse;
  }

  /**
   * @description: 設計師查看指定設計師頁面
   */
  static async getSingleDesignerPageByDesigner(requestData: GetSingleDesignerPageRequest): Promise<GetSingleDesignerPageResponse> {
    const response = await BaseApiService.Designer.post(('/Portfolio/Get/Page'), requestData);
    return response.data as GetSingleDesignerPageResponse;
  }

  /**
   * @description: 客戶查看設計師評價(滿意度)
   */
  static async getDesignerRateByCustomer(requestData: GetDesignerRateRequest): Promise<GetDesignerRateResponse> {
    const response = await BaseApiService.Customer.post(('/Portfolio/Get/Rating'), requestData);
    return response.data as GetDesignerRateResponse;
  }

  /**
   * @description: 設計師查看設計師評價(滿意度)
   */
  static async getDesignerRateByDesigner(requestData: GetDesignerRateRequest): Promise<GetDesignerRateResponse> {
    const response = await BaseApiService.Designer.post(('/Portfolio/Get/Rating'), requestData);
    return response.data as GetDesignerRateResponse;
  }

  // /**
  //  * @description: 作品集按愛心
  //  */
  // static async setPortfolioLike(requestData: SetPortfolioLikeRequest): Promise<SetPortfolioLikeResponse> {
  //   const response = await BaseApiService.Customer.post(('/Portfolio/Set/Like'), requestData);
  //   return response.data as SetPortfolioLikeResponse;
  // }
  //
  // /**
  //  * @description: 作品集添加瀏覽紀錄
  //  */
  // static async setPortfolioView(requestData: SetPortfolioViewRequest): Promise<SetPortfolioViewResponse> {
  //   const response = await BaseApiService.Customer.post(('/Portfolio/Set/View'), requestData);
  //   return response.data as SetPortfolioViewResponse;
  // }

  /**
   * @description: 設計師刊登作品
   */
  static async publishPortfolio(requestData: PublishPortfolioRequest): Promise<UploadPortfolioResponse> {
    const response = await BaseApiService.Designer.post(('/Portfolio/Designer/Publish'), requestData);
    return response.data as UploadPortfolioResponse;
  }

  /**
   * @description: 設計師修改作品
   */
  static async editPortfolio(requestData: EditPortfolioRequest): Promise<UploadPortfolioResponse> {
    const response = await BaseApiService.Designer.post(('/Portfolio/Designer/Edit'), requestData);
    return response.data as UploadPortfolioResponse;
  }

  /**
   * @description: 設計師刪除作品
   */
  static async deletePortfolio(requestData: DeletePortfolioRequest): Promise<DeletePortfolioResponse> {
    const response = await BaseApiService.Designer.post(('/Portfolio/Designer/Delete'), requestData);
    return response.data as DeletePortfolioResponse;
  }

  /**
   * @description: 客戶取得設計師頁面的重導網址
   */
  static async getRedirectDesignerPageByCustomer(requestData: getRedirectDesignerPageRequest): Promise<getRedirectDesignerPageResponse> {
    const response = await BaseApiService.Customer.post(('/Portfolio/RedirectDesignerPage'), requestData);
    return response.data as getRedirectDesignerPageResponse;
  }
}
