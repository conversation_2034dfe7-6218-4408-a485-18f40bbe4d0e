<script setup lang="ts">
import OrderPublishedModal from '@/components/customer/OrderPublishedModal.vue';
import { onMounted, ref } from 'vue';

const orderPublishedModalRef = ref<InstanceType<typeof OrderPublishedModal> | null>(null);

onMounted(() => {
  orderPublishedModalRef.value?.openOrderPublishedModal();
});
</script>

<template>
  <OrderPublishedModal ref="orderPublishedModalRef" />
</template>
