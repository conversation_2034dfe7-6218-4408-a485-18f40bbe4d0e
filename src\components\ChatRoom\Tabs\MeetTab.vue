<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { GetMeetListResponse, MeetRecordItem } from '@/model/response/noteResponse.ts';
import { NoteListService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import VideoModal from '@/components/ChatRoom/VideoModal.vue';
import { formatFullDateTime } from '@/utils/timeFormat.ts';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const data = ref<MeetRecordItem[]>([]);
const skip = ref<number>(0);
const videoRef = ref<InstanceType<typeof VideoModal> | null>(null);
const isFirstTime = ref<boolean>(true);
const isMoreData = ref<boolean>(true);

const getMeetList = async () => {
  let res: GetMeetListResponse;
  if (props.isDesigner) {
    res = await NoteListService.GetMeetListByDesigner(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  } else {
    res = await NoteListService.GetMeetListByCustomer(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    if (res.meets.length < 20) {
      isMoreData.value = false;
    }

    if (res.meets.length === 0) {
      if (!isFirstTime.value) toastInfo('已無更多資料');
      return;
    } else {
      data.value.push(...res.meets);
      skip.value += 20;
    }
  }
};

const openVideoModal = (url: string) => {
  videoRef.value?.openModal(url);
};

onMounted(async () => {
  await getMeetList();
  isFirstTime.value = false;
});
</script>

<template>
  <div class="w-full text-black" v-if="data.length === 0">
    <p>尚無任何會議記錄</p>
  </div>
  <div class="w-full flex flex-col gap-y-1" v-else>
    <div class="w-full">
      <p class="text-red-600 text-sm text-start">
        以下資訊用於保護雙方權益，未經過雙方同意請勿提供給第三方任何資訊，使用過程需遵守個資法。
      </p>
    </div>
    <!--    會議記錄本體-->
    <div>
      <div v-for="meet in data" :key="meet.meetId"
           class="w-full p-2 border flex flex-col gap-y-1 cursor-pointer"
           @click="openVideoModal(meet.meetUrl)">
        <div class="flex gap-x-2 justify-start items-center">
          <img v-if="meet.initiatorAvatar" :src="meet.initiatorAvatar" alt="avatar" class="w-8 h-8 rounded-full" />
          <img v-else src="/vectors/general/avatar.svg" alt="avatar" class="w-8 h-8 rounded-full" />
          <p class="font-bold">{{ meet.initiatorName }}</p>
        </div>
        <div class="flex gap-x-2 items-center">
          <p>開始時間</p>
          <p>{{ formatFullDateTime(meet.startedAt) }}</p>
        </div>
        <div class="flex gap-x-2 items-center">
          <p>對話時間</p>
          <p>{{ meet.duration }}秒</p>
        </div>
      </div>
    </div>
    <p class="cursor-pointer" v-if="isMoreData" @click="getMeetList()">查看更多</p>
  </div>
  <VideoModal ref="videoRef" />
</template>
