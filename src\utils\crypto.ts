import CryptoJS from 'crypto-js';

// SHA256 雜湊
export const hashPassword = async (plainText: string): Promise<string> => {
  if (!window.crypto || !window.crypto.subtle) {
    throw new Error('Web Crypto API is not supported in this browser.');
  }
  const encoder = new TextEncoder();
  const data = encoder.encode(plainText);
  const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));

  return hashArray.map((byte) => byte.toString(16).padStart(2, '0')).join('');
};

//AES-256 加密
const secretKey = 'MMSLAB-406-HomeEasy-Designer-Web'

export const encryptAES = (plainText: string): string => {
  return CryptoJS.AES.encrypt(plainText, secretKey).toString();
};

export const decryptAES = (cipherText: string): string => {
  const bytes = CryptoJS.AES.decrypt(cipherText, secretKey);
  return bytes.toString(CryptoJS.enc.Utf8);
};
