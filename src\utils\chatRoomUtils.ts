export const getFileExtension = (fileName: string) => {
  return fileName.split('.').pop();
};

export const isMsgLink = (text: string) => {
  const urlRegex = /[-a-zA-Z0-9@:%.+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%+.~#?&//=]*)/;
  return urlRegex.test(text);
};

export const openNewTab = (url: string) => {
  if (!/^https?:\/\//i.test(url)) {
    url = 'http://' + url;
  }
  window.open(url, '_blank');
};
