FROM node:18-bookworm-slim AS build-stage
WORKDIR /src
#補依賴
RUN apt-get update && apt-get install -y \
  wget \
  ca-certificates \
  fonts-liberation \
  libappindicator3-1 \
  libasound2 \
  libatk-bridge2.0-0 \
  libatk1.0-0 \
  libcups2 \
  libdbus-1-3 \
  libdrm2 \
  libgbm1 \
  libgtk-3-0 \
  libnspr4 \
  libnss3 \
  libx11-xcb1 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  xdg-utils \
  libu2f-udev \
  libvulkan1 \
  --no-install-recommends \
  && apt-get clean && rm -rf /var/lib/apt/lists/*
COPY package.json yarn.lock* ./
RUN yarn
COPY . .
RUN yarn build:prod
RUN yarn global add serve
#爬蟲，預渲染用
RUN yarn add puppeteer
#啟動背景 serve server讓爬蟲抓
RUN serve -s dist -l 3030 & \
    sleep 5 && \
    node puppeteer-lite.cjs && \
    pkill serve || true

# Production stage：使用 nginx
FROM nginx:1.25-alpine
# 將剛剛 build 出來的 dist 全部 copy 到 nginx 靜態根目錄
COPY --from=build-stage /src/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 3030
CMD ["nginx", "-g", "daemon off;"]