export interface UpdateProcessRequest {
  orderId: string;
  builders: SetProcessImageBuilder[];
}

export interface FinishProcessRequest {
  orderId: string;
  processKey: string;
}

export interface SetProcessImageBuilder {
  key: string; // 項目key
  pushImage: {
    url: string; //一個項目中 單個照片的S3url
    description: string; //一個項目中 單個照片的註解
  }[]; //新增照片
  pullImage: string[]; // 照片自己的 key
  setImageDescription: {
    key: string;  // 照片自己的key
    description: string; // 照片自己的註解
  }[]; //更新照片的註解
}