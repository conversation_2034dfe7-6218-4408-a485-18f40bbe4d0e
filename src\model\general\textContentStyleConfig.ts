//這個檔案記載了TextContent相關組件的特殊樣式，例如某個欄位要加粗體，某個欄位要判斷數值來決定是否顯示紅色數字
//目前設計師端與客戶端統一用一套textContentStyleConfig規則顯示，如果後面要改，可以區分designerTextContentStyleConfig跟customerTextContentStyleConfig
type RuleConfig = Record<string, (data: string, isDesigner?: boolean) => string>; //輸入欄位名和內容(name/text/unit)，可選擇是否為設計師，返回一個HTML物件string

const nameStyleConfig: RuleConfig = {
  //針對欄位名調整
  '水質分數(1-100)': (data) => {
    return `<span class="font-bold">${data}</span>`; //本來就粗體啊 除非哪天取消全部粗體這邊才有效
  },
  水質檢測: (data) => {
    return `<span class="font-bold">${data}</span>`;
  },
  空氣檢測: (data) => {
    return `<span class="font-bold">${data}</span>`;
  },
  噪音評估: (data) => {
    return `<span class="font-bold">${data}</span>`;
  },
  濕度評估: (data) => {
    return `<span class="font-bold">${data}</span>`;
  },
  輻射評估: (data) => {
    return `<span class="font-bold">${data}</span>`;
  }
};

const textStyleConfig: RuleConfig = {
  //調整內文
  '水質分數(1-100)': (data) => {
    return Number(data) < 90 ? `<span class="text-red-600">${data}</span>` : data;
  },
  水質檢測: (data, isDesigner) => {
    //step2欄位
    let string = data;
    if (!isDesigner) {
      // 客戶只顯示括號內的文字，如：普通
      const match = data.match(/\(([^)]+)\)/);
      string = match ? match[1] : data;
    }
    return string.includes('不好') ? `<span class="text-red-600">${string}</span>` : string;
  },
  導電度: (data) => {
    return Number(data) > 50 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'TDS(溶解性固體量)': (data) => {
    return Number(data) > 300 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'TOC(總有機碳)': (data) => {
    return Number(data) > 1.5 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'COD(化學需氧量)': (data) => {
    return Number(data) > 1.5 ? `<span class="text-red-600">${data}</span>` : data;
  },
  餘氯: (data) => {
    return Number(data) > 0.05 ? `<span class="text-red-600">${data}</span>` : data;
  },
  空氣檢測: (data) => {
    //step2欄位
    return data === '良好' || data === '普通' ? data : `<span class="text-red-600">${data}</span>`;
  },
  'HCHO(甲醛)': (data) => {
    return Number(data) > 0.08 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'TVOC(揮發性有機物)': (data) => {
    return Number(data) > 0.6 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'PM2.5(細懸浮微粒)': (data) => {
    return Number(data) > 75 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'PM10(懸浮微粒)': (data) => {
    return Number(data) > 150 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'CO(一氧化碳)': (data) => {
    return Number(data) > 10 ? `<span class="text-red-600">${data}</span>` : data;
  },
  'CO2(二氧化碳)': (data) => {
    return Number(data) > 800 ? `<span class="text-red-600">${data}</span>` : data;
  },
  噪音評估: (data) => {
    const matches = data.match(/(\d+(\.\d+)?)/g);
    if (matches) {
      return matches.reduce((result, match) => {
        const styleClass = parseFloat(match) >= 60 ? 'text-red-600' : '';
        return result.replace(match, `<span class="${styleClass}">${match}</span>`);
      }, data);
    }
    return data;
  },
  噪音檢測: (data) => {
    //step2欄位
    const matches = data.match(/(\d+(\.\d+)?)/g);
    if (matches) {
      return matches.reduce((result, match) => {
        const styleClass = parseFloat(match) >= 60 ? 'text-red-600' : '';
        return result.replace(match, `<span class="${styleClass}">${match}</span>`);
      }, data);
    }
    return data;
  },
  '(.*音量.*)': (data) => {
    //各種XX音量，如房間音量
    const matches = data.match(/(\d+(\.\d+)?)/g);
    if (matches) {
      return matches.reduce((result, match) => {
        const styleClass = parseFloat(match) >= 60 ? 'text-red-600' : '';
        return result.replace(match, `<span class="${styleClass}">${match}</span>`);
      }, data);
    }
    return data;
  },
  濕度評估: (data) => {
    const matches = data.match(/(\d+(\.\d+)?)/g);
    if (matches) {
      return matches.reduce((result, match) => {
        const styleClass = parseFloat(match) >= 70 ? 'text-red-600' : '';
        return result.replace(match, `<span class="${styleClass}">${match}</span>`);
      }, data);
    }
    return data;
  },
  '(.*濕度.*)': (data) => {
    //各種XX濕度，如客廳濕度 濕度檢測(step2欄位)
    const matches = data.match(/(\d+(\.\d+)?)/g);
    if (matches) {
      return matches.reduce((result, match) => {
        const styleClass = parseFloat(match) >= 70 ? 'text-red-600' : '';
        return result.replace(match, `<span class="${styleClass}">${match}</span>`);
      }, data);
    }
    return data;
  },
  輻射評估: (data) => {
    return data === '安全' || data === '普通' ? data : `<span class="text-red-600">${data}</span>`;
  },
  電磁輻射: (data) => {
    //step2欄位
    return data === '安全' || data === '普通' ? data : `<span class="text-red-600">${data}</span>`;
  },
  環境電場: (data) => {
    return Number(data) > 250 ? `<span class="text-red-600">${data}</span>` : data;
  },
  環境磁場: (data) => {
    return Number(data) > 50 ? `<span class="text-red-600">${data}</span>` : data;
  },
  訊號輻射: (data) => {
    return Number(data) > 50 ? `<span class="text-red-600">${data}</span>` : data;
  }
};

const unitStyleConfig: RuleConfig = {
  //針對欄位單位調整
  '水質分數(1-100)': (data) => {
    return `<span class="font-bold">${data}</span>`;
  }
};

export function getStyleConfig(
  configType: 'name' | 'text' | 'unit',
  name: string,
  data: string,
  isDesigner?: boolean
): string {
  //負責找到對應的name，套用樣式
  const configs: Record<string, RuleConfig> = {
    //選擇用哪個表
    name: nameStyleConfig,
    text: textStyleConfig,
    unit: unitStyleConfig
  };
  const config = configs[configType];

  if (config[name]) {
    //先用具體的鍵名
    return config[name](data, isDesigner);
  }

  for (const pattern in config) {
    //如果沒有，則使用正則表達式
    const regex = new RegExp(pattern);
    if (regex.test(name)) {
      return config[pattern](data, isDesigner);
    }
  }

  return data;
}
