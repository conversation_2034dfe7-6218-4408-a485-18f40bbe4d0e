<script setup lang="ts">
import { computed, ref } from 'vue';
import HezPortfolioRadioButton from '@/components/General/HezPortfolioRadioButton.vue';
import { OrderUsage, OrderStyle, OrderTheme, UploadImageType } from '@/utils/hezParameters.ts';
import { toastError, toastSuccess } from '@/utils/toastification.ts';
import { fileTypeCheck, S3uploader } from '@/utils/S3Uploader.ts';
import { MediaItem } from '@/model/general/media.ts';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import { UploadFileEnum } from '@/model/enum/fileType.ts';
import { PortfolioItem, UploadPortfolioResponse } from '@/model/response/portfolioResponse.ts';
import { PortfolioService } from '@/api/portfolio.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const emit = defineEmits(['upload-result']);
const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isEditing = ref(false);
const portfolioData = ref<{
  portfolioId: string;
  description: string;
  usage: string;
  style: string;
  theme: string;
  media: MediaItem[];
}>({
  portfolioId: '',
  description: '',
  usage: '住宅',
  style: '現代',
  theme: '日式',
  media: [] as MediaItem[]
});
const isAPILoading = ref(false);

const closeModal = () => {
  modalRef.value?.closeModal();
};

const openModal = (portfolio?: PortfolioItem) => {
  if (portfolio) {
    isEditing.value = true;
    portfolioData.value = {
      portfolioId: portfolio.portfolioId,
      description: portfolio.description,
      usage: portfolio.usage,
      style: portfolio.style,
      theme: portfolio.theme,
      media: portfolio.media.map(media => ({ url: media.url, type: media.type }))
    };
  } else {
    isEditing.value = false;
    portfolioData.value = {
      portfolioId: '',
      description: '',
      usage: '住宅',
      style: '現代',
      theme: '日式',
      media: [] as MediaItem[]
    };
  }
  modalRef.value?.openModal();
};

const triggerFileInput = () => {
  fileInput.value?.click(); // 因為實現點擊div觸發上傳圖片，觸發 input 的 handleFileChange
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let allFileTypePass = true;
    for (const fileCheck of input.files) {
      if (!fileTypeCheck(fileCheck, UploadFileEnum.Picture)) {
        toastError('檔案格式錯誤');
        allFileTypePass = false;
        break;
      }
    }
    if (!allFileTypePass) {
      input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
      return;
    }

    const files = Array.from(input.files);
    const S3Urls = await S3uploader(files);
    S3Urls.forEach(url => {
      portfolioData.value.media.push({ url: url, type: MediaTypeEnum.Image });
    });
  }
  input.value = ''; // 重置 input.value，以便下次能再次觸發 change 事件
};

const removeOnePreview = (url: string) => {
  portfolioData.value.media = portfolioData.value.media.filter(media => media.url !== url);
};

const uploadPortfolio = async () => {
  if (isAPILoading.value === true) return;
  if (portfolioData.value.description === '') {
    toastError('請輸入圖片說明');
    return;
  }
  if (portfolioData.value?.media.length === 0) {
    toastError('至少上傳一張圖片');
    return;
  }
  isAPILoading.value = true;
  let uploadResult: UploadPortfolioResponse;
  if (isEditing.value) {
    uploadResult = await PortfolioService.editPortfolio(portfolioData.value);
  } else {
    uploadResult = await PortfolioService.publishPortfolio({
      description: portfolioData.value.description,
      usage: portfolioData.value.usage,
      style: portfolioData.value.style,
      theme: portfolioData.value.theme,
      media: portfolioData.value.media
    });
  }
  if (uploadResult.status === APIStatusCodeEnum.Success) {
    isEditing.value ? toastSuccess('修改成功') : toastSuccess('刊登成功');
    emit('upload-result', uploadResult.portfolio, isEditing.value);
    closeModal();
  } else {
    console.error(uploadResult.msg);
  }
  isAPILoading.value = false;
};

const modalTitle = computed(() => {
  return isEditing.value ? '修改作品' : '新增作品';
});

const updatedMediaCount = computed(() => {
  return portfolioData.value.media.length;
});

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="modalTitle" :show-close-button="true" :click-outside-close="false" ref="modalRef"
                @closeModal="closeModal">
    <div class="flex flex-col py-4 px-8 space-y-4">
      <HezPortfolioRadioButton title="裝潢用途" legend="orderUsage" :options="OrderUsage"
                               name="order-usage"
                               v-model="portfolioData.usage" />

      <HezPortfolioRadioButton title="裝潢風格" legend="orderStyle" :options="OrderStyle"
                               name="order-style"
                               v-model="portfolioData.style" />

      <HezPortfolioRadioButton title="裝潢主題" legend="orderTheme" :options="OrderTheme"
                               name="order-theme"
                               v-model="portfolioData.theme" />

      <div class="flex items-start">
        <label class="text-base font-semibold ">作品說明</label>
      </div>
      <div class="flex flex-col gap-y-2 justify-start text-black">
        <input type="file" ref="fileInput" :accept="UploadImageType" multiple required
               @change="handleFileChange" class="hidden">
        <div class="flex flex-grow">
          <label for="about" class="sr-only">workInfo</label>
          <div class="flex flex-grow text-start">
                          <textarea id="description" name="description" rows="4" placeholder="新增圖片說明"
                                    v-model="portfolioData.description"
                                    class="resize-none input-basic rounded-md input-focus" />
          </div>
        </div>
      </div>
      <div class="flex flex-col gap-y-2 items-start">
        <label class="text-base items-start font-semibold ">作品圖片</label>
        <div @click="triggerFileInput"
             class="flex flex-col w-full p-2 bg-gray-200 justify-center items-center rounded-lg cursor-pointer">
          <img src="/vectors/general/addImage.svg" alt="addImage" class="w-10 h-10" />
          <p>新增圖片</p>
        </div>
      </div>
      <div class="flex flex-col gap-x-1 text-start">
        <p class="text-base font-semibold text-black">您即將刊登的圖片({{ updatedMediaCount }})</p>
        <div class="flex flex-wrap gap-x-1">
          <div v-for="image in portfolioData?.media" :key="image.url" class="relative">
            <img :src="image.url" alt="image" class="w-24 h-24" v-if="image.url" />
            <button class="absolute top-0 right-0  w-6 h-6 flex justify-center items-center"
                    @click="removeOnePreview(image.url)">
              <img src="/vectors/general/crossCircle.svg" alt="crossCircle" class="" />
            </button>
          </div>
        </div>
      </div>
      <button type="button"
              class="cus-btn w-full py-4"
              @click="uploadPortfolio">
        <span v-if="isEditing">送出修改作品</span>
        <span v-else>刊登</span>
      </button>
    </div>
  </DefaultModal>
</template>
