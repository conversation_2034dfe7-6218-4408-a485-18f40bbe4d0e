<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import { toastInfo } from '@/utils/toastification.ts';
import { CustomerDesignOrderDetailService, CustomerDesignOrderService } from '@/api/customerOrder.ts';
import {
  DesignOrderStatusEnum,
  DesignOrderSubStatusEnum,
  RemittanceStatusEnum, SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import CustomerHouseInfoForStep2 from '@/components/customer/OrderDetail/CustomerHouseInfoForStep2.vue';
import CustomerHousePhoto from '@/components/customer/OrderDetail/CustomerHousePhoto.vue';
import CustomerHouseCheck from '@/components/customer/OrderDetail/CustomerHouseCheck.vue';
import CustomerFloorPlan from '@/components/customer/OrderDetail/CustomerFloorPlan.vue';
import CustomerHouseNote from '@/components/customer/OrderDetail/CustomerHouseNote.vue';
import CustomerStep1TextContent from '@/components/customer/OrderDetail/CustomerStep1TextContent.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  UserCircleIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import HezDivider from '@/components/General/HezDivider.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import GoToAppModal from '@/components/Modal/GoToAppModal.vue';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import { CustomerOrderDesignItem } from '@/model/response/customerDesignOrderResponse.ts';
import Customer2D from '@/components/customer/OrderDetail/Customer2D.vue';
import Customer3D from '@/components/customer/OrderDetail/Customer3D.vue';
import CustomerAmountDocs from '@/components/customer/OrderDetail/CustomerAmountDocs.vue';
import { moneyAddCommas } from '@/utils/budgetFormat.ts';
import CustomerSignModal from '@/components/Modal/CustomerSignModal.vue';
import CustomerRemitModal from '@/components/Modal/CustomerRemitModal.vue';
import { formatFullDate } from '@/utils/timeFormat.ts';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import CustomerRateModal from '@/components/Modal/CustomerRateModal.vue';

const route = useRoute();
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const chatHubStore = useChatHubStore();
const orderId = route.params.id as string;
const designNotAssignModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const deleteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const goToAppModalRef = ref<InstanceType<typeof GoToAppModal> | null>(null);
const signModal = ref<InstanceType<typeof CustomerSignModal> | null>(null);
const remitModal = ref<InstanceType<typeof CustomerRemitModal> | null>(null);
const goToAppModalProps = ref<{ title: string; content: string; content2?: string }>({
  title: '',
  content: '',
  content2: ''
});
const signModalTitle = ref('合約簽署');
const remitModalTitle = ref('匯款狀態');
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal> | null>(null);
const completeModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const fullCompleteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const rateModalRef = ref<InstanceType<typeof CustomerRateModal> | null>(null);
const design2DCheckModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const design3DCheckModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const amountCheckModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const elapsedMinutes = ref(0);
const elapsedSeconds = ref(0);
const interval = ref<number | null>(null);
const isoTimestamp = ref<string | null>(null);
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);

const orderData = ref<CustomerOrderDesignItem>({
  orderId: '',
  createTime: '',
  refreshTime: '',
  status: DesignOrderStatusEnum.Comparing,
  subStatus: DesignOrderSubStatusEnum.NotUploaded,
  contract: {
    signingStatus: SigningStatusEnum.Init,
    remittanceStatus: RemittanceStatusEnum.NotRemitted,
    amount: 0,
    discountAmount: 0,
    remittedAmount: 0
  },
  isDeleted: false,
  designerId: '',
  designerName: '',
  designerAvatar: '',
  designerPhone: '',
  customerId: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  publishInfo: {
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    designBudget: {
      upper: 0,
      lower: 0
    },
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    discussionFrequency: 0, // 每周討論頻率，單位次/周
    keyDesignDetail: '',
    referenceImages: [],
    isAssignDesigner: false
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
  },
  designContent: {
    design2D: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    design3D: {
      updateTime: '',
      updateCount: 0,
      content: []
    }
  },
  constructionFee: {
    updateTime: '',
    updateCount: 0,
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    constructionEstimate: {
      upper: 0,
      lower: 0
    },
    bigDataEstimate: {
      upper: 0,
      lower: 0
    },
    constructionAmountDocs: [
      {
        amount: 0,
        createTime: '',
        documentUrl: ''
      }
    ]
  },
  quotationCount: 0,
  isNextStepUsed: false,
  chatRoomId: '',
  orderRatingId: ''
});

const goHome = () => {
  router.push({
    name: 'customerhome'
  });
};

const designDetail = computed(() => {
  return {
    orderId: orderData.value.orderId,
    createTime: orderData.value.createTime,
    refreshTime: orderData.value.refreshTime,
    status: orderData.value.status,
    subStatus: orderData.value.subStatus,
    contract: {
      signingStatus: orderData.value.contract.signingStatus,
      remittanceStatus: orderData.value.contract.remittanceStatus,
      amount: moneyAddCommas(orderData.value.contract.amount),
      discountAmount: moneyAddCommas(orderData.value.contract.discountAmount),
      remittedAmount: moneyAddCommas(orderData.value.contract.remittedAmount)
    },
    isDeleted: orderData.value.isDeleted,
    designerId: orderData.value.designerId,
    designerName: orderData.value.designerName,
    designerAvatar: orderData.value.designerAvatar,
    customerId: orderData.value.customerId,
    customerName: orderData.value.customerName,
    address: orderData.value.address,
    publishInfo: orderData.value.publishInfo,
    measureContent: {
      houseInfo: orderData.value.measureContent.houseInfo,
      photos: orderData.value.measureContent.photos,
      houseCheck: orderData.value.measureContent.houseCheck,
      floorPlan: orderData.value.measureContent.floorPlan,
      note: orderData.value.measureContent.note,
      constructionRequest: orderData.value.measureContent.constructionRequest,
      waterQuality: orderData.value.measureContent.waterQuality,
      airQuality: orderData.value.measureContent.airQuality,
      noise: orderData.value.measureContent.noise,
      humidity: orderData.value.measureContent.humidity,
      radiation: orderData.value.measureContent.radiation,
    },
    designContent: {
      design2D: orderData.value.designContent.design2D,
      design3D: orderData.value.designContent.design3D
    },
    constructionFee: orderData.value.constructionFee,
    quotationCount: orderData.value.quotationCount,
    isNextStepUsed: orderData.value.isNextStepUsed,
    chatRoomId: orderData.value.chatRoomId,
    nameAddress: {
      name: orderData.value.customerName,
      address: orderData.value.address.fullName
    },
    design2DMoney: moneyAddCommas(Math.round(orderData.value.contract.amount / 2)),
    design3DMoney: moneyAddCommas(orderData.value.contract.amount - Math.round(orderData.value.contract.amount / 2)),
    latestConstructionAmountDoc: orderData.value.constructionFee.constructionAmountDocs.length === 0 ? {
      amount: '0',
      time: '0',
      documentUrl: '',
      count: 0
    } : {
      amount: moneyAddCommas(orderData.value.constructionFee.constructionAmountDocs.slice(-1)[0].amount),
      time: formatFullDate(orderData.value.constructionFee.constructionAmountDocs.slice(-1)[0].createTime),
      documentUrl: orderData.value.constructionFee.constructionAmountDocs.slice(-1)[0].documentUrl,
      count: orderData.value.constructionFee.constructionAmountDocs.length - 1
    }
  };
});

const showSignOrRemitModal = (title?: string) => {
  if (designDetail.value.contract.signingStatus === SigningStatusEnum.Init || designDetail.value.contract.signingStatus === SigningStatusEnum.VerifyFail) {
    if (title) {
      signModalTitle.value = title;
    } else {
      signModalTitle.value = '合約簽署';
    }
    signModal.value?.openModal();
  } else if (designDetail.value.contract.signingStatus === SigningStatusEnum.Verifying) {
    if (title) {
      goToAppModalProps.value = { title: title, content: '合約尚在審核中' };
    } else {
      goToAppModalProps.value = { title: '合約簽署', content: '合約尚在審核中' };
    }
    goToAppModalRef.value?.openModal();
  } else if (designDetail.value.contract.remittanceStatus === RemittanceStatusEnum.NotRemitted) {
    if (title) {
      remitModalTitle.value = title;
    } else {
      remitModalTitle.value = '匯款狀態';
    }
    remitModal.value?.openModal();
  } else if (designDetail.value.contract.remittanceStatus === RemittanceStatusEnum.InProgress) {
    if (title) {
      goToAppModalProps.value = { title: title, content: '匯款尚在審核中' };
    } else {
      goToAppModalProps.value = { title: '匯款狀態', content: '匯款尚在審核中' };
    }
    goToAppModalRef.value?.openModal();
  }
};

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      switch (designDetail.value.status) {
        case DesignOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '免費語音', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('免費語音');
          break;
        default:
          appUsePhoneModalRef.value?.openModal();
          break;
      }
      break;
    case contactItemEnum.Meet:
      switch (designDetail.value.status) {
        case DesignOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '視訊會議', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('視訊會議');
          break;
        default:
          createMeet(designDetail.value.chatRoomId, false, customerInfoStore.userId);
      }
      break;
    case contactItemEnum.Chat:
      switch (designDetail.value.status) {
        case DesignOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '聊天室', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('聊天室');
          break;
        default:
          if (chatHubStore._worker === null) {
            sharedWorkerNotSupportModalRef.value?.openModal();
          } else {
            chatRoomRef.value?.showChatRoom();
          }
      }
      break;
    case contactItemEnum.Note:
      switch (designDetail.value.status) {
        case DesignOrderStatusEnum.Comparing:
          goToAppModalProps.value = { title: '紀錄本', content: '目前設計師尚未確定' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderStatusEnum.Contracting:
          showSignOrRemitModal('紀錄本');
          break;
        default:
          noteBookRef.value?.openModal();
      }
  }
};

const topInfoClicked = () => {
  switch (designDetail.value.status) {
    case DesignOrderStatusEnum.Comparing:
      designNotAssignModalRef.value?.openModal();
      break;
    default:
      goToAppModalProps.value = { title: '家易App', content: '因網頁無法自動刷新' };
      goToAppModalRef.value?.openModal();
  }
};

const customerInfoClicked = () => {
  switch (designDetail.value.status) {
    case DesignOrderStatusEnum.Comparing:
      deleteModalRef.value?.openModal();
      break;
    case DesignOrderStatusEnum.Contracting:
      showSignOrRemitModal();
      break;
    default:
      break;
  }
};

const design2DClicked = () => {
  switch (designDetail.value.status) {
    case DesignOrderStatusEnum.Contracting:
      showSignOrRemitModal('2D室內設計');
      break;
    case DesignOrderStatusEnum.Working:
      switch (designDetail.value.subStatus) {
        case DesignOrderSubStatusEnum.NotUploaded:
        case DesignOrderSubStatusEnum.Design2DUploaded:
          goToAppModalProps.value = { title: '2D室內設計', content: '設計師尚未請求撥款' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.Design2DRequest:
          design2DCheckModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.Design2DReject:
          goToAppModalProps.value = {
            title: '2D室內設計',
            content: '設計師正在修正中',
            content2: '請透過視訊會議與設計師溝通'
          };
          goToAppModalRef.value?.openModal();
          break;
        default:
          break;
      }
      break;
    default:
      break;
  }
};

const check2D = async (agree: boolean) => {
  const res = await CustomerDesignOrderService.accepting2D({ orderId: orderId, isApprove: agree });
  if (res.status === APIStatusCodeEnum.Success) {
    design2DCheckModalRef.value?.closeModal();
    orderData.value = res.result;
  } else {
    toastInfo('操作失敗');
  }
};

const design3DClicked = () => {
  switch (designDetail.value.status) {
    case DesignOrderStatusEnum.Contracting:
      showSignOrRemitModal('3D模型設計');
      break;
    case DesignOrderStatusEnum.Working:
      switch (designDetail.value.subStatus) {
        case DesignOrderSubStatusEnum.Design2DAgree:
        case DesignOrderSubStatusEnum.Design3DUploaded:
          goToAppModalProps.value = { title: '3D模型設計', content: '設計師尚未請求撥款' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.Design3DRequest:
          design3DCheckModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.Design3DReject:
          goToAppModalProps.value = {
            title: '3D模型設計',
            content: '設計師正在修正中',
            content2: '請透過視訊會議與設計師溝通'
          };
          goToAppModalRef.value?.openModal();
          break;
        default:
          break;
      }
      break;
    default:
      break;
  }
};

const check3D = async (agree: boolean) => {
  const res = await CustomerDesignOrderService.accepting3D({ orderId: orderId, isApprove: agree });
  if (res.status === APIStatusCodeEnum.Success) {
    design3DCheckModalRef.value?.closeModal();
    orderData.value = res.result;
  } else {
    toastInfo('操作失敗');
  }
};

const constructionAmountClicked = () => {
  switch (designDetail.value.status) {
    case DesignOrderStatusEnum.Contracting:
      showSignOrRemitModal('裝潢施工報價');
      break;
    case DesignOrderStatusEnum.Working:
      switch (designDetail.value.subStatus) {
        case DesignOrderSubStatusEnum.Design2DAgree:
        case DesignOrderSubStatusEnum.Design3DAgree:
          goToAppModalProps.value = { title: '裝潢施工報價', content: '設計師尚未報價' };
          goToAppModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.ConstructionAmountDocsUploaded:
          amountCheckModalRef.value?.openModal();
          break;
        case DesignOrderSubStatusEnum.ConstructionAmountDocsRequest:
          goToAppModalProps.value = {
            title: '裝潢施工報價',
            content: '設計師正在修正中',
            content2: '請透過視訊會議與設計師溝通'
          };
          goToAppModalRef.value?.openModal();
          break;
        default:
          break;
      }
      break;
    default:
      break;
  }
};

const agreeAmount = async () => {
  const res = await CustomerDesignOrderService.approveAmountDocs({ orderId: orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    amountCheckModalRef.value?.closeModal();
    orderData.value = res.result;
  } else {
    toastInfo('操作失敗');
  }
};

const rejectAmount = async () => {
  const res = await CustomerDesignOrderService.rejectAmountDocs({ orderId: orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    amountCheckModalRef.value?.closeModal();
    orderData.value = res.result;
  } else {
    toastInfo('操作失敗');
  }
};

const deleteOrder = async () => {
  await CustomerDesignOrderService.deleteOrder({ orderId: orderId });
  toastInfo('訂單已刪除');
  //回訂單紀錄
  await router.push({
    name: 'customerorderlist'
  });
};

const getOrderData = async () => {
  const response = await CustomerDesignOrderDetailService.getOneOrderDetail({ orderId: orderId });
  if (response.status !== APIStatusCodeEnum.Success) {
    toastInfo('找不到訂單資料');
    goHome();
  } else {
    orderData.value = response.result;
    if (orderData.value.status === DesignOrderStatusEnum.Completed) {
      fullCompleteModalRef.value?.openModal();
    } else if (orderData.value.subStatus >= DesignOrderSubStatusEnum.ConstructionAmountDocsUploaded) {
      completeModalRef.value?.openModal();
    }
    if (orderData.value.status === DesignOrderStatusEnum.Comparing) {
      isoTimestamp.value = orderData.value.createTime;
      updateElapsedTime();
    }
  }
};

const closeFullCompleteModal = () => {
  fullCompleteModalRef.value?.closeModal();
  if (orderData.value.orderRatingId) rateModalRef.value?.openModal();
};

const closeCompleteModal = () => {
  completeModalRef.value?.closeModal();
  if (orderData.value.orderRatingId) rateModalRef.value?.openModal();
};

const updateElapsedTime = () => {
  if (isoTimestamp.value) {
    const startTime = new Date(isoTimestamp.value).getTime();
    const now = Date.now();
    const elapsedTime = now - startTime;

    elapsedMinutes.value = Math.floor(elapsedTime / 1000 / 60);
    elapsedSeconds.value = Math.floor((elapsedTime / 1000) % 60);
  }
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

watch(isoTimestamp, (newTimestamp) => {
  if (newTimestamp) {
    updateElapsedTime();
    interval.value = setInterval(updateElapsedTime, 1000) as unknown as number;
  }
});

onMounted(async () => {
  if (!customerInfoStore.loginState) {
    goHome();
    return;
  }
  await getOrderData();
});

onBeforeUnmount(() => {
  if (interval.value !== null) {
    clearInterval(interval.value);
  }
});

</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <div class="cus-border text-lg">
      <div class="flex flex-row justify-center ">
        <p class="text-2xl font-bold text-black">室內設計</p>
      </div>
      <div class="flex p-2 gap-x-4 justify-start items-start max-md:flex-col"
           @click="topInfoClicked()">
        <img v-if="designDetail.designerAvatar" class="w-20 h-20 rounded-full"
             :src="designDetail.designerAvatar" alt="designerAvatar" />
        <UserCircleIcon v-else
                        class="h-24 max-md:h-12 w-24 max-md:w-12 flex-none" />
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class=" font-medium text-nowrap">設計師姓名</p>
            <p v-if="designDetail.status !== DesignOrderStatusEnum.Comparing"
               class=" font-bold">
              {{ designDetail.designerName }}</p>
            <p v-else class=" font-bold text-red-600">(未確定)</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-medium text-nowrap">室內設計費</p>
            <p v-if="designDetail.status !== DesignOrderStatusEnum.Comparing"
               class=" font-bold">
              {{ designDetail.contract.amount }}</p>
            <p v-else class=" font-bold text-red-600">(比價中)</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-medium text-nowrap">優惠總金額</p>
            <p v-if="designDetail.status !== DesignOrderStatusEnum.Comparing"
               class=" font-bold">
              {{ designDetail.contract.discountAmount }}</p>
            <p v-else class=" font-bold text-red-600">(比價中)</p>
          </div>
        </div>
      </div>
      <HezDivider />
      <div class="flex gap-2 justify-evenly">
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Phone)">
          <PhoneIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">免費語音</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Meet)">
          <VideoCameraIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">視訊會議</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Chat)">
          <ChatBubbleLeftEllipsisIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">聊天室</p>
        </div>
        <div class="flex flex-col gap-3 items-center text-center mx-auto cursor-pointer hover-zoom"
             @click="contactItemClicked(contactItemEnum.Note)">
          <ClipboardDocumentListIcon class="md:h-16 md:w-16 h-8 w-8" />
          <p class="font-bold text-nowrap max-md:text-base">紀錄本</p>
        </div>
      </div>
    </div>

    <div v-if="orderData.chatRoomId && chatHubStore._worker !== null"
         class="fixed flex flex-col justify-end bottom-0 right-0 w-80 z-40"
         :class="isChatRoomShow ? 'h-3/5' : 'h-auto' ">
      <ChatRoom :is-for-meet="false"
                :room-id="orderData.chatRoomId" :is-designer="false"
                @show-chat-room="changeChatRoomShow"
                ref="chatRoomRef" />
    </div>

    <!-- CustomerInfo Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="customerInfoClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          1
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg">客戶需支付設計費用</p>
            <p v-if="designDetail.status !== DesignOrderStatusEnum.Comparing"
               class="font-bold md:text-lg">
              {{ designDetail.contract.discountAmount }} (已折抵)</p>
            <p v-else class=" font-bold md:text-lg text-red-600">(比價中)</p>
          </div>
          <div v-if="designDetail.status !== DesignOrderStatusEnum.Comparing" class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">合約狀態</p>
            <p v-if="designDetail.contract.signingStatus === SigningStatusEnum.Init"
               class=" font-bold md:text-lg text-red-600">未簽署</p>
            <p v-else-if="designDetail.contract.signingStatus === SigningStatusEnum.Verifying"
               class=" font-bold md:text-lg text-blue-600">已簽署(審核中)</p>
            <p v-else-if="designDetail.contract.signingStatus === SigningStatusEnum.Verified"
               class=" font-bold md:text-lg text-black">已簽署</p>
            <p v-else class=" font-bold md:text-lg text-red-600">已簽署(審核失敗)</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">匯款狀態</p>
            <p v-if="designDetail.contract.remittanceStatus === RemittanceStatusEnum.NotRemitted"
               class=" font-bold md:text-lg text-red-600">未匯款</p>
            <p v-else-if="designDetail.contract.remittanceStatus === RemittanceStatusEnum.InProgress"
               class=" font-bold md:text-lg text-blue-600">已匯款(審核中)</p>
            <p v-else class=" font-bold md:text-lg text-black">已匯款</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 2D Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="design2DClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          2
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class=" font-bold md:text-lg text-black">2D室內設計</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">工程款</p>
            <p v-if="designDetail.status === DesignOrderStatusEnum.Comparing"
               class=" font-bold md:text-lg text-black">(比價中)</p>
            <p v-else class=" font-bold md:text-lg text-black">{{ designDetail.design2DMoney }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <template v-if="designDetail.status === DesignOrderStatusEnum.Comparing">
              <p class=" font-bold md:text-lg text-black">(未確認)</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Contracting">
              <p class=" font-bold md:text-lg text-black">使用者未同意撥款</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Working">
              <p v-if="designDetail.subStatus === DesignOrderSubStatusEnum.NotUploaded"
                 class=" font-bold md:text-lg text-blue-600">使用者未同意撥款</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design2DUploaded"
                 class=" font-bold md:text-lg text-blue-600">設計師上傳中</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design2DRequest"
                 class=" font-bold md:text-lg text-blue-600">設計師已完成上傳</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design2DReject"
                 class=" font-bold md:text-lg text-red-600">使用者要求修正</p>
              <p v-else-if="designDetail.subStatus >= DesignOrderSubStatusEnum.Design2DAgree "
                 class=" font-bold md:text-lg text-black">使用者已同意撥款</p>
            </template>
            <template v-else>
              <p class=" font-bold md:text-lg text-black">使用者已同意撥款</p>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 3D Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="design3DClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          3
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class=" font-bold md:text-lg text-black">3D模型設計</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">工程款</p>
            <p v-if="designDetail.status === DesignOrderStatusEnum.Comparing"
               class=" font-bold md:text-lg text-black">(比價中)</p>
            <p v-else class=" font-bold md:text-lg text-black">{{ designDetail.design3DMoney }}</p>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <template v-if="designDetail.status === DesignOrderStatusEnum.Comparing">
              <p class=" font-bold md:text-lg text-black">(未確認)</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Contracting">
              <p class=" font-bold md:text-lg text-black">使用者未同意撥款</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Working">
              <p v-if="designDetail.subStatus < DesignOrderSubStatusEnum.Design2DAgree"
                 class=" font-bold md:text-lg text-black">使用者未同意撥款</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design2DAgree"
                 class=" font-bold md:text-lg text-blue-600">使用者未同意撥款</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design3DUploaded"
                 class=" font-bold md:text-lg text-blue-600">設計師上傳中</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design3DRequest"
                 class=" font-bold md:text-lg text-blue-600">設計師已完成上傳</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design3DReject"
                 class=" font-bold md:text-lg text-red-600">使用者要求修正</p>
              <p v-else-if="designDetail.subStatus >= DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-black">使用者已同意撥款</p>
            </template>
            <template v-else>
              <p class=" font-bold md:text-lg text-black">使用者已同意撥款</p>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Amount Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="constructionAmountClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          4
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
            <p class=" font-bold md:text-lg text-black">裝潢施工報價</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">裝潢施工報價</p>
            <template v-if="designDetail.status === DesignOrderStatusEnum.Comparing">
              <p class=" font-bold md:text-lg text-black">(比價中)</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Contracting">
              <p v-if="designDetail.constructionFee.constructionAmountDocs.length === 0"
                 class=" font-bold md:text-lg text-black">未報價</p>
              <p v-else class="font-bold md:text-lg text-black">
                {{ designDetail.latestConstructionAmountDoc.amount }}</p>
            </template>
            <template v-else>
              <p
                v-if="designDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree && designDetail.constructionFee.constructionAmountDocs.length > 0"
                class=" font-bold md:text-lg text-black">
                {{ designDetail.latestConstructionAmountDoc.amount }}</p>
              <p v-else-if="designDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-black">未報價</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-red-600">未報價</p>
              <p v-else class=" font-bold md:text-lg text-black">
                {{ designDetail.latestConstructionAmountDoc.amount }}</p>
            </template>
          </div>
          <div class="flex gap-3">
            <p class=" font-bold md:text-lg text-nowrap">裝潢施工清單</p>
            <template v-if="designDetail.status === DesignOrderStatusEnum.Comparing">
              <p class=" font-bold md:text-lg text-black">(未確認)</p>
            </template>
            <template v-else-if="designDetail.status === DesignOrderStatusEnum.Contracting">
              <p v-if="designDetail.constructionFee.constructionAmountDocs.length === 0"
                 class="font-bold md:text-lg text-black">未上傳</p>
              <p v-else class="font-bold md:text-lg text-black">
                第{{ designDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ designDetail.latestConstructionAmountDoc.time }})</p>
            </template>
            <template v-else>
              <p
                v-if="designDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree && designDetail.constructionFee.constructionAmountDocs.length > 0"
                class=" font-bold md:text-lg text-black">
                第{{ designDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ designDetail.latestConstructionAmountDoc.time }})
              </p>
              <p v-else-if="designDetail.subStatus < DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-black">未上傳</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.Design3DAgree"
                 class=" font-bold md:text-lg text-red-600">未上傳</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsUploaded"
                 class=" font-bold md:text-lg text-blue-600">第{{ designDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ designDetail.latestConstructionAmountDoc.time }})</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsAccepted"
                 class=" font-bold md:text-lg text-black">第{{ designDetail.latestConstructionAmountDoc.count + 1 }}次
                ({{ designDetail.latestConstructionAmountDoc.time }})</p>
              <p v-else-if="designDetail.subStatus === DesignOrderSubStatusEnum.ConstructionAmountDocsRequest"
                 class=" font-bold md:text-lg text-red-600">使用者要求修正</p>
            </template>
          </div>
        </div>
      </div>
    </div>

    <Customer2D :model-value="designDetail.designContent.design2D" />
    <Customer3D :model-value="designDetail.designContent.design3D" />
    <CustomerAmountDocs :model-value="designDetail.constructionFee" />
    <CustomerHouseInfoForStep2 v-model:house-info="designDetail.measureContent.houseInfo"
                               v-model:publish-info="designDetail.publishInfo"
                               v-model:name-address="designDetail.nameAddress" />
    <CustomerHousePhoto :model-value="designDetail.measureContent.photos" />
    <CustomerHouseCheck :model-value="designDetail.measureContent.houseCheck" />
    <CustomerFloorPlan :model-value="designDetail.measureContent.floorPlan" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.constructionRequest.content.length > 0 || new Date(designDetail.measureContent.constructionRequest.updateTime) > new Date(0)" title="裝潢需求" :data="designDetail.measureContent.constructionRequest" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.waterQuality.content.length > 0 || new Date(designDetail.measureContent.waterQuality.updateTime) > new Date(0)" title="水質檢測" :data="designDetail.measureContent.waterQuality" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.airQuality.content.length > 0 || new Date(designDetail.measureContent.airQuality.updateTime) > new Date(0)" title="空氣檢測" :data="designDetail.measureContent.airQuality" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.noise.content.length > 0 || new Date(designDetail.measureContent.noise.updateTime) > new Date(0)" title="噪音檢測" :data="designDetail.measureContent.noise" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.humidity.content.length > 0 || new Date(designDetail.measureContent.humidity.updateTime) > new Date(0)" title="濕度檢測" :data="designDetail.measureContent.humidity" />
    <CustomerStep1TextContent v-if="designDetail.measureContent.radiation.content.length > 0 || new Date(designDetail.measureContent.radiation.updateTime) > new Date(0)" title="電磁輻射" :data="designDetail.measureContent.radiation" />
    <CustomerHouseNote :model-value="designDetail.measureContent.note" />
  </div>

  <DefaultModal title="設計師報價" :show-close-button="true" :click-outside-close="true" ref="designNotAssignModalRef"
                @closeModal="designNotAssignModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">因網頁無法自動刷新</p>
        <p class="font-bold md:text-lg">請在家易App獲得即時報價</p>
        <p class="font-bold md:text-lg">超過百位設計師方便您比價</p>
        <p class="font-bold md:text-lg">找尋時間：<span>{{ elapsedMinutes }} 分鐘 {{ elapsedSeconds }} 秒</span></p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <DefaultModal title="刪除訂單" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
                ref="deleteModalRef" @closeModal="deleteModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">確認刪除此訂單?</p>
        <p class="font-bold md:text-lg">
          若超過三次會被封鎖帳號!</p>
        <button type="button"
                class="button-basic w-full mt-2 ring-1 bg-red-300 ring-inset ring-gray-300 hover:bg-red-400"
                @click="deleteOrder()">確認刪除訂單
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="同意2D室內設計撥款" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
                ref="design2DCheckModalRef" @closeModal="design2DCheckModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">請確認2D室內設計已完成</p>
        <p class="font-bold md:text-lg">同意撥款 {{ designDetail.design2DMoney }}</p>
        <p class="font-bold md:text-lg">確認後將款項匯入至設計師帳戶</p>
        <div class="flex max-md:flex-col w-full gap-2">
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="check2D(false)">要求修正
          </button>
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="check2D(true)">同意撥款
          </button>
        </div>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="同意3D模型設計撥款" :show-close-button="true" :click-outside-close="false" modal-width="max-w-md"
                ref="design3DCheckModalRef" @closeModal="design3DCheckModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">請確認3D模型設計已完成</p>
        <p class="font-bold md:text-lg">同意撥款 {{ designDetail.design3DMoney }}</p>
        <p class="font-bold md:text-lg">確認後將款項匯入至設計師帳戶</p>
        <div class="flex max-md:flex-col w-full gap-2">
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="check3D(false)">要求修正
          </button>
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="check3D(true)">同意撥款
          </button>
        </div>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="要求修正報價或結束訂單" :show-close-button="true" :click-outside-close="false"
                modal-width="max-w-lg"
                ref="amountCheckModalRef" @closeModal="amountCheckModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg text-start">請確認同意此設計師上傳的裝潢施工報價清單</p>
        <p class="font-bold md:text-lg">同意後將完成訂單</p>
        <p class="font-bold md:text-lg">如果您無法同意此設計師的報價清單</p>
        <p class="font-bold md:text-lg">可以找其他裝潢施工團隊進行比價</p>
        <div class="flex max-md:flex-col w-full gap-2">
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="rejectAmount()">要求修正報價
          </button>
          <button type="button"
                  class="button-basic w-full mt-2 ring-1 bg-gray-200 ring-inset ring-gray-300 hover:bg-gray-300"
                  @click="agreeAmount()">結束訂單
          </button>
        </div>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal title="Step2 室內設計已完成" :show-close-button="true" :click-outside-close="true"
                ref="completeModalRef"
                @close-modal="closeCompleteModal()">
    <div class="flex flex-col items-center text-black">
      <img src="/vectors/customerStep/step2.svg" alt="Step2" class="w-32 h-32 md:w-40 md:h-40 mb-2">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">恭喜你已完成室內設計</p>
        <p class="font-bold md:text-lg">請到App刊登裝潢施工細節</p>
        <p class="font-bold md:text-lg">如果要選擇相同的設計師來裝潢施工</p>
        <p class="font-bold md:text-lg">請等待此設計師的報價細節</p>
        <p class="font-bold md:text-lg max-md:text-start">您也可以使用比價報價服務找新的設計師</p>
        <p class="font-bold md:text-lg">但目前的設計師就無法承接裝潢施工</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <DefaultModal title="Step2 室內設計已完成" :show-close-button="true" :click-outside-close="true"
                ref="fullCompleteModalRef"
                @close-modal="closeFullCompleteModal()">
    <div class="flex flex-col items-center text-black">
      <img src="/vectors/customerStep/step2.svg" alt="Step2" class="w-32 h-32 md:w-40 md:h-40 mb-2">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">恭喜你已完成室內設計</p>
        <p class="font-bold md:text-lg">請到App刊登裝潢施工細節</p>
        <p class="font-bold md:text-lg max-md:text-start">並簽署合約、確認驗收流程、上傳匯款資訊</p>
        <p class="font-bold md:text-lg">來確保您的權益 </p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <GoToAppModal :title="goToAppModalProps.title" :content="goToAppModalProps.content" ref="goToAppModalRef" />
  <AppUsePhoneModal :user-type="UserTypeEnum.Customer" ref="appUsePhoneModalRef" />
  <CustomerSignModal :title="signModalTitle" ref="signModal" />
  <CustomerRemitModal :title="remitModalTitle" ref="remitModal" />
  <NoteBook :room-id="orderData.chatRoomId" :is-designer="false" ref="noteBookRef" />
  <AndroidNotSupportSharedWorkerModal :is-designer="false" ref="sharedWorkerNotSupportModalRef" />
  <CustomerRateModal :order-type="OrderTypeEnum.Design"
                     :deisgner-name="orderData.designerName"
                     :deisgner-avatar="orderData.designerAvatar"
                     :order-rating-id="orderData.orderRatingId"
                     ref="rateModalRef" />
</template>
