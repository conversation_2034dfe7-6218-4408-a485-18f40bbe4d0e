<script setup lang="ts">
import { ref } from 'vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ProcessKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import WorkProcessModalContent
  from '@/components/Order/OrderDetail/UpdateModal/ModalContent/WorkProcessModalContent.vue';
import { toastInfo } from '@/utils/toastification.ts';

const props = defineProps<{ title: string; orderId: string; }>();
const workProcess = defineModel<ProcessKeyItem>('workProcess', { required: true });
const workAmount = defineModel<string>('workAmount', { required: true });
const isAnyWorkChecking = defineModel<boolean>('isAnyWorkChecking', { required: true });

const emits = defineEmits(['refreshData']);
const isApiLoading = ref(false);

const updateModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  updateModalRef.value?.openModal();
};

const closeModal = () => {
  if (isApiLoading.value) {
    toastInfo('請等待檔案上傳完成');
  } else {
    updateModalRef.value?.closeModal();
  }
};

const changeApiLoading = (isLoading: boolean) => {
  isApiLoading.value = isLoading;
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="props.title" ref="updateModalRef"
                :click-outside-close="false"
                :showCloseButton="true"
                @closeModal="closeModal">
    <WorkProcessModalContent :order-id="props.orderId"
                             v-model:work-process="workProcess"
                             v-model:work-amount="workAmount"
                             v-model:is-any-work-checking="isAnyWorkChecking"
                             @refresh-data="emits('refreshData')"
                             @change-api-loading="changeApiLoading" />
  </DefaultModal>
</template>
