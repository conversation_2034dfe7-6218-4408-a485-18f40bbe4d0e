import BaseApiService from './baseApiService.ts';
import { BigDataEstimateResponse } from '@/model/response/bigDataResponse.ts';
import { BigDataEstimateRequest } from '@/model/request/bigDataRequest.ts';

export class BigDataService {
  /**
   * @description: 大數據估價
   */
  static async BigDataEstimate(requestData:BigDataEstimateRequest): Promise<BigDataEstimateResponse> {
    const response = await BaseApiService.Customer.post(('/Order/Customer/BigDataEstimate'), requestData);
    return response.data as BigDataEstimateResponse;
  }
}
