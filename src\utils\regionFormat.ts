import { RegionEnum } from '@/model/enum/taiwanRegion.ts';

export const regionEnumToChinese: { [key in RegionEnum]: string } = {
  [RegionEnum.TaipeiCity]: '台北市',
  [RegionEnum.NewTaipeiCity]: '新北市',
  [RegionEnum.KeelungCity]: '基隆市',
  [RegionEnum.TaoyuanCity]: '桃園市',
  [RegionEnum.HsinchuCity]: '新竹市',
  [RegionEnum.HsinchuCounty]: '新竹縣',
  [RegionEnum.MiaoliCounty]: '苗栗縣',
  [RegionEnum.TaichungCity]: '台中市',
  [RegionEnum.ChanghuaCounty]: '彰化縣',
  [RegionEnum.NantouCounty]: '南投縣',
  [RegionEnum.YunlinCounty]: '雲林縣',
  [RegionEnum.ChiayiCity]: '嘉義市',
  [RegionEnum.ChiayiCounty]: '嘉義縣',
  [RegionEnum.TainanCity]: '台南市',
  [RegionEnum.KaohsiungCity]: '高雄市',
  [RegionEnum.PingtungCounty]: '屏東縣',
  [RegionEnum.YilanCounty]: '宜蘭縣',
  [RegionEnum.HualienCounty]: '花蓮縣',
  [RegionEnum.TaitungCounty]: '台東縣',
  [RegionEnum.PenghuCounty]: '澎湖縣',
  [RegionEnum.KinmenCounty]: '金門縣',
  [RegionEnum.LienchiangCounty]: '連江縣'
};

export const regionEnumArrayToChinese = (regionEnums: RegionEnum[]): string[] => {
  return regionEnums.map(region => regionEnumToChinese[region]);
};
