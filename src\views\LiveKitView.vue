<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import {
  ConnectionQuality,
  LocalParticipant,
  LocalTrackPublication,
  Participant,
  ParticipantEvent,
  RemoteParticipant,
  RemoteTrackPublication,
  Room,
  RoomEvent,
  Track,
  TrackPublication
} from 'livekit-client';
import { useRoute, useRouter } from 'vue-router';
import ChatRoom from '@/views/ChatRoom.vue';
import { useChatHubStore, useMeetTokenLogStore } from '@/stores/global.ts';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import { IsMobile } from '@/utils/hezParameters.ts';

const participantsList = reactive<renderParticipantList[]>([]);
let screenShareData = reactive<{
  isSharingScreen: boolean;
  identity: string;
  screenTrack: Track | undefined;
}>({
  isSharingScreen: false,
  identity: '',
  screenTrack: undefined
});

interface renderParticipantList {
  avatarUrl: string;
  userName: string;
  isMicrophoneEnabled: boolean;
  isCameraEnabled: boolean;
  isSharingScreen: boolean;
  isSpeaking: boolean;
  identity: string;
  videoTrack: Track | undefined;
  audioTrack: Track | undefined;
  screenTrack: Track | undefined;
  isLocal: boolean;
  quality?: ConnectionQuality;
}

const route = useRoute();
const router = useRouter();
const meetTokenLogStore = useMeetTokenLogStore();
const chatHubStore = useChatHubStore();
const meetToken = route.params.meetToken as string;
const roomId = route.params.roomId as string;
const isDesigner = meetTokenLogStore.meetTokenLog?.find(log => log.meetToken === meetToken)?.userType === UserTypeEnum.Designer;
const liveKitUrl = import.meta.env.VITE_LIVE_KIT_URL;
const expiryTime = ref<string>('');
const hasAudioInput = ref<boolean>(false);
const hasVideoInput = ref<boolean>(false);
const supportsScreenShare = ref(false);

const updateParticipant = (participant: Participant) => {
  //判斷現有的參與者是否已經存在TestParticipantsList，如果存在就不要push改用更新的
  let JsonAvatar = '';
  if (participant.metadata !== undefined) {
    JsonAvatar = JSON.parse(participant.metadata).avatar;
  }

  //更新參與者總表
  const index = participantsList.findIndex(p => p.identity === participant.identity);
  if (index === -1) {
    console.log('push participant', participant.name);
    const participantData = {
      avatarUrl: JsonAvatar,
      userName: participant.name ? participant.name : '找不到名稱',
      isMicrophoneEnabled: participant.isMicrophoneEnabled,
      isCameraEnabled: participant.isCameraEnabled,
      isSharingScreen: participant.isScreenShareEnabled,
      isSpeaking: participant.isSpeaking,
      identity: participant.identity,
      videoTrack: participant.getTrackPublication(Track.Source.Camera)?.track,
      audioTrack: participant.getTrackPublication(Track.Source.Microphone)?.track,
      screenTrack: participant.getTrackPublication(Track.Source.ScreenShare)?.track,
      isLocal: participant.isLocal,
      quality: participant.connectionQuality
    };
    participantsList.push(participantData);
  } else {
    console.log('update participant', participant);
    participantsList[index] = {
      avatarUrl: JsonAvatar,
      userName: participant.name ? participant.name : '找不到名稱',
      isMicrophoneEnabled: participant.isMicrophoneEnabled,
      isCameraEnabled: participant.isCameraEnabled,
      isSharingScreen: participant.isScreenShareEnabled,
      isSpeaking: participant.isSpeaking,
      identity: participant.identity,
      videoTrack: participant.getTrackPublication(Track.Source.Camera)?.track,
      audioTrack: participant.getTrackPublication(Track.Source.Microphone)?.track,
      screenTrack: participant.getTrackPublication(Track.Source.ScreenShare)?.track,
      isLocal: participant.isLocal,
      quality: participant.connectionQuality
    };
  }
  // 處理螢幕分享邏輯
  if (participant.isScreenShareEnabled) {
    if (!screenShareData.identity || screenShareData.identity === participant.identity) {
      screenShareData = {
        isSharingScreen: participant.isScreenShareEnabled,
        identity: participant.identity,
        screenTrack: participant.getTrackPublication(Track.Source.ScreenShare)?.track
      };
    }
  } else if (screenShareData.identity === participant.identity) {
    const nextScreenSharer = participantsList.find(p => p.isSharingScreen);
    if (nextScreenSharer) {
      screenShareData = {
        isSharingScreen: nextScreenSharer.isSharingScreen,
        identity: nextScreenSharer.identity,
        screenTrack: nextScreenSharer.screenTrack
      };
    } else {
      resetShareScreen();
    }
  }
};

const resetShareScreen = () => {
  screenShareData = {
    isSharingScreen: false,
    identity: '',
    screenTrack: undefined
  };
};

const room = new Room({
  adaptiveStream: false, //要設定為 false 避免 LiveKit 自動調整手機桌面分享過來的影音品質 會變糊240p
  dynacast: true
});

const connectToRoom = async (meetToken: string) => {
  await room.prepareConnection(liveKitUrl, meetToken);
  setupEventListeners(room);
  await room.connect(liveKitUrl, meetToken);
};

const setupEventListeners = (room: Room) => {
  room
    .on(RoomEvent.Connected, handleRoomConnected)
    .on(RoomEvent.Disconnected, handleRoomDisconnected)
    .on(RoomEvent.Reconnected, handleRoomDisconnected)
    .on(RoomEvent.TrackSubscribed, handleTrackSubscribed)
    .on(RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed)
    .on(RoomEvent.TrackUnpublished, handleTrackUnpublished)
    .on(RoomEvent.TrackPublished, handleTrackPublished)
    .on(RoomEvent.ParticipantConnected, handleParticipantConnected)
    .on(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected)
    .on(RoomEvent.LocalTrackPublished, handleLocalTrackPublished)
    .on(RoomEvent.LocalTrackUnpublished, handleLocalTrackUnpublished)
    .on(RoomEvent.ActiveSpeakersChanged, handleActiveSpeakerChange);
};

const handleRoomConnected = async () => {
  if (room.metadata !== undefined) {
    expiryTime.value = JSON.parse(room.metadata).expiryTime;
  }
  //先偵測是否有可用的麥克風或鏡頭裝置 & 是否支援桌面分享
  const devices = await navigator.mediaDevices.enumerateDevices();
  hasAudioInput.value = devices.some(device => device.kind === 'audioinput');
  hasVideoInput.value = devices.some(device => device.kind === 'videoinput');
  supportsScreenShare.value = navigator.mediaDevices && typeof navigator.mediaDevices.getDisplayMedia === 'function';

  // room連線之後要去抓取房間內的參與者資訊
  handleParticipantConnected(room.localParticipant);

  room.remoteParticipants.forEach(participant => {
    handleParticipantConnected(participant);
  });

};

const handleTrackSubscribed = (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
  // 遠端'第一次'開啟螢幕分享訂閱
  console.log('track subscribed', participant, publication, track.source, participant.name);
  updateParticipant(participant);
};

// 目前用不到
const handleTrackUnsubscribed = (track: Track, publication: RemoteTrackPublication, participant: RemoteParticipant) => {
  console.log('tack Unsubscribed', participant, publication, track.source, participant.isScreenShareEnabled);
};

// LiveKit官方套件有Bug，遠端參與者開啟螢幕分享要用這個方法
const handleTrackPublished = (publication: RemoteTrackPublication, participant: RemoteParticipant) => {
  console.log('track published', publication.isSubscribed, participant.isScreenShareEnabled);
  if (publication.source === Track.Source.ScreenShare && publication.track) {
    participant.emit(ParticipantEvent.TrackSubscribed, publication.track, publication);
  }
};

// LiveKit官方套件有Bug，遠端參與者關閉螢幕分享要用這個方法
const handleTrackUnpublished = (publication: RemoteTrackPublication, participant: RemoteParticipant) => {
  console.log('track unpublished', publication.isSubscribed, participant.isScreenShareEnabled);
  updateParticipant(participant);
};

const handleLocalTrackPublished = (localTrack: LocalTrackPublication, participant: LocalParticipant) => {
  // 本地開啟螢幕分享
  console.log('local track published', localTrack.source, participant.name);
  updateParticipant(participant);
};

const handleLocalTrackUnpublished = (localTrack: LocalTrackPublication, participant: LocalParticipant) => {
  // 本地關閉螢幕分享
  console.log('local track unpublished', localTrack.source, participant.name);
  updateParticipant(participant);
  if (localTrack.source === Track.Source.ScreenShare && screenShareData.identity === participant.identity) {
    const nextScreenSharer = participantsList.find(p => p.isSharingScreen);
    if (nextScreenSharer) {
      screenShareData = nextScreenSharer;
    } else {
      resetShareScreen();
    }
  }
};

const handleParticipantConnected = (participant: Participant) => {
  console.log(participant.name + '已加入會議');
  updateParticipant(participant);
  participant
    .on(ParticipantEvent.TrackMuted, (trackPub: TrackPublication) => {
      const type = trackPub.source;
      const index = participantsList.findIndex(p => p.identity === participant.identity);
      if (index !== -1) {
        if (type === Track.Source.Microphone) {
          participantsList[index].isMicrophoneEnabled = false;
          console.log(participant.name, 'Microphone muted', participant.isMicrophoneEnabled);
        } else if (type === Track.Source.Camera) {
          participantsList[index].isCameraEnabled = false;
          console.log(participant.name, 'Camera muted', participant.isCameraEnabled);
        }
      }
    })
    .on(ParticipantEvent.TrackUnmuted, (trackPub: TrackPublication) => {
      const type = trackPub.source;
      const index = participantsList.findIndex(p => p.identity === participant.identity);
      if (index !== -1) {
        if (type === Track.Source.Microphone) {
          participantsList[index].isMicrophoneEnabled = true;
          console.log(participant.name, 'Microphone muted', participant.isMicrophoneEnabled);
        } else if (type === Track.Source.Camera) {
          participantsList[index].isCameraEnabled = true;
          console.log(participant.name, 'Camera muted', participant.isCameraEnabled);
        }
      }
    })
    .on(ParticipantEvent.ConnectionQualityChanged, (quality: ConnectionQuality) => {
      //TODO 這邊要去更新參與者的連線品質
      console.log(participant.name, 'quality', quality);
    });
};

const handleParticipantDisconnected = (participant: RemoteParticipant) => {
  const index = participantsList.findIndex(p => p.identity === participant.identity);
  if (index !== -1) {
    participantsList.splice(index, 1);
  }
  console.log(participant.name + '已離開會議');
  // toastInfo(participant.name + '已離開會議');
};

const handleRoomDisconnected = () => {
  if (IsMobile) {
    router.back();
  } else {
    window.close();
  }
};

const handleActiveSpeakerChange = (speakers: Participant[]) => {
  // 輪詢整個participantsList找出跟speakers相同的id，有找出來的設定 isSpeaking = true，沒找出來的就 isSpeaking = false
  participantsList.forEach(participant => {
    participant.isSpeaking = speakers.some(speaker => speaker.identity === participant.identity);
  });
};

// 這邊是影音視訊流的 HTML 沾黏器
const attachTrack = (el: HTMLMediaElement, track: Track) => {
  if (el && track) {
    const mediaStream = el.srcObject as MediaStream;
    const currentTrack = mediaStream ? mediaStream.getVideoTracks()[0] : null;
    if (!currentTrack || currentTrack !== track.mediaStreamTrack) {
      console.log('attach track', el, track);
      el.srcObject = new MediaStream([track.mediaStreamTrack]);
      el.play().catch(console.error);
    }
  }
};

const openMic = async () => {
  await room.localParticipant.setMicrophoneEnabled(true);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isMicrophoneEnabled = true;
};

const closeMic = async () => {
  await room.localParticipant.setMicrophoneEnabled(false);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isMicrophoneEnabled = false;
};

const openCam = async () => {
  await room.localParticipant.setCameraEnabled(true);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isCameraEnabled = true;
};

const closeCam = async () => {
  await room.localParticipant.setCameraEnabled(false);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isCameraEnabled = false;
};

const shareScreen = async () => {
  await room.localParticipant.setScreenShareEnabled(true);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isSharingScreen = true;
};

const stopShareScreen = async () => {
  await room.localParticipant.setScreenShareEnabled(false);
  const local = room.localParticipant;
  const index = participantsList.findIndex(p => p.identity === local.identity);
  participantsList[index].isSharingScreen = false;
};

const leaveMeeting = () => {
  room.disconnect();
};

const localIndex = computed(() => participantsList.findIndex(p => p.isLocal));

// 會議時間倒數計時
const intervalId = ref<number | null>(null);
const formattedMeetingDuration = ref('');

const startMeetingCountdown = () => {
  const expireTime = new Date(expiryTime.value).getTime();

  intervalId.value = setInterval(() => {
    const now = Date.now();
    const duration = Math.floor((expireTime - now) / 1000);

    if (duration <= 0) {
      stopMeetingCountdown();
      formattedMeetingDuration.value = '00:00';
      return;
    }

    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    formattedMeetingDuration.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, 1000) as unknown as number;
};

const stopMeetingCountdown = () => {
  if (intervalId.value !== null) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
  formattedMeetingDuration.value = '';
};

watch(() => expiryTime.value, (newVal) => {
  if (newVal !== '') {
    startMeetingCountdown();
  } else {
    stopMeetingCountdown();
  }
});
// 會議時間進行到數計時

onMounted(async () => {
  await connectToRoom(meetToken);
});

onUnmounted(() => {
  stopMeetingCountdown();
  if (room) {
    room.disconnect();
  }
});
</script>

<template>
  <div class="flex max-lg:flex-col max-w-[1880px] gap-x-8 mx-auto">
    <div class="flex flex-col gap-y-2 p-2 h-screen w-full">

      <div class="flex p-2 items-center justify-between gap-2">
        <button class="bg-red-300 p-4 text-base button-basic text-black items-center hover:bg-red-400"
                @click="leaveMeeting()">
          <span class="font-bold">離開會議</span>
        </button>
        <div class="flex items-center gap-x-2">
          <!--          桌面分享-->
          <div class="bg-color-secondary rounded-lg py-1 px-2">
            <button
              v-if="!supportsScreenShare || (screenShareData.isSharingScreen && !participantsList[localIndex]?.isSharingScreen)"
              class="rounded-lg button-basic text-black items-center cursor-not-allowed">
              <img src="/vectors/liveKit/screen_none.svg" alt="uploadPortfolio" class="w-8 h-8" />
            </button>
            <button v-else-if="!participantsList[localIndex]?.isSharingScreen"
                    class="rounded-lg button-basic text-black items-center hover:bg-color-selected"
                    @click="shareScreen()">
              <img src="/vectors/liveKit/screen_off.svg" alt="uploadPortfolio" class="w-8 h-8" />
            </button>
            <button v-else
                    class="rounded-lg button-basic text-black items-center hover:bg-color-selected"
                    @click="stopShareScreen()">
              <img src="/vectors/liveKit/screen_on.svg" alt="uploadPortfolio" class="w-8 h-8" />
            </button>
          </div>
          <p class="bg-gray-600 p-4 rounded-md text-white">{{ formattedMeetingDuration }}</p>
        </div>
      </div>

      <!--桌面分享-->
      <div v-if="screenShareData.isSharingScreen" class="flex items-center justify-center h-5/6">
        <video class="md:h-[550px] h-80"
               :ref="(el) => attachTrack(el as HTMLVideoElement, screenShareData?.screenTrack as Track)"
               autoplay playsinline muted></video>
      </div>
      <div v-else class="cursor-pointer cus-border flex items-center h-5/6" @click="shareScreen()">
        <img src="/vectors/liveKit/share_screen_brown.svg" alt="" class="w-full md:h-96 h-80" />
      </div>

      <!--      參與者列表-->
      <div class="flex w-full shrink-0 gap-2 overflow-x-auto">
        <div v-for="participant in participantsList" :key="participant.identity"
             :class="['bg-gray-200 flex flex-col gap-y-2 rounded p-2 md:w-40 w-32 md:min-w-40 min-w-32 justify-center min-h-40 border-4 box-border',participant.isSpeaking ? 'border-green-500' : 'border-gray-200']"
        >
          <!--沒開鏡頭-->
          <div class="flex flex-col gap-y-2 items-center h-full justify-center"
               v-if="!participant.isCameraEnabled">
            <img v-if="participant.avatarUrl" :src="participant.avatarUrl" alt="avatar"
                 class="md:w-20 md:h-20 w-10 h-10 rounded-full">
            <img v-else src="/vectors/general/avatar.svg" alt="avatar"
                 class="md:w-20 md:h-20 w-10 h-10 rounded-full">

            <div class="flex gap-x-2 items-center">
              <p class="font-bold">{{ participant.userName }}</p>
              <img src="/vectors/liveKit/mic_on.svg" alt="" class="w-6 h-6"
                   v-if="participant.isMicrophoneEnabled" />
              <img src="/vectors/liveKit/mic_off.svg" alt="" class="w-6 h-6"
                   v-else />
            </div>
          </div>
          <!--有開鏡頭-->
          <div class="flex flex-col gap-y-2" v-else>
            <div class="flex flex-col gap-y-2 ">
              <video
                :ref="(el) => attachTrack(el as HTMLVideoElement, participant.videoTrack as Track)" autoplay
                playsinline class="w-full max-h-32 object-contain "></video>
            </div>
            <div class="items-center justify-center flex gap-x-2 p-2">
              <p class="font-bold">{{ participant.userName }}</p>
              <img src="/vectors/liveKit/mic_on.svg" alt="" class="w-6 h-6"
                   v-if="participant.isMicrophoneEnabled" />
              <img src="/vectors/liveKit/mic_off.svg" alt="" class="w-6 h-6"
                   v-else />
            </div>
          </div>
          <audio v-if="!participant.isLocal"
                 :ref="(el) => attachTrack(el as HTMLAudioElement, participant.audioTrack as Track)" autoplay></audio>
        </div>
      </div>

      <!--      功能列表-->
      <div class="flex p-2 bg-color-secondary rounded-xl items-center justify-center gap-2">
        <!--        麥克風-->
        <div>
          <button v-if="!hasAudioInput"
                  class="rounded-xl button-basic text-black items-center cursor-not-allowed">
            <img src="/vectors/liveKit/mic_none.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
          <button v-else-if="!participantsList[localIndex]?.isMicrophoneEnabled"
                  class="rounded-xl button-basic text-black items-center hover:bg-color-selected"
                  @click="openMic()">
            <img src="/vectors/liveKit/mic_off.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
          <button v-else
                  class="rounded-xl button-basic text-black items-center hover:bg-color-selected"
                  @click="closeMic()">
            <img src="/vectors/liveKit/mic_on.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
        </div>
        <!--        視訊鏡頭-->
        <div>
          <button v-if="!hasVideoInput"
                  class="rounded-xl button-basic text-black items-center cursor-not-allowed">
            <img src="/vectors/liveKit/cam_none.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
          <button v-else-if="!(participantsList[localIndex]?.isCameraEnabled)"
                  class="rounded-xl button-basic text-black items-center hover:bg-color-selected"
                  @click="openCam()">
            <img src="/vectors/liveKit/cam_off.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
          <button v-else
                  class="rounded-xl button-basic text-black items-center hover:bg-color-selected"
                  @click="closeCam()">
            <img src="/vectors/liveKit/cam_on.svg" alt="uploadPortfolio" class="w-8 h-8" />
          </button>
        </div>

      </div>
    </div>

    <div v-if="roomId && chatHubStore._worker !== null"
         class="h-screen max-lg:h-1/2 p-1 max-lg:w-full w-1/3 ">
      <ChatRoom :is-for-meet="true" :room-id="roomId"
                :is-designer="isDesigner" ref="chatRoomRef" />
    </div>
  </div>

</template>
