# 家易前台網頁

## 開發文件

### [正式版網址](https://www.homeeasy.app/)
### [測試版網址](https://www-dev.homeeasy.app/)
### [HomeEasy 後台網頁](https://manage.homeeasy.app)
### [Swagger API 文件](https://api.homeeasy.app/swagger/index.html)
### [Async API 文件](https://api.homeeasy.app/asyncapi/docs/index.html)
### [Jenkins - Production](https://ci2.bluenet-ride.com/view/HomeEasy/job/HEZ-Web-AWS/)
### [<PERSON> - Develop](https://ci2.bluenet-ride.com/view/HomeEasy/job/HEZ-Web-DEV-AWS/)
### [Tailwind CSS 官方文檔](https://tailwindcss.com/docs/installation)
### [Tailwind CSS 語法對照網站](https://rogden.github.io/tailwind-config-viewer/)
### [LiveKit - JS SDK 官方文檔](https://docs.livekit.io/client-sdk-js/)
### [SignalR - JS Client 官方文檔](https://learn.microsoft.com/zh-tw/aspnet/signalr/overview/guide-to-the-api/hubs-api-guide-javascript-client)

## 開發工具版本

- Node.js: v18.18.0
- yarn: v1.22.19

## 專案建置

### 安裝相依套件
```sh
yarn install
```

### 開發期間測試網頁

```sh
yarn dev:dev
```

### 建置網頁，會包含檢查錯誤，建議 Commit 改動前執行一次。

```sh
yarn build:dev
```

### 預渲染
```sh
yarn global add serve

```


## 開發 Coding Style 規則

* 名稱不要包含縮寫，除非是廣為人知的縮寫，例如：`API`、`URL`、`SVG`
* 魔法常數(const)：大駝峰式命名，通常會整理在 `src/utils/hezParameters` 資料夾
* 常數(const)、變數(let)：小駝峰式命名
* 函數(function)、箭頭函數 (arrow functions)：小駝峰式命名
* class：大駝峰式命名
* Enum：大駝峰式命名，後綴加上Enum，例如：`enum MediaTypeEnum`
* interface：大駝峰式命名
* type：大駝峰式命名

---
