<script setup lang="ts">

</script>

<template>
  <div class="flex justify-center p-2 rounded-lg">
    <div class="flex flex-col gap-y-4 md:m-3">
      <div class="flex max-md:flex-col mt-1 justify-center items-center text-color-secondary gap-4">
        <div>
          <a href="https://play.google.com/store/apps/details?id=lab.homeeasy.designer"
             target="_blank">
            <img src="/vectors/general/AndroidDownload.svg" alt="Android download" class="hover-zoom w-60" />
          </a>
        </div>
        <div>
          <a href="https://apps.apple.com/us/app/homeeasy-designer/id6477273359"
             target="_blank">
            <img src="/vectors/general/IOSDownload.svg" alt="IOS download" class="hover-zoom w-60" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
