<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue';
import { DeleteNoteResponse, GetNoteListResponse, NoteRecordItem } from '@/model/response/noteResponse.ts';
import { NoteListService, NoteService } from '@/api/chat.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import NoteUpdate from '@/components/ChatRoom/NoteUpdate.vue';
import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { formatFullDateTime } from '@/utils/timeFormat.ts';
import NoteComment from '@/components/ChatRoom/NoteComment.vue';
import { toastInfo } from '@/utils/toastification.ts';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

const props = defineProps(
  {
    roomId: {
      type: String,
      required: true
    },
    isDesigner: {
      type: Boolean,
      required: true
    }
  }
);
const data = ref<NoteRecordItem[]>([]);
const skip = ref<number>(0);
const reFreshSwiper = ref<boolean>(false);
const updateRef = ref<InstanceType<typeof NoteUpdate> | null>(null);
const commentRef = ref<InstanceType<typeof NoteComment> | null>(null);
const userInfoStore = props.isDesigner ? useDesignerInfoStore() : useCustomerInfoStore();
const isFirstTime = ref<boolean>(true);
const isMoreData = ref<boolean>(true);

const getNoteList = async () => {
  let res: GetNoteListResponse;
  if (props.isDesigner) {
    res = await NoteListService.GetNoteListByDesigner(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  } else {
    res = await NoteListService.GetNoteListByCustomer(
      {
        roomId: props.roomId,
        skip: skip.value,
        limit: 20
      });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    if (res.notes.length < 20) {
      isMoreData.value = false;
    }

    if (res.notes.length === 0) {
      if (!isFirstTime.value) toastInfo('已無更多資料');
      return;
    } else {
      data.value.push(...res.notes);
      skip.value += 20;
    }
  }
};

const addNote = () => {
  updateRef.value?.openModal();
};

const editNote = (note: NoteRecordItem) => {
  updateRef.value?.openModal({
    text: note.text,
    imageUrls: note.medias
  }, note.noteId);
};

const deleteNote = async (noteId: string) => {
  let res: DeleteNoteResponse;
  if (props.isDesigner) {
    res = await NoteService.DeleteNoteByDesigner({
      roomId: props.roomId,
      noteId: noteId
    });
  } else {
    res = await NoteService.DeleteNoteByCustomer({
      roomId: props.roomId,
      noteId: noteId
    });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    data.value = data.value.filter(note => note.noteId !== noteId);
  }
};

const updateNoteResult = async (updateNote: NoteRecordItem) => {
  const index = data.value.findIndex(note => note.noteId === updateNote.noteId);
  if (index === -1) {
    data.value.unshift(updateNote);
  } else {
    reFreshSwiper.value = true;
    data.value[index] = updateNote;
    await nextTick();
    reFreshSwiper.value = false;
  }
};

const openCommentModal = (noteId: string) => {
  commentRef.value?.openModal(noteId);
};

const updateCommentCount = (noteId: string, isPlus: boolean) => {
  const index = data.value.findIndex(note => note.noteId === noteId);
  if (index !== -1) {
    if (isPlus) {
      data.value[index].commentCount++;
    } else {
      data.value[index].commentCount--;
    }
  }
};

const sortedData = computed(() => {
  const sortData = data.value
  return sortData.sort((a, b) => {
    return new Date(b.refreshTime).getTime() - new Date(a.refreshTime).getTime();
  });
});

onMounted(async () => {
  await getNoteList();
  isFirstTime.value = false;
});
</script>

<template>
  <div class="mb-2 flex justify-center">
    <button class="cus-btn w-full mx-4 p-2" @click="addNote()">新增記事本</button>
  </div>
  <div class="w-full text-black" v-if="sortedData.length === 0">
    <p>尚無任何記事</p>
  </div>
  <div class="w-full flex flex-col gap-y-1" v-else>
    <div v-for="note in sortedData" :key="note.noteId"
         class="w-full p-2 gap-y-1 border flex flex-col">
      <div class="flex justify-between items-center">
        <div class="flex gap-x-2 items-center justify-start">
          <img v-if="note.userAvatar" :src="note.userAvatar" alt="note" class="w-12 h-12 rounded-full">
          <img v-else src="/vectors/general/avatar.svg" alt="avatar" class="w-12 h-12 rounded-full">
          <p>{{ note.username }}</p>
        </div>
        <div v-if="note.userId === userInfoStore.userId" class="relative">
          <Menu as="div">
            <MenuButton class="block text-gray-500 hover:text-gray-900">
              <span class="sr-only">Open options</span>
              <EllipsisVerticalIcon class="h-8 w-8" aria-hidden="true" />
            </MenuButton>
            <transition enter-active-class="transition ease-out duration-100"
                        enter-from-class="transform opacity-0 scale-95"
                        enter-to-class="transform opacity-100 scale-100"
                        leave-active-class="transition ease-in duration-75"
                        leave-from-class="transform opacity-100 scale-100"
                        leave-to-class="transform opacity-0 scale-95">
              <MenuItems
                class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                <MenuItem
                  v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="editNote(note)"
                  >修改記事</a>
                </MenuItem>
                <MenuItem
                  v-slot="{ active }">
                  <a
                    :class="[active ? 'bg-gray-50' : '', 'block px-3 py-1 text-sm leading-6 text-gray-900 cursor-pointer']"
                    @click="deleteNote(note.noteId)"
                  >刪除記事</a>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>
        </div>

      </div>
      <div>
        <swiper-container class="w-full fix-pagination" :pagination="true" space-between="30"
                          v-if="!reFreshSwiper">
          <swiper-slide v-for="(media,index) in note.medias" :key="index"
                        class="bg-center bg-auto">
            <img :src="media" alt=""
                 class="bg-gray-200 object-contain block w-full h-96 max-md:h-64">
          </swiper-slide>
        </swiper-container>
      </div>
      <div>
        <p class="text-start break-words">{{ note.text }}</p>
      </div>
      <div class="flex justify-between">
        <div class="flex gap-x-2 items-center cursor-pointer" @click="openCommentModal(note.noteId)">
          <img src="/vectors/chatRoom/comment.svg" alt="comment" class="w-8 h-8">
          <p>{{ note.commentCount }}</p>
        </div>
        <p>{{ formatFullDateTime(note.refreshTime) }}</p>
      </div>
    </div>
    <p class="cursor-pointer" v-if="isMoreData" @click="getNoteList()">查看更多</p>
  </div>

  <NoteComment :room-id="props.roomId" :is-designer="isDesigner" @update-comment-count="updateCommentCount"
               ref="commentRef" />
  <NoteUpdate :room-id="props.roomId" :is-add-from-chat="false" @note-result="updateNoteResult"
              :is-designer="isDesigner" ref="updateRef" />
</template>
