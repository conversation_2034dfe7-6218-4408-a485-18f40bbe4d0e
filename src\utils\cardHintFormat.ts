import { DesignOrderSubStatusEnum, MeasureOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import { ConstructionCardHintData, DesignCardHintData } from '@/model/formatted/orderCard.ts';
import { OneDayMilliseconds } from '@/utils/hezParameters.ts';

export const measureCardHint = (dateString: string, status: MeasureOrderStatusEnum): string => {
  const targetDate = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const targetDateValue = targetDate.valueOf();
  const todayValue = today.valueOf();
  const oneDay = OneDayMilliseconds;

  if (status === MeasureOrderStatusEnum.WaitingUpload) return '丈量已完成，資料未上傳';

  if (targetDateValue >= todayValue && targetDateValue < todayValue + oneDay) {
    return '今日丈量行程';
  } else if (targetDateValue >= todayValue + oneDay && targetDateValue < todayValue + 2 * oneDay) {
    return '明日丈量行程';
  } else if (targetDateValue >= todayValue + 2 * oneDay && targetDateValue < todayValue + 3 * oneDay) {
    return '後天丈量行程';
  } else {
    return '預約丈量行程';
  }
};

export const designCardHint = (subStatus: DesignOrderSubStatusEnum): DesignCardHintData => {
  switch (subStatus) {
    case DesignOrderSubStatusEnum.NotUploaded:
    case DesignOrderSubStatusEnum.Design2DUploaded:
    case DesignOrderSubStatusEnum.Design2DRequest:
    case DesignOrderSubStatusEnum.Design2DReject:
      return {
        orderStatus: '已匯款',
        twoDStatus: '進行中',
        threeDStatus: '未完成',
        constructionAmountListStatus: '未完成'
      };
    case DesignOrderSubStatusEnum.Design2DAgree:
    case DesignOrderSubStatusEnum.Design3DUploaded:
    case DesignOrderSubStatusEnum.Design3DRequest:
    case DesignOrderSubStatusEnum.Design3DReject:
      return {
        orderStatus: '已匯款',
        twoDStatus: '已完成',
        threeDStatus: '進行中',
        constructionAmountListStatus: '未完成'
      };
    case DesignOrderSubStatusEnum.Design3DAgree:
    case DesignOrderSubStatusEnum.ConstructionAmountDocsUploaded:
    case DesignOrderSubStatusEnum.ConstructionAmountDocsRequest:
      return {
        orderStatus: '已匯款',
        twoDStatus: '已完成',
        threeDStatus: '已完成',
        constructionAmountListStatus: '進行中'
      };
    case DesignOrderSubStatusEnum.ConstructionAmountDocsAccepted:
      return {
        orderStatus: '已匯款',
        twoDStatus: '已完成',
        threeDStatus: '已完成',
        constructionAmountListStatus: '已完成'
      };
    default: {
      return {
        orderStatus: '已匯款',
        twoDStatus: '',
        threeDStatus: '',
        constructionAmountListStatus: ''
      };
    }
  }
};

export const constructionCardHint = (): ConstructionCardHintData => {
  return {
    orderTakeStatus: '已接單',
    workStatus: '工作中'
  };
};
