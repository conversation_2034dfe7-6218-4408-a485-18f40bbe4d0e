<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';
import DesignerAppDownload from '@/components/General/DesignerAppDownload.vue';

defineProps<{ title: string; }>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal :title="title" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="ModalRef" @closeModal="ModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">目前雙方尚未完成合約簽署</p>
        <p class="font-bold md:text-lg">請確定雙方合約簽署完成後</p>
        <p class="font-bold md:text-lg">才開始著手設計</p>
        <p class="font-bold md:text-lg">避免做白工的情況發生</p>
        <p class="font-bold md:text-lg">請到App簽屬合約</p>
      </div>
      <DesignerAppDownload />
    </div>
  </DefaultModal>
</template>
