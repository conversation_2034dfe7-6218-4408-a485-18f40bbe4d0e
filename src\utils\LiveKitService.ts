import { MeetService } from '@/api/chat.ts';
import { RecordTypeEnum } from '@/model/enum/chatEnum.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import router from '@/router';
import { toastError } from '@/utils/toastification.ts';
import { MeetAnswerResponse, MeetDialResponse } from '@/model/response/chatResponse.ts';
import { IsMobile, IsProduction } from '@/utils/hezParameters.ts';
import { useMeetTokenLogStore } from '@/stores/global.ts';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';

const handleMeetError = (status: APIStatusCodeEnum) => {
  switch (status) {
    case APIStatusCodeEnum.RoomNotFound:
      toastError('聊天室不存在');
      break;
    case APIStatusCodeEnum.RoomMemberAtLeastTwo:
      toastError('會議人數至少2人以上');
      break;
    case APIStatusCodeEnum.SelfBusy:
      toastError('自己忙碌中');
      break;
    case APIStatusCodeEnum.OpponentBusy:
      toastError('對方忙碌中');
      break;
    case APIStatusCodeEnum.CallNotFound:
      toastError('通話不存在');
      break;
  }
};

export const createMeet = async (roomId: string, isDesigner: boolean, userId: string) => {
  const meetTokenLogStore = useMeetTokenLogStore();
  const isRecord = IsProduction ? RecordTypeEnum.Video : RecordTypeEnum.None;
  let res: MeetDialResponse;

  if (isDesigner) {
    res = await MeetService.DialByDesigner({ roomId, recordType: isRecord });
  } else {
    res = await MeetService.DialByCustomer({ roomId, recordType: isRecord });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    meetTokenLogStore.updateMeetTokenLog(res.meetToken, userId, isDesigner ? UserTypeEnum.Designer : UserTypeEnum.Customer);
    const routeData = router.resolve({
      name: 'meeting',
      params: { roomId: roomId, meetToken: res.meetToken }
    });
    if (IsMobile) {
      await router.push(routeData);
    } else {
      window.open(routeData.href, '_blank');
    }

  } else if (res.status === APIStatusCodeEnum.MeetingAlreadyExist) {
    await answerMeet(roomId, isDesigner, userId);
  } else {
    handleMeetError(res.status);
  }
};

export const answerMeet = async (roomId: string, isDesigner: boolean, userId: string) => {
  const meetTokenLogStore = useMeetTokenLogStore();
  let res: MeetAnswerResponse;
  if (isDesigner) {
    res = await MeetService.AnswerByDesigner({ roomId });
  } else {
    res = await MeetService.AnswerByCustomer({ roomId });
  }

  if (res.status === APIStatusCodeEnum.Success) {
    meetTokenLogStore.updateMeetTokenLog(res.meetToken, userId, isDesigner ? UserTypeEnum.Designer : UserTypeEnum.Customer);
    const routeData = router.resolve({
      name: 'meeting',
      params: { roomId: roomId, meetToken: res.meetToken }
    });
    if (IsMobile) {
      await router.push(routeData);
    } else {
      window.open(routeData.href, '_blank');
    }
  } else {
    handleMeetError(res.status);
  }
};

