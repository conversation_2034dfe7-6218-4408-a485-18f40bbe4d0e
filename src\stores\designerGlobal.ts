import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getDesignerGuestToken, SecurityService } from '@/api/security.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode';
import { decryptAES, encryptAES, hashPassword } from '@/utils/crypto';
import { toastError, toastInfo, toastSuccess } from '@/utils/toastification.ts';
import type { LoginData } from '@/model/general/security';
import { deviceInfo } from '@/utils/userDeviceInfo.ts';
import router from '@/router';
import { ProfileVerifyStatusEnum } from '@/model/enum/profileVerify.ts';

export const useDesignerInfoStore = defineStore('DesignerInfo', () => {
    const guestToken = ref('');
    const loginState = ref<boolean>(false);
    const userId = ref<string>('');
    const hasEverLogin = ref<boolean>(false);
    const rememberMe = ref<boolean>(true);
    const loginCipher = ref<{ phone: string; password: string; }>({
      phone: '',
      password: ''
    });

    const getGuestToken = async () => {
      const result = await getDesignerGuestToken(deviceInfo);

      if (result.status !== APIStatusCodeEnum.Success) {
        toastError('取得設計師Token失敗');
      } else {
        guestToken.value = result.token;//會觸發App.vue中的$subscribe
        // 如果已經登入，則重新登入
        if (loginState.value) {
          await login({
            phone: decryptAES(loginCipher.value.phone),
            password: decryptAES(loginCipher.value.password),
            rememberForShow: rememberMe.value
          });
        }
        console.log('已重新取得設計師Token');
      }
    };

    const registerSuccess = (id: string, phone: string, password: string) => {
      userId.value = id;
      loginCipher.value.phone = encryptAES(phone);
      loginCipher.value.password = encryptAES(password);
      loginState.value = true;
      hasEverLogin.value = true;
    };

    const login = async (data: LoginData): Promise<boolean> => {
      const ciphertext = await hashPassword(data.password);
      const loginResult = await SecurityService.userLoginByDesigner({
        phone: data.phone,
        password: ciphertext
      });

      if (loginResult.status === APIStatusCodeEnum.PhoneOrPasswordError) {
        toastError('帳號或密碼錯誤');
        return false;
      }
      loginState.value = true;
      userId.value = loginResult.userId;

      loginCipher.value = {
        phone: encryptAES(data.phone),
        password: encryptAES(data.password)
      };

      rememberMe.value = data.rememberForShow;

      hasEverLogin.value = true;
      toastSuccess('登入成功');
      localStorage.setItem('needToBeRefresh', JSON.stringify(['designer']));
      return true;
    };

    const logout = async () => {
      if (loginState.value === false) {
        console.error('使用者未登入');
        return;
      }
      try {
        await SecurityService.LogoutByDesigner({});
        toastInfo('登出成功');
        clearLoginStore();
        await getGuestToken();
        await router.push({ name: 'designerhome' });
        localStorage.setItem('needToBeRefresh', JSON.stringify(['designer', 'meeting']));
      } catch (error) {
        toastError('登出失敗');
      }
    };

    const clearLoginStore = () => {
      loginState.value = false;
      userId.value = '';
    };

    const clearPassword = () => {
      loginCipher.value = { phone: '', password: '' };
      rememberMe.value = true;
    };

    const clearGuestToken = () => {
      guestToken.value = '';
    };

    return {
      guestToken,
      loginState,
      userId,
      hasEverLogin,
      loginCipher,
      rememberMe,
      getGuestToken,
      registerSuccess,
      login,
      logout,
      clearLoginStore,
      clearPassword,
      clearGuestToken
    };
  },
  {
    persist: true
  }
);

export const useDesignerRedirectStore = defineStore('designerRedirect', () => {
  const redirectRouteName = ref<string>('designerprofile');
  const setRedirectRoute = (route: string) => {
    redirectRouteName.value = route;
  };
  const resetRedirectRoute = () => {
    redirectRouteName.value = 'designerprofile';
  };
  return { redirectRouteName, setRedirectRoute, resetRedirectRoute };
});

export const useDesignerVerifyStore = defineStore('designerVerify', () => {
    const taiwanIdVerify = ref<boolean>(false);
    const companyVerify = ref<boolean>(false);
    const emailVerify = ref<boolean>(false);

    const setTaiwanIdVerify = (status: ProfileVerifyStatusEnum) => {
      taiwanIdVerify.value = status === ProfileVerifyStatusEnum.Success;
    };
    const setCompanyVerify = (status: ProfileVerifyStatusEnum) => {
      companyVerify.value = status === ProfileVerifyStatusEnum.Success;
    };
    const setEmailVerify = (status: boolean) => {
      emailVerify.value = status;
    };

    return {
      taiwanIdVerify,
      companyVerify,
      emailVerify,
      setTaiwanIdVerify,
      setCompanyVerify,
      setEmailVerify
    };

  },
  {
    persist: true
  }
);
