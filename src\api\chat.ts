import BaseApiService from '@/api/baseApiService.ts';
import {
  GetChatRoomMsgRequest,
  GetOneChatRoomRequest,
  MeetAnswerRequest,
  MeetDialRequest, SendChatRoomMsgRequest, UnSendChatRoomMsgRequest
} from '@/model/request/chatRequest.ts';
import {
  ChatRoomResponse, GetChatRoomInfoResponse,
  GetChatRoomMsgResponse,
  MeetAnswerResponse,
  MeetDialResponse, SendMsgResponse, UnSendMsgResponse
} from '@/model/response/chatResponse.ts';
import {
  AddCommentRequest,
  AddNoteRequest, DeleteCommentRequest,
  DeleteNoteRequest,
  EditNoteRequest, GetCommentListRequest,
  GetListRequest,
  GetOneNoteRequest
} from '@/model/request/noteRequest.ts';
import {
  AddCommentResponse,
  DeleteCommentResponse,
  DeleteNoteResponse, GetCommentListResponse,
  GetFileListResponse,
  GetImageListResponse, GetLinkListResponse,
  GetMeetListResponse,
  GetNoteListResponse, OneNoteResponse
} from '@/model/response/noteResponse.ts';

export class MeetService {
  /**
   * @description: 客戶建立多人會議
   */
  static async DialByCustomer(requestData: MeetDialRequest): Promise<MeetDialResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Meet/Call/Dial'), requestData);
    return response.data as MeetDialResponse;
  }

  /**
   * @description: 設計師建立多人會議
   */
  static async DialByDesigner(requestData: MeetDialRequest): Promise<MeetDialResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Meet/Call/Dial'), requestData);
    return response.data as MeetDialResponse;
  }

  /**
   * @description: 客戶進入多人會議
   */
  static async AnswerByCustomer(requestData: MeetAnswerRequest): Promise<MeetAnswerResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Meet/Call/Answer'), requestData);
    return response.data as MeetAnswerResponse;
  }

  /**
   * @description: 設計師進入多人會議
   */
  static async AnswerByDesigner(requestData: MeetAnswerRequest): Promise<MeetAnswerResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Meet/Call/Answer'), requestData);
    return response.data as MeetAnswerResponse;
  }
}

export class ChatService {
  /**
   * @description: 客戶取得單個聊天室
   */
  static async GetOneRoomByCustomer(requestData: GetOneChatRoomRequest): Promise<ChatRoomResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/GetOne'), requestData);
    return response.data as ChatRoomResponse;
  }

  /**
   * @description: 設計師取得單個聊天室
   */
  static async GetOneRoomByDesigner(requestData: GetOneChatRoomRequest): Promise<ChatRoomResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/GetOne'), requestData);
    return response.data as ChatRoomResponse;
  }

  /**
   * @description: 客戶取得單個聊天室訊息
   */
  static async GetOneRoomMsgByCustomer(requestData: GetChatRoomMsgRequest): Promise<GetChatRoomMsgResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Web/GetRoomMessage'), requestData);
    return response.data as GetChatRoomMsgResponse;
  }

  /**
   * @description: 設計師取得單個聊天室訊息
   */
  static async GetOneRoomMsgByDesigner(requestData: GetChatRoomMsgRequest): Promise<GetChatRoomMsgResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Web/GetRoomMessage'), requestData);
    return response.data as GetChatRoomMsgResponse;
  }

  /**
   * @description: 客戶取得單個聊天室會議資訊
   */
  static async GetOneRoomInfoByCustomer(requestData: GetOneChatRoomRequest): Promise<GetChatRoomInfoResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/GetInfo'), requestData);
    return response.data as GetChatRoomInfoResponse;
  }

  /**
   * @description: 設計師取得單個聊天室會議資訊
   */
  static async GetOneRoomInfoByDesigner(requestData: GetOneChatRoomRequest): Promise<GetChatRoomInfoResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/GetInfo'), requestData);
    return response.data as GetChatRoomInfoResponse;
  }

  /**
   * @description: 客戶發送訊息
   */
  static async SendMsgByCustomer(requestData: SendChatRoomMsgRequest): Promise<SendMsgResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Message/Send'), requestData);
    return response.data as SendMsgResponse;
  }

  /**
   * @description: 設計師發送訊息
   */
  static async SendMsgByDesigner(requestData: SendChatRoomMsgRequest): Promise<SendMsgResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Message/Send'), requestData);
    return response.data as SendMsgResponse;
  }

  /**
   * @description: 客戶收回訊息
   */
  static async UnSendMsgByCustomer(requestData: UnSendChatRoomMsgRequest): Promise<UnSendMsgResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Message/Unsend'), requestData);
    return response.data as UnSendMsgResponse;
  }

  /**
   * @description: 設計師收回訊息
   */
  static async UnSendMsgByDesigner(requestData: UnSendChatRoomMsgRequest): Promise<UnSendMsgResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Message/Unsend'), requestData);
    return response.data as UnSendMsgResponse;
  }

}

export class NoteListService {
  /**
   * @description: 客戶取得聊天室會議記錄列表
   */
  static async GetMeetListByCustomer(requestData: GetListRequest): Promise<GetMeetListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Meet/GetList'), requestData);
    return response.data as GetMeetListResponse;
  }

  /**
   * @description: 設計師取得聊天室會議記錄列表
   */
  static async GetMeetListByDesigner(requestData: GetListRequest): Promise<GetMeetListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Meet/GetList'), requestData);
    return response.data as GetMeetListResponse;
  }

  /**
   * @description: 客戶取得聊天室記事本列表
   */
  static async GetNoteListByCustomer(requestData: GetListRequest): Promise<GetNoteListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/GetList'), requestData);
    return response.data as GetNoteListResponse;
  }

  /**
   * @description: 設計師取得聊天室記事本列表
   */
  static async GetNoteListByDesigner(requestData: GetListRequest): Promise<GetNoteListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/GetList'), requestData);
    return response.data as GetNoteListResponse;
  }

  /**
   * @description: 客戶取得聊天室圖片列表
   */
  static async GetImageListByCustomer(requestData: GetListRequest): Promise<GetImageListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Image/GetList'), requestData);
    return response.data as GetImageListResponse;
  }

  /**
   * @description: 設計師取得聊天室圖片列表
   */
  static async GetImageListByDesigner(requestData: GetListRequest): Promise<GetImageListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Image/GetList'), requestData);
    return response.data as GetImageListResponse;
  }

  /**
   * @description: 客戶取得聊天室檔案列表
   */
  static async GetFileListByCustomer(requestData: GetListRequest): Promise<GetFileListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/File/GetList'), requestData);
    return response.data as GetFileListResponse;
  }

  /**
   * @description: 設計師取得聊天室檔案列表
   */
  static async GetFileListByDesigner(requestData: GetListRequest): Promise<GetFileListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/File/GetList'), requestData);
    return response.data as GetFileListResponse;
  }

  /**
   * @description: 客戶取得聊天室連結列表
   */
  static async GetLinkListByCustomer(requestData: GetListRequest): Promise<GetLinkListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Link/GetList'), requestData);
    return response.data as GetLinkListResponse;
  }

  /**
   * @description: 設計師取得聊天室連結列表
   */
  static async GetLinkListByDesigner(requestData: GetListRequest): Promise<GetLinkListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Link/GetList'), requestData);
    return response.data as GetLinkListResponse;
  }
}

export class NoteService {
  /**
   * @description: 客戶取得單個記事本
   */
  static async GetNoteOneByCustomer(requestData: GetOneNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/GetOne'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 設計師取得單個記事本
   */
  static async GetNoteOneByDesigner(requestData: GetOneNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/GetOne'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 客戶新增單個記事本
   */
  static async AddNoteByCustomer(requestData: AddNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Create'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 設計師新增單個記事本
   */
  static async AddNoteByDesigner(requestData: AddNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Create'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 客戶修改單個記事本
   */
  static async EditNoteByCustomer(requestData: EditNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Edit'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 設計師修改單個記事本
   */
  static async EditNoteByDesigner(requestData: EditNoteRequest): Promise<OneNoteResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Edit'), requestData);
    return response.data as OneNoteResponse;
  }

  /**
   * @description: 客戶刪除單個記事本
   */
  static async DeleteNoteByCustomer(requestData: DeleteNoteRequest): Promise<DeleteNoteResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Delete'), requestData);
    return response.data as DeleteNoteResponse;
  }

  /**
   * @description: 設計師刪除單個記事本
   */
  static async DeleteNoteByDesigner(requestData: DeleteNoteRequest): Promise<DeleteNoteResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Delete'), requestData);
    return response.data as DeleteNoteResponse;
  }

  /**
   * @description: 客戶取得單個記事本留言列表
   */
  static async GetCommentListByCustomer(requestData: GetCommentListRequest): Promise<GetCommentListResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Comment/GetList'), requestData);
    return response.data as GetCommentListResponse;
  }

  /**
   * @description: 設計師取得單個記事本留言列表
   */
  static async GetCommentListByDesigner(requestData: GetCommentListRequest): Promise<GetCommentListResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Comment/GetList'), requestData);
    return response.data as GetCommentListResponse;
  }

  /**
   * @description: 客戶新增留言
   */
  static async AddCommentByCustomer(requestData: AddCommentRequest): Promise<AddCommentResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Comment/Create'), requestData);
    return response.data as AddCommentResponse;
  }

  /**
   * @description: 設計師新增留言
   */
  static async AddCommentByDesigner(requestData: AddCommentRequest): Promise<AddCommentResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Comment/Create'), requestData);
    return response.data as AddCommentResponse;
  }

  /**
   * @description: 客戶刪除留言
   */
  static async DeleteCommentByCustomer(requestData: DeleteCommentRequest): Promise<DeleteCommentResponse> {
    const response = await BaseApiService.Customer.post(('/Chat/Room/Record/Note/Comment/Delete'), requestData);
    return response.data as DeleteCommentResponse;
  }

  /**
   * @description: 設計師刪除留言
   */
  static async DeleteCommentByDesigner(requestData: DeleteCommentRequest): Promise<DeleteCommentResponse> {
    const response = await BaseApiService.Designer.post(('/Chat/Room/Record/Note/Comment/Delete'), requestData);
    return response.data as DeleteCommentResponse;
  }
}
