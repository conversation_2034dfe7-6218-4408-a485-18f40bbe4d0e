<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { CustomerBasicOrderMeasureItem } from '@/model/response/customerMeasureOrderResponse.ts';
import {
  CustomerConstructionOrderDetailService,
  CustomerDesignOrderDetailService, CustomerInvoiceDetailService,
  CustomerMeasureOrderDetailService
} from '@/api/customerOrder.ts';
import {
  AcceptanceProcessStatusEnum,
  ConstructionOrderStatusEnum,
  DesignOrderStatusEnum, InvoiceOrderStatusEnum,
  MeasureOrderStatusEnum, RemittanceStatusEnum, SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import { formatFullDateTimeWithDay, formatFullDate } from '@/utils/timeFormat.ts';
import HezDivider from '@/components/General/HezDivider.vue';
import { useRouter } from 'vue-router';
import { CustomerBasicOrderDesignItem } from '@/model/response/customerDesignOrderResponse.ts';
import { CustomerBasicOrderConstructionItem } from '@/model/response/customerConstructionOrderResponse.ts';
import { OrderTypeEnum } from '@/model/enum/orderType.ts';
import { budgetCombineLowToHigh, moneyAddCommas } from '@/utils/budgetFormat.ts';
import { BasicInvoice } from '@/model/response/customerInvoiceResponse.ts';

const measureOrderList = ref<CustomerBasicOrderMeasureItem[]>([]);
const designOrderList = ref<CustomerBasicOrderDesignItem[]>([]);
const constructionOrderList = ref<CustomerBasicOrderConstructionItem[]>([]);
const InvoiceList = ref<BasicInvoice[]>([]);
const router = useRouter();
const isApiLoaded = ref(false);
const orderShow = ref('all');// 使用單選 分三種 所有訂單 進行訂單 歷史訂單 all process history

enum OrderShowTypeEnum {
  Process = 0,
  History = 1,
}

interface allOrderListData {
  orderType: OrderTypeEnum,
  orderId: string,
  refreshTime: string,
  orderStatus: OrderShowTypeEnum,
  measureData?: {
    status: MeasureOrderStatusEnum,
    title: string,
    address: string,
    designerName: string,
    measureTime: string,
    houseInfo: string,
    housePhoto: string,
    houseCheck: string,
    floorPlan: string,
  },
  designData?: {
    status: DesignOrderStatusEnum,
    title: string,
    designerName: string,
    contract: {
      signingStatus: SigningStatusEnum,
      remittanceStatus: RemittanceStatusEnum,
    },
    design2D: string,
    design3D: string,
    constructionFee: string
  },
  constructionData?: {
    status: ConstructionOrderStatusEnum,
    title: string,
    designerName: string,
    contract: {
      signingStatus: SigningStatusEnum,
      remittanceStatus: RemittanceStatusEnum,
      acceptanceProcessStatus: AcceptanceProcessStatusEnum
    },
    quoteMoney: string,
    workProcess: {
      name: string,
      state: string
    }[],
  },
  invoiceData?: {
    status: InvoiceOrderStatusEnum,
    invoiceOrderType: OrderTypeEnum,
    invoiceCardTitle: string,
    customerName: string,
    address: string,
    amount: string,
    invoiceCardStatus: string,
  }
}

const updateItemState = (time: string, count: number) => {
  if (count === 0) {
    return '未上傳';
  } else if (count > 1) {
    return `已更新 (${formatFullDate(time)})`;
  } else {
    return `已上傳 (${formatFullDate(time)})`;
  }
};

const contractText = (state: SigningStatusEnum) => {
  switch (state) {
    case SigningStatusEnum.Init:
      return '未簽署';
    case SigningStatusEnum.Verifying:
      return '審核中';
    case SigningStatusEnum.Verified:
      return '已簽署';
    case SigningStatusEnum.VerifyFail:
      return '審核失敗';
  }
};

const remittanceText = (state: RemittanceStatusEnum) => {
  switch (state) {
    case RemittanceStatusEnum.NotRemitted:
      return '未匯款';
    case RemittanceStatusEnum.InProgress:
      return '匯款中';
    case RemittanceStatusEnum.Remitted:
      return '已匯款';
  }
};

const acceptanceProcessText = (state: AcceptanceProcessStatusEnum) => {
  switch (state) {
    case AcceptanceProcessStatusEnum.Init:
      return '未確認';
    case AcceptanceProcessStatusEnum.WaitingCustomerConfirm:
      return '待確認';
    case AcceptanceProcessStatusEnum.WaitingDesignerConfirm:
      return '已送出';
    case AcceptanceProcessStatusEnum.AgreementReached:
      return '已確認';
  }
};

const goToOrderDetail = (orderType: OrderTypeEnum, orderId: string) => {
  // TODO 根據不同訂單類型跳轉不同頁面
  if (orderType === OrderTypeEnum.Invoice) {
    router.push({ name: 'customerinvoice', params: { id: orderId } }); // TODO 跳轉到發票詳情頁面
  } else if (orderType === OrderTypeEnum.Measure)
    router.push({ name: 'customermeasuredetail', params: { id: orderId } });
  else if (orderType === OrderTypeEnum.Design)
    router.push({ name: 'customerdesigndetail', params: { id: orderId } });
  else if (orderType === OrderTypeEnum.Construction)
    router.push({ name: 'customerconstructiondetail', params: { id: orderId } });
};

const gotoPublishOrder = () => {
  router.push({ name: 'customermeasurepublish' });
};

const callAllOrder = async () => {
  measureOrderList.value = (await CustomerMeasureOrderDetailService.getManyOrderDetail({})).result;
  designOrderList.value = (await CustomerDesignOrderDetailService.getManyOrderDetail({})).result;
  constructionOrderList.value = (await CustomerConstructionOrderDetailService.getManyOrderDetail({})).result;
  InvoiceList.value = (await CustomerInvoiceDetailService.getManyInvoice({})).invoices;
  isApiLoaded.value = true;
};

const allOrderList = computed<allOrderListData[]>(() => {
  //TODO measure、design、construction 三種進行中訂單的陣列
  let orderList: allOrderListData[] = [];

  measureOrderList.value.forEach((order) => {
    if (order.isDeleted) return;
    const measureData = () => {
      switch (order.status) {
        case MeasureOrderStatusEnum.WaitingSurveyor:
          return {
            status: order.status,
            title: '待安排丈量師',
            address: order.address,
            designerName: '安排中',
            measureTime: '安排中',
            houseInfo: '',
            housePhoto: '',
            houseCheck: '',
            floorPlan: ''
          };
        case MeasureOrderStatusEnum.SurveyorComing:
          return {
            status: order.status,
            title: '丈量師即將到府服務',
            address: order.address,
            designerName: order.designerName,
            measureTime: formatFullDateTimeWithDay(order.measureTime),
            houseInfo: '',
            housePhoto: '',
            houseCheck: '',
            floorPlan: ''
          };
        case MeasureOrderStatusEnum.WaitingUpload:
          return {
            status: order.status,
            title: '待丈量師上傳資料',
            address: order.address,
            designerName: order.designerName,
            measureTime: formatFullDateTimeWithDay(order.measureTime),
            houseInfo: updateItemState(order.content.houseInfo.updateTime, order.content.houseInfo.updateCount),
            housePhoto: updateItemState(order.content.photos.updateTime, order.content.photos.updateCount),
            houseCheck: updateItemState(order.content.houseCheck.updateTime, order.content.houseCheck.updateCount),
            floorPlan: updateItemState(order.content.floorPlan.updateTime, order.content.floorPlan.updateCount)
          };
        case MeasureOrderStatusEnum.SurveyDone:
          return {
            status: order.status,
            title: '丈量師已上傳資料',
            address: order.address,
            designerName: order.designerName,
            measureTime: formatFullDateTimeWithDay(order.measureTime),
            houseInfo: updateItemState(order.content.houseInfo.updateTime, order.content.houseInfo.updateCount),
            housePhoto: updateItemState(order.content.photos.updateTime, order.content.photos.updateCount),
            houseCheck: updateItemState(order.content.houseCheck.updateTime, order.content.houseCheck.updateCount),
            floorPlan: updateItemState(order.content.floorPlan.updateTime, order.content.floorPlan.updateCount)
          };
        case MeasureOrderStatusEnum.RefillTime:
          return {
            status: order.status,
            title: '需重新填寫預約時間',
            address: order.address,
            designerName: '安排中',
            measureTime: '需重新填寫',
            houseInfo: '',
            housePhoto: '',
            houseCheck: '',
            floorPlan: ''
          };
      }
    };
    const data = {
      orderType: OrderTypeEnum.Measure,
      orderId: order.orderId,
      refreshTime: order.refreshTime,
      orderStatus: order.status !== MeasureOrderStatusEnum.SurveyDone ? OrderShowTypeEnum.Process : OrderShowTypeEnum.History,
      measureData: measureData()
    };
    orderList.push(data);
  });

  designOrderList.value.forEach((order) => {
    if (order.isDeleted) return;
    const designData = () => {
      switch (order.status) {
        case DesignOrderStatusEnum.Comparing:
          return {
            status: order.status,
            title: '尋找設計師中',
            designerName: order.quotationCount === 0 ? '(尋找設計師中)' : `已有${order.quotationCount}人報價`,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus
            },
            design2D: '',
            design3D: '',
            constructionFee: ''
          };
        case DesignOrderStatusEnum.Contracting:
          return {
            status: order.status,
            title: '已指定設計師',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus
            },
            design2D: updateItemState(order.designContent.design2D.updateTime, order.designContent.design2D.updateCount),
            design3D: updateItemState(order.designContent.design3D.updateTime, order.designContent.design3D.updateCount),
            constructionFee: updateItemState(order.constructionFee.updateTime, order.constructionFee.updateCount)
          };
        case DesignOrderStatusEnum.Working:
          return {
            status: order.status,
            title: '已指定設計師',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus
            },
            design2D: updateItemState(order.designContent.design2D.updateTime, order.designContent.design2D.updateCount),
            design3D: updateItemState(order.designContent.design3D.updateTime, order.designContent.design3D.updateCount),
            constructionFee: updateItemState(order.constructionFee.updateTime, order.constructionFee.updateCount)
          };
        case DesignOrderStatusEnum.Completed:
          return {
            status: order.status,
            title: '設計資料已上傳',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus
            },
            design2D: updateItemState(order.designContent.design2D.updateTime, order.designContent.design2D.updateCount),
            design3D: updateItemState(order.designContent.design3D.updateTime, order.designContent.design3D.updateCount),
            constructionFee: updateItemState(order.constructionFee.updateTime, order.constructionFee.updateCount)
          };
      }
    };
    const data = {
      orderType: OrderTypeEnum.Design,
      orderId: order.orderId,
      refreshTime: order.refreshTime,
      orderStatus: order.status !== DesignOrderStatusEnum.Completed ? OrderShowTypeEnum.Process : OrderShowTypeEnum.History,
      designData: designData()
    };
    orderList.push(data);
  });

  constructionOrderList.value.forEach((order) => {
    if (order.isDeleted) return;
    const constructionData = () => {
      switch (order.status) {
        case ConstructionOrderStatusEnum.Comparing:
          return {
            status: order.status,
            title: '尋找設計師中',
            designerName: order.quotationCount === 0 ? '(尋找設計師中)' : `已有${order.quotationCount}人報價`,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus,
              acceptanceProcessStatus: order.contract.acceptanceProcessStatus
            },
            quoteMoney: '',
            workProcess: [{ name: '', state: '' }]
          };
        case ConstructionOrderStatusEnum.Quoting:
          return {
            status: order.status,
            title: '等待設計師提供報價清單',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus,
              acceptanceProcessStatus: order.contract.acceptanceProcessStatus
            },
            quoteMoney: order.contract.constructionAmount !== -1 ? moneyAddCommas(order.contract.constructionAmount) : budgetCombineLowToHigh(order.contract.constructionEstimate),
            workProcess: [{ name: '', state: '' }]
          };
        case ConstructionOrderStatusEnum.Contracting:
          return {
            status: order.status,
            title: '已指定設計師',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus,
              acceptanceProcessStatus: order.contract.acceptanceProcessStatus
            },
            quoteMoney: '',
            workProcess: [{ name: '', state: '' }]
          };
        case ConstructionOrderStatusEnum.Working:
          return {
            status: order.status,
            title: '已指定設計師',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus,
              acceptanceProcessStatus: order.contract.acceptanceProcessStatus
            },
            quoteMoney: '',
            workProcess: order.constructionContent.process.map((process) => {
              return {
                name: process.name,
                state: updateItemState(process.updateTime, process.updateCount)
              };
            })
          };
        case ConstructionOrderStatusEnum.Completed:
          return {
            status: order.status,
            title: '裝潢資料已上傳',
            designerName: order.designerName,
            contract: {
              signingStatus: order.contract.signingStatus,
              remittanceStatus: order.contract.remittanceStatus,
              acceptanceProcessStatus: order.contract.acceptanceProcessStatus
            },
            quoteMoney: '',
            workProcess: order.constructionContent.process.map((process) => {
              return {
                name: process.name,
                state: updateItemState(process.updateTime, process.updateCount)
              };
            })
          };
      }
    };
    const data = {
      orderType: OrderTypeEnum.Construction,
      orderId: order.orderId,
      refreshTime: order.refreshTime,
      orderStatus: order.status !== ConstructionOrderStatusEnum.Completed ? OrderShowTypeEnum.Process : OrderShowTypeEnum.History,
      constructionData: constructionData()
    };
    orderList.push(data);
  });

  InvoiceList.value.forEach((invoice) => {
    const invoiceData = () => {
      return {
        status: invoice.status,
        invoiceOrderType: invoice.orderType,
        invoiceCardTitle: invoice.orderType === OrderTypeEnum.Measure ? '到府丈量服務'
          : invoice.orderType === OrderTypeEnum.Design ? '室內設計比價'
            : '裝潢施工細節',
        customerName: invoice.customerName,
        address: invoice.address.fullName,
        amount: moneyAddCommas(invoice.amount),
        invoiceCardStatus: invoice.status === InvoiceOrderStatusEnum.NotIssued ? '未開立'
          : invoice.status === InvoiceOrderStatusEnum.Pending ? '待開立' : '已開立'
      };
    };

    const data = {
      orderType: OrderTypeEnum.Invoice,
      orderId: invoice.invoiceId,
      refreshTime: invoice.refreshTime,
      orderStatus: OrderShowTypeEnum.History,
      invoiceData: invoiceData()
    };
    orderList.push(data);
  });

  return orderList;
});

const filteredOrderList = computed(() => {
  //TODO 按照orderShow去過濾 處理過後的orderList
  let filteredList: allOrderListData[];
  if (orderShow.value === 'process') {
    filteredList = allOrderList.value.filter((order) => order.orderStatus === OrderShowTypeEnum.Process);
  } else if (orderShow.value === 'history') {
    filteredList = allOrderList.value.filter((order) => order.orderStatus === OrderShowTypeEnum.History);
  } else {
    filteredList = allOrderList.value.slice();
  }
  return filteredList.slice().sort((a, b) => {
    return new Date(b.refreshTime).getTime() - new Date(a.refreshTime).getTime();
  });
});

onMounted(async () => {
  await callAllOrder();
});
</script>

<template>
  <div class="w-full">
    <div class="text-center p-4 rounded-lg my-4">
      <h1 class="text-2xl font-bold">刊登的訂單紀錄</h1>
    </div>
    <div class="w-full p-4 mb-4 rounded-lg ">
      <div class="flex max-md:flex-col justify-between gap-2">
        <label
          :class="['md:w-1/4 flex gap-x-2 items-center justify-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'all' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="all" v-model="orderShow" class="hidden">
          所有訂單
        </label>
        <label
          :class="['md:w-1/4 flex gap-x-2 items-center justify-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'process' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="process" v-model="orderShow" class="hidden">
          進行訂單
        </label>
        <label
          :class="['md:w-1/4 flex gap-x-2 items-center justify-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'history' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="history" v-model="orderShow" class="hidden">
          歷史訂單
        </label>
      </div>
    </div>
    <div v-if="allOrderList.length === 0 && isApiLoaded">
      <div class="text-center cus-border p-4 rounded-lg my-4">
        <h1 class="">您目前尚未預約到府丈量訂單服務</h1>
        <h1 class="">是否前往<span class="text-red-600 cursor-pointer"
                                   @click="gotoPublishOrder()">預約到府丈量訂單服務</span>
        </h1>
      </div>
    </div>

    <div v-for="order in filteredOrderList" :key="order.orderId"
         class="cus-border p-8 rounded-lg mb-4 cursor-pointer hover-zoom text-center"
         @click="goToOrderDetail(order.orderType,order.orderId)">

      <!--      Step1 Card-->
      <template v-if="order.orderType === OrderTypeEnum.Measure ">
        <h2 class="text-xl  mb-2">Step1 到府丈量服務</h2>
        <p class="text-xl  mb-2">{{ order?.measureData?.title }}</p>
        <HezDivider />
        <div class="mx-auto flex justify-center"
             v-if="order.measureData?.status !== MeasureOrderStatusEnum.WaitingUpload && order.measureData?.status !== MeasureOrderStatusEnum.SurveyDone">
          <div class="text-lg text-left">
            <p>丈量師 {{ order.measureData?.designerName }}</p>
            <p>丈量地址 {{ order.measureData?.address }}</p>
            <p>丈量時間 <span
              :class="{'text-red-600' : order.measureData?.status === MeasureOrderStatusEnum.RefillTime}"> {{ order.measureData?.measureTime
              }}</span></p>
          </div>
        </div>
        <div v-else class="mx-auto flex justify-center">
          <div class="text-lg text-left">
            <p>房屋資訊 {{ order.measureData?.houseInfo }}</p>
            <p>房屋照片 {{ order.measureData?.housePhoto }}</p>
            <p>屋況紀錄(非專業技師) {{ order.measureData?.houseCheck }}</p>
            <p>丈量平面圖 {{ order.measureData?.floorPlan }}</p>
          </div>
        </div>
      </template>

      <!--      Step2 Card-->
      <template v-else-if="order.orderType === OrderTypeEnum.Design ">
        <h2 class="text-xl  mb-2">Step2 室內設計比價</h2>
        <p class="text-xl  mb-2">{{ order.designData?.title }}</p>
        <HezDivider />
        <div class="mx-auto flex justify-center"
             v-if="order.designData?.status === DesignOrderStatusEnum.Comparing">
          <div class="text-lg text-left">
            <p>設計師姓名 {{ order.designData?.designerName }}</p>
          </div>
        </div>
        <div v-else-if="order.designData?.status === DesignOrderStatusEnum.Contracting"
             class="mx-auto flex justify-center">
          <div class="text-lg text-left">
            <p>設計合約
              <span
                v-if="order.designData?.contract.signingStatus === SigningStatusEnum.Init || order.designData?.contract.signingStatus === SigningStatusEnum.VerifyFail"
                class="text-red-600">
                {{ contractText(order.designData?.contract.signingStatus) }}
              </span>
              <span
                v-else-if="order.designData?.contract.signingStatus === SigningStatusEnum.Verifying"
                class="text-blue-600">
                {{ contractText(order.designData?.contract.signingStatus) }}
              </span>
              <span v-else class="text-black">
                {{ contractText(order.designData?.contract.signingStatus) }}
              </span>
            </p>
            <p>匯款金額
              <span v-if="order.designData?.contract.remittanceStatus === RemittanceStatusEnum.NotRemitted"
                    class="text-red-600">{{ remittanceText(order.designData?.contract.remittanceStatus) }}
              </span>
              <span v-else-if="order.designData?.contract.remittanceStatus === RemittanceStatusEnum.InProgress"
                    class="text-blue-600">{{ remittanceText(order.designData?.contract.remittanceStatus) }}
              </span>
              <span
                v-else class="text-black">{{ remittanceText(order.designData?.contract.remittanceStatus) }}
              </span>
            </p>
          </div>
        </div>
        <div v-else class="mx-auto flex justify-center">
          <div class="text-lg text-left">
            <p>2D室內設計 {{ order.designData?.design2D }}</p>
            <p>3D模型設計 {{ order.designData?.design3D }}</p>
            <p>裝潢報價清單 {{ order.designData?.constructionFee }}</p>
          </div>
        </div>
      </template>

      <!--      Step3 Card-->
      <template v-else-if="order.orderType === OrderTypeEnum.Construction ">
        <h2 class="text-xl  mb-2">Step3 裝潢施工細節</h2>
        <p class="text-xl  mb-2">{{ order.constructionData?.title }}</p>
        <HezDivider />
        <div class="mx-auto flex justify-center"
             v-if="order.constructionData?.status === ConstructionOrderStatusEnum.Comparing">
          <div class="text-lg text-left">
            <p>設計師姓名 {{ order.constructionData?.designerName }}</p>
          </div>
        </div>
        <div class="mx-auto flex justify-center"
             v-if="order.constructionData?.status === ConstructionOrderStatusEnum.Quoting">
          <div class="text-lg text-left">
            <p>設計師姓名 {{ order.constructionData?.designerName }}</p>
            <p>設計師報價 {{ order.constructionData?.quoteMoney }}</p>
          </div>
        </div>
        <div class="mx-auto flex justify-center"
             v-if="order.constructionData?.status === ConstructionOrderStatusEnum.Contracting">
          <div class="text-lg text-left">
            <p>裝潢合約
              <span
                v-if="order.constructionData?.contract.signingStatus === SigningStatusEnum.Init || order.constructionData?.contract.signingStatus === SigningStatusEnum.VerifyFail"
                class="text-red-600">
                {{ contractText(order.constructionData?.contract.signingStatus) }}
              </span>
              <span
                v-else-if="order.constructionData?.contract.signingStatus === SigningStatusEnum.Verifying"
                class="text-blue-600">
                {{ contractText(order.constructionData?.contract.signingStatus) }}
              </span>
              <span v-else class="text-black">
                {{ contractText(order.constructionData?.contract.signingStatus) }}
              </span>
            </p>

            <p>流程狀態
              <span
                v-if="order.constructionData?.contract.acceptanceProcessStatus === AcceptanceProcessStatusEnum.Init || order.constructionData?.contract.acceptanceProcessStatus === AcceptanceProcessStatusEnum.WaitingCustomerConfirm"
                class="text-red-600">
                {{ acceptanceProcessText(order.constructionData?.contract.acceptanceProcessStatus) }}
              </span>
              <span
                v-else-if="order.constructionData?.contract.acceptanceProcessStatus === AcceptanceProcessStatusEnum.WaitingDesignerConfirm"
                class="text-blue-600">
                {{ acceptanceProcessText(order.constructionData?.contract.acceptanceProcessStatus) }}
              </span>
              <span v-else class="text-black">
                {{ acceptanceProcessText(order.constructionData?.contract.acceptanceProcessStatus) }}
              </span>
            </p>

            <p>匯款金額
              <span v-if="order.constructionData?.contract.remittanceStatus === RemittanceStatusEnum.NotRemitted"
                    class="text-red-600">{{ remittanceText(order.constructionData?.contract.remittanceStatus) }}
              </span>
              <span v-else-if="order.constructionData?.contract.remittanceStatus === RemittanceStatusEnum.InProgress"
                    class="text-blue-600">{{ remittanceText(order.constructionData?.contract.remittanceStatus) }}
              </span>
              <span
                v-else class="text-black">{{ remittanceText(order.constructionData?.contract.remittanceStatus) }}
              </span>
            </p>
          </div>
        </div>
        <div v-else class="mx-auto flex justify-center">
          <div>
            <div v-for="work in order.constructionData?.workProcess" :key="work.name" class="text-lg text-left">
              <p>{{ work.name }} {{ work.state }}</p>
            </div>
          </div>
        </div>
      </template>

      <!--      Step4 Card-->
      <template v-else-if="order.orderType === OrderTypeEnum.Invoice ">
        <h2 class="text-xl  mb-2">Step4 發票收據節稅</h2>
        <p class="text-xl  mb-2">{{ order.invoiceData?.invoiceCardTitle }}</p>
        <HezDivider />
        <div class="mx-auto flex justify-center">
          <div class="text-lg text-left">
            <p>屋主姓名 {{ order.invoiceData?.customerName }}</p>
            <p>
              <span
                v-if="order.invoiceData?.invoiceOrderType === OrderTypeEnum.Measure">
                丈量地址 {{ order.invoiceData?.address }}</span>
              <span
                v-else-if="order.invoiceData?.invoiceOrderType === OrderTypeEnum.Design">
                設計地址 {{ order.invoiceData?.address }}</span>
              <span v-else>裝潢地址 {{ order.invoiceData?.address }}</span>
            </p>
            <p>
              <span
                v-if="order.invoiceData?.invoiceOrderType === OrderTypeEnum.Measure">
                丈量金額 {{ order.invoiceData?.amount }}</span>
              <span
                v-else-if="order.invoiceData?.invoiceOrderType === OrderTypeEnum.Design">
                設計金額 {{ order.invoiceData?.amount }}</span>
              <span v-else>裝潢金額 {{ order.invoiceData?.amount }}</span>
            </p>
            <p>
              發票狀態
              <span v-if="order.invoiceData?.status === InvoiceOrderStatusEnum.NotIssued"
                    class="text-red-600">{{ order.invoiceData?.invoiceCardStatus }}</span>
              <span v-else-if="order.invoiceData?.status === InvoiceOrderStatusEnum.Pending"
                    class="text-blue-600">{{ order.invoiceData?.invoiceCardStatus }}</span>
              <span v-else class="text-black">{{ order.invoiceData?.invoiceCardStatus }}</span>
            </p>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>
