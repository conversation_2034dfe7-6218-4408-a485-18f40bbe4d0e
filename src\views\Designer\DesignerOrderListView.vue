<script setup lang="ts">
import HezDesignerInfo from '@/components/Designer/HezDesignerInfo.vue';
import { computed, onMounted, ref } from 'vue';
import { OrderCardService } from '@/api/designerOrder.ts';
import {
  AcceptedOrderCardData, ConstructionAcceptedCard, ConstructionHistoricalCard,
  ConstructionQuotingCard,
  ConstructionUnacceptedCard, DesignAcceptedCard, DesignHistoricalCard,
  DesignQuotingCard,
  DesignUnacceptedCard,
  HistoricalOrderCardData, MeasureAcceptedCard, MeasureHistoricalCard, MeasureUnacceptedCard,
  QuotingOrderCardData,
  UnacceptedOrderCardData
} from '@/model/response/orderCard/manyOrderCardResponse.ts';
import { OrderListStatusEnum, OrderTypeEnum } from '@/model/enum/orderType.ts';
import {
  AcceptanceProcessStatusEnum,
  AmountStatusEnum,
  ConstructionOrderStatusEnum,
  DesignOrderStatusEnum,
  DesignOrderSubStatusEnum, MeasureOrderStatusEnum, SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import { JSONStringToObject } from '@/utils/JsonStringFormat.ts';
import { budgetCombineLowToHigh, moneyAddCommas } from '@/utils/budgetFormat.ts';
import { useDesignerVerifyStore } from '@/stores/designerGlobal.ts';
import { useRouter } from 'vue-router';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { formatFullDateTimeWithDay, measureOrderScheduleHint } from '@/utils/timeFormat.ts';

const orderShow = ref<'all' | 'unaccepted' | 'quoting' | 'accepted' | 'historical'>('all');
const unacceptedOrderListData = ref<UnacceptedOrderCardData[]>([]);
const quotingOrderListData = ref<QuotingOrderCardData[]>([]);
const acceptedOrderListData = ref<AcceptedOrderCardData[]>([]);
const historicalOrderListData = ref<HistoricalOrderCardData[]>([]);
const goToVerifyModalRef = ref<InstanceType<typeof DefaultModal> | null>();
const VerifyModalTitle = ref('尚未完成身分驗證');


const callAllOrder = async () => {
  unacceptedOrderListData.value = (await OrderCardService.getManyUnaccepted({})).result;
  quotingOrderListData.value = (await OrderCardService.getManyQuoting({})).result;
  acceptedOrderListData.value = (await OrderCardService.getManyAccepted({})).result;
  historicalOrderListData.value = (await OrderCardService.getManyHistorical({})).result;
};

const verifyStore = useDesignerVerifyStore();
const router = useRouter();

//訂單資料處理區開始
interface allOrderListData {
  createTime: string;
  refreshTime: string;
  // Step1~3 三種
  orderType: OrderTypeEnum;
  // 有unaccepted、quoting、accepted、historical等四種
  orderListStatus: OrderListStatusEnum;
  orderId: string;
  customerName: string;
  address: string;
  measureUnaccepted?: {
    measureTimes: string[];
  };
  designUnaccepted?: {
    areaPing: string;
    designBudget: string;
    constructionBudget: string;
    isAssignedDesigner: boolean;
    quotedDesignersCount: number;
    //'請盡快回覆報價'、'您是指定的設計師，請盡快回覆'、'n位設計師已報價' 三種
    cardHint: string;
  };
  constructionUnaccepted?: {
    houseType: string;
    areaPing: string;
    constructionBudget: string;
    quotedDesignersCount: number;
    //'請盡快回覆報價'、'n位設計師已報價' 兩種
    cardHint: string;
  };
  designQuoting?: {
    areaPing: string;
    designBudget: string;
    constructionBudget: string;
    isAssignedDesigner: boolean;
    quotedDesignersCount: number;
    designAmount: string;
    constructionEstimate: string;
    //'已報價，等待回覆中' 一種
    cardHint: string;
  };
  constructionQuoting?: {
    houseType: string;
    areaPing: string;
    constructionBudget: string;
    quotedDesignersCount: number;
    constructionEstimate: string;
    isCustomerAccepted: boolean;
    amountStatus: AmountStatusEnum;
    constructionAmount: string;
    constructionAmountNumber: number;
    constructionDiscountAmount: string;
    //'已提供報價'、'報價已接受'、'報價清單已提供'三種
    cardQuotingHint: string;
    //'等待回覆中'、'請盡速提供報價清單'、'等待客戶確認中' 三種
    cardHint: string;
  };
  measureAccepted?: {
    measureTime: string;
    cardHint: string;
  };
  designAccepted?: {
    areaPing: string;
    designBudget: string;
    constructionBudget: string;
    isAssignedDesigner: boolean;
    quotedDesignersCount: number;
    designAmount: string;
    constructionEstimate: string;
    status: DesignOrderStatusEnum;
    subStatus: DesignOrderSubStatusEnum;
    signingStatus: SigningStatusEnum;
    isBankAccountSet: boolean;
    //'合約未簽約'、'等待雙方審核中'、'等待客戶匯款中'、'已匯款' 四種
    cardHint: string;
    //'已接單' 一種 備註:開始工作之後就給空字串
    orderAcceptedHint: string;
    //'進行中'、已完成' 兩種 備註:開始工作之後才出現 在這之前給空字串
    design2DHint: string;
    //'未完成'、'進行中'、已完成' 三種 備註:開始工作之後才出現 在這之前給空字串
    design3DHint: string;
    //'未完成'、'進行中'、已完成' 三種 備註:開始工作之後才出現 在這之前給空字串
    constructionAmountDocHint: string;
  };
  constructionAccepted?: {
    houseType: string;
    areaPing: string;
    constructionBudget: string;
    quotedDesignersCount: number;
    constructionEstimate: string;
    isCustomerAccepted: boolean;
    amountStatus: AmountStatusEnum;
    constructionAmount: string;
    constructionDiscountAmount: string;
    status: ConstructionOrderStatusEnum;
    acceptanceProcessStatus: AcceptanceProcessStatusEnum;
    signingStatus: SigningStatusEnum;
    isBankAccountSet: boolean;
    //'合約未簽約'、'驗收流程未確認'、'工作中' 三種
    cardHint: string;
    //'已接單' 一種
    orderAcceptedHint: string;
  };
  measureHistorical?: {
    measureTime: string;
    cardHint: string;
  };
  designHistorical?: {
    areaPing: string;
    designBudget: string;
    constructionBudget: string;
    isAssignedDesigner: boolean;
    quotedDesignersCount: number;
    designAmount: string;
    constructionEstimate: string;
    status: DesignOrderStatusEnum;
    subStatus: DesignOrderSubStatusEnum;
    signingStatus: SigningStatusEnum;
    isBankAccountSet: boolean;
    isDeleted: boolean;
    //'訂單已完成' 一種
    cardHint: string;
  };
  constructionHistorical?: {
    houseType: string;
    areaPing: string;
    constructionBudget: string;
    quotedDesignersCount: number;
    constructionEstimate: string;
    isCustomerAccepted: boolean;
    amountStatus: AmountStatusEnum;
    constructionAmount: string;
    constructionDiscountAmount: string;
    status: ConstructionOrderStatusEnum;
    acceptanceProcessStatus: AcceptanceProcessStatusEnum;
    signingStatus: SigningStatusEnum;
    isBankAccountSet: boolean;
    isDeleted: boolean;
    //'訂單已完成' 一種
    cardHint: string;
  };
}

const processMeasureUnaccepted = (order: MeasureUnacceptedCard) => {
  return {
    measureTimes: order.measureTime.map(time => formatFullDateTimeWithDay(time.measureTime))
  };
};

const processDesignUnaccepted = (order: DesignUnacceptedCard) => {
  return {
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    designBudget: budgetCombineLowToHigh(order.designBudget),
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    isAssignedDesigner: order.isAssignedDesigner,
    quotedDesignersCount: order.quotedDesignersCount,
    cardHint: order.isAssignedDesigner
      ? '您是指定的設計師，請盡快回覆報價'
      : order.quotedDesignersCount === 0
        ? '請盡快回覆報價'
        : `${order.quotedDesignersCount}位設計師已報價`
  };
};

const processConstructionUnaccepted = (order: ConstructionUnacceptedCard) => {
  return {
    houseType: JSONStringToObject(order.houseType).content.text,
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    quotedDesignersCount: order.quotedDesignersCount,
    cardHint: order.quotedDesignersCount === 0 ? '請盡快回覆報價' : `${order.quotedDesignersCount}位設計師已報價`
  };
};

const processDesignQuoting = (order: DesignQuotingCard) => {
  return {
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    designBudget: budgetCombineLowToHigh(order.designBudget),
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    isAssignedDesigner: order.isAssignedDesigner,
    quotedDesignersCount: order.quotedDesignersCount,
    designAmount: moneyAddCommas(order.designAmount),
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    cardHint: '已報價，等待回覆中'
  };
};

const processConstructionQuoting = (order: ConstructionQuotingCard) => {
  const cardHint = () => {
    if (!order.isCustomerAccepted) {
      return '等待回覆中';
    } else if (order.amountStatus === AmountStatusEnum.NotQuoted) {
      return '請盡速提供報價清單';
    } else if (order.amountStatus === AmountStatusEnum.CustomerDisagreed) {
      return '客戶要求議價和修改';
    } else {
      return '等待客戶確認中';
    }
  };
  return {
    houseType: JSONStringToObject(order.houseType).content.text,
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    quotedDesignersCount: order.quotedDesignersCount,
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    isCustomerAccepted: order.isCustomerAccepted,
    amountStatus: order.amountStatus,
    constructionAmount: moneyAddCommas(order.constructionAmount),
    constructionAmountNumber: order.constructionAmount,
    constructionDiscountAmount: moneyAddCommas(order.constructionDiscountAmount),
    cardQuotingHint: !order.isCustomerAccepted ? '已提供報價' : order.amountStatus === AmountStatusEnum.NotQuoted ? '報價已接受' : '報價清單已提供',
    cardHint: cardHint()
  };
};

const processMeasureAccepted = (order: MeasureAcceptedCard) => {
  const cardHint = () => {
    if (order.status === MeasureOrderStatusEnum.WaitingUpload) {
      return '丈量已完成，資料未上傳';
    } else {
      return measureOrderScheduleHint(order.measureTime);
    }
  };
  return {
    measureTime: formatFullDateTimeWithDay(order.measureTime),
    cardHint: cardHint()
  };
};

const processDesignAccepted = (order: DesignAcceptedCard) => {
  const cardHint = () => {
    if (order.status === DesignOrderStatusEnum.Contracting) {
      if (order.signingStatus === SigningStatusEnum.Init) {
        return '合約未簽約';
      } else if (order.signingStatus === SigningStatusEnum.VerifyFail) {
        return '重新簽署合約';
      } else {
        return '等待雙方審核中';
      }
    } else {
      return '已匯款'; //合約簽完開始工作就維持 '已匯款'
    }
  };
  const orderAcceptedHint = () => {
    return order.status <= DesignOrderStatusEnum.Contracting ? '已接單' : '';
  };
  const design2DHint = () => {
    if (order.status >= DesignOrderStatusEnum.Working) {
      if (order.subStatus < DesignOrderSubStatusEnum.Design2DAgree) {
        return '進行中';
      } else {
        return '已完成';
      }
    } else {
      return '';
    }
  };
  const design3DHint = () => {
    if (order.status >= DesignOrderStatusEnum.Working) {
      if (order.subStatus < DesignOrderSubStatusEnum.Design2DAgree) {
        return '未完成';
      } else if (order.subStatus < DesignOrderSubStatusEnum.Design3DAgree) {
        return '進行中';
      } else {
        return '已完成';
      }
    } else {
      return '';
    }
  };
  const constructionAmountDocHint = () => {
    if (order.status >= DesignOrderStatusEnum.Working) {
      if (order.subStatus < DesignOrderSubStatusEnum.Design3DAgree) {
        return '未完成';
      } else {
        return '進行中';
      }
    } else {
      return '';
    }
  };

  return {
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    designBudget: budgetCombineLowToHigh(order.designBudget),
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    isAssignedDesigner: order.isAssignedDesigner,
    quotedDesignersCount: order.quotedDesignersCount,
    designAmount: moneyAddCommas(order.designAmount),
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    status: order.status,
    subStatus: order.subStatus,
    signingStatus: order.signingStatus,
    isBankAccountSet: order.isBankAccountSet,
    cardHint: cardHint(),
    orderAcceptedHint: orderAcceptedHint(),
    design2DHint: design2DHint(),
    design3DHint: design3DHint(),
    constructionAmountDocHint: constructionAmountDocHint()
  };
};

const processConstructionAccepted = (order: ConstructionAcceptedCard) => {
  const cardHint = () => {
    if (order.signingStatus !== SigningStatusEnum.Verified) {
      switch (order.signingStatus) {
        case SigningStatusEnum.Init:
          return '合約未簽約';
        case SigningStatusEnum.Verifying:
          return '等待雙方審核中';
        case SigningStatusEnum.VerifyFail:
          return '重新簽署合約';
      }
    }
    switch (order.acceptanceProcessStatus) {
      case AcceptanceProcessStatusEnum.Init:
        return '驗收流程未確認';
      case AcceptanceProcessStatusEnum.WaitingCustomerConfirm:
      case AcceptanceProcessStatusEnum.WaitingDesignerConfirm:
        return '雙方流程確認中';
      case AcceptanceProcessStatusEnum.AgreementReached:
        return '工作中';
    }
  };

  return {
    houseType: JSONStringToObject(order.houseType).content.text,
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    quotedDesignersCount: order.quotedDesignersCount,
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    isCustomerAccepted: order.isCustomerAccepted,
    amountStatus: order.amountStatus,
    constructionAmount: moneyAddCommas(order.constructionAmount),
    constructionDiscountAmount: moneyAddCommas(order.constructionDiscountAmount),
    status: order.status,
    acceptanceProcessStatus: order.acceptanceProcessStatus,
    signingStatus: order.signingStatus,
    isBankAccountSet: order.isBankAccountSet,
    cardHint: cardHint(),
    orderAcceptedHint: '已接單'
  };
};

const processMeasureHistorical = (order: MeasureHistoricalCard) => {
  return {
    measureTime: formatFullDateTimeWithDay(order.measureTime),
    cardHint: '丈量完成，資料已上傳'
  };
};

const processDesignHistorical = (order: DesignHistoricalCard) => {
  return {
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    designBudget: budgetCombineLowToHigh(order.designBudget),
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    isAssignedDesigner: order.isAssignedDesigner,
    quotedDesignersCount: order.quotedDesignersCount,
    designAmount: moneyAddCommas(order.designAmount),
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    status: order.status,
    subStatus: order.subStatus,
    signingStatus: order.signingStatus,
    isBankAccountSet: order.isBankAccountSet,
    isDeleted: order.isDeleted,
    cardHint: '訂單已完成'
  };
};

const processConstructionHistorical = (order: ConstructionHistoricalCard) => {
  return {
    houseType: JSONStringToObject(order.houseType).content.text,
    areaPing: `${JSONStringToObject(order.areaPing).content.text}坪`,
    constructionBudget: budgetCombineLowToHigh(order.constructionBudget),
    quotedDesignersCount: order.quotedDesignersCount,
    constructionEstimate: budgetCombineLowToHigh(order.constructionEstimate),
    isCustomerAccepted: order.isCustomerAccepted,
    amountStatus: order.amountStatus,
    constructionAmount: moneyAddCommas(order.constructionAmount),
    constructionDiscountAmount: moneyAddCommas(order.constructionDiscountAmount),
    status: order.status,
    acceptanceProcessStatus: order.acceptanceProcessStatus,
    signingStatus: order.signingStatus,
    isBankAccountSet: order.isBankAccountSet,
    isDeleted: order.isDeleted,
    cardHint: '訂單已完成'
  };
};

const combineUnacceptedOrders = (orders: UnacceptedOrderCardData[]): allOrderListData[] => {
  return orders
    .map(order => {
      const result: allOrderListData = {
        createTime: order.createTime,
        refreshTime: order.refreshTime,
        orderType: order.orderType,
        orderListStatus: OrderListStatusEnum.Unaccepted,
        orderId: order.orderId,
        customerName: order.customerName,
        address: order.orderType === OrderTypeEnum.Measure ? order.address : `${order.address}***`
      };

      if (order.measureUnaccepted) {
        result.measureUnaccepted = processMeasureUnaccepted(order.measureUnaccepted);
      }

      if (order.designUnaccepted) {
        result.designUnaccepted = processDesignUnaccepted(order.designUnaccepted);
      }

      if (order.constructionUnaccepted) {
        result.constructionUnaccepted = processConstructionUnaccepted(order.constructionUnaccepted);
      }

      return result;
    });
};

const combineQuotingOrders = (orders: QuotingOrderCardData[]): allOrderListData[] => {
  return orders
    .map(order => {
      const result: allOrderListData = {
        createTime: order.createTime,
        refreshTime: order.refreshTime,
        orderType: order.orderType,
        orderListStatus: OrderListStatusEnum.Quoting,
        orderId: order.orderId,
        customerName: order.customerName,
        address: order.address
      };

      if (order.designQuoting) {
        result.designQuoting = processDesignQuoting(order.designQuoting);
      }

      if (order.constructionQuoting) {
        result.constructionQuoting = processConstructionQuoting(order.constructionQuoting);
      }

      return result;
    });
};

const combineAcceptedOrders = (orders: AcceptedOrderCardData[]): allOrderListData[] => {
  return orders
    .map(order => {
      const result: allOrderListData = {
        createTime: order.createTime,
        refreshTime: order.refreshTime,
        orderType: order.orderType,
        orderListStatus: OrderListStatusEnum.Accepted,
        orderId: order.orderId,
        customerName: order.customerName,
        address: order.address
      };

      if (order.measureAccepted) {
        result.measureAccepted = processMeasureAccepted(order.measureAccepted);
      }

      if (order.designAccepted) {
        result.designAccepted = processDesignAccepted(order.designAccepted);
      }

      if (order.constructionAccepted) {
        result.constructionAccepted = processConstructionAccepted(order.constructionAccepted);
      }

      return result;
    });
};

const combineHistoricalOrders = (orders: HistoricalOrderCardData[]): allOrderListData[] => {
  return orders
    .filter(order =>
      !(order.designHistorical?.isDeleted === true || order.constructionHistorical?.isDeleted === true || order.measureHistorical?.isDeleted === true)
    ) // 排除 isDeleted 為 true 的訂單
    .map(order => {
      const result: allOrderListData = {
        createTime: order.createTime,
        refreshTime: order.refreshTime,
        orderType: order.orderType,
        orderListStatus: OrderListStatusEnum.Historical,
        orderId: order.orderId,
        customerName: order.customerName,
        address: order.address
      };

      if (order.measureHistorical) {
        result.measureHistorical = processMeasureHistorical(order.measureHistorical);
      }

      if (order.designHistorical) {
        result.designHistorical = processDesignHistorical(order.designHistorical);
      }

      if (order.constructionHistorical) {
        result.constructionHistorical = processConstructionHistorical(order.constructionHistorical);
      }

      return result;
    });
};
//訂單資料處理區結束

const openGoToVerifyModal = () => {
  if (!verifyStore.taiwanIdVerify) {
    VerifyModalTitle.value = '尚未完成身分驗證';
  } else if (!verifyStore.companyVerify) {
    VerifyModalTitle.value = '尚未完成公司驗證';
  } else {
    VerifyModalTitle.value = '尚未完成email驗證';
  }
  goToVerifyModalRef.value?.openModal();
};

const goToProfile = () => {
  router.push({ name: 'designerprofile' });
};

const goToOrderDetail = (orderId: string, orderType: OrderTypeEnum, orderListStatus: OrderListStatusEnum) => {
  if (!verifyStore.taiwanIdVerify || !verifyStore.companyVerify || !verifyStore.emailVerify) {
    openGoToVerifyModal();
    return;
  }
  switch (orderType) {
    case OrderTypeEnum.Measure:
      switch (orderListStatus) {
        case OrderListStatusEnum.Unaccepted:
          router.push({
            name: 'MeasureUnacceptedOrderDetail', params: { id: orderId }
          });
          break;
        case OrderListStatusEnum.Accepted:
        case OrderListStatusEnum.Historical:
          router.push({
            name: 'MeasureAcceptedOrderDetail', params: { id: orderId }
          });
          break;
      }
      break;
    case OrderTypeEnum.Design:
      switch (orderListStatus) {
        case OrderListStatusEnum.Unaccepted:
        case OrderListStatusEnum.Quoting:
          router.push({
            name: 'DesignUnacceptedOrderDetail', params: { id: orderId }
          });
          break;
        case OrderListStatusEnum.Accepted:
        case OrderListStatusEnum.Historical:
          router.push({
            name: 'DesignAcceptedOrderDetail', params: { id: orderId }
          });
          break;
      }
      break;
    case OrderTypeEnum.Construction:
      switch (orderListStatus) {
        case OrderListStatusEnum.Unaccepted:
        case OrderListStatusEnum.Quoting:
          router.push({
            name: 'ConstructionUnacceptedOrderDetail', params: { id: orderId }
          });
          break;
        case OrderListStatusEnum.Accepted:
        case OrderListStatusEnum.Historical:
          router.push({
            name: 'ConstructionAcceptedOrderDetail', params: { id: orderId }
          });
          break;
      }
      break;
  }
};

const allOrderList = computed<allOrderListData[]>(() => {
  return [
    ...combineUnacceptedOrders(unacceptedOrderListData.value),
    ...combineQuotingOrders(quotingOrderListData.value),
    ...combineAcceptedOrders(acceptedOrderListData.value),
    ...combineHistoricalOrders(historicalOrderListData.value)
  ];
});

const filteredOrderList = computed(() => {
  let filteredList: allOrderListData[];
  switch (orderShow.value) {
    case 'unaccepted':
      filteredList = allOrderList.value.filter((order) => order.orderListStatus === OrderListStatusEnum.Unaccepted);
      break;
    case 'quoting':
      filteredList = allOrderList.value.filter((order) => order.orderListStatus === OrderListStatusEnum.Quoting);
      break;
    case 'accepted':
      filteredList = allOrderList.value.filter((order) => order.orderListStatus === OrderListStatusEnum.Accepted);
      break;
    case 'historical':
      filteredList = allOrderList.value.filter((order) => order.orderListStatus === OrderListStatusEnum.Historical);
      break;
    default:
      filteredList = allOrderList.value;
  }
  return filteredList.slice().sort((a, b) => {
    return new Date(b.refreshTime).getTime() - new Date(a.refreshTime).getTime();
  });
});

onMounted(async () => {
  await callAllOrder();
});
</script>

<template>
  <div class="w-full my-8 space-y-4">
    <HezDesignerInfo />
    <div class="p-4 mb-4 rounded w-full">
      <div class="flex max-md:flex-col max-md:w-full justify-between gap-2">
        <label
          :class="['md:w-1/6 flex gap-x-2 justify-center items-center t px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'all' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="all" v-model="orderShow" class="hidden">
          所有訂單
        </label>
        <label
          :class="['md:w-1/6 flex gap-x-2 justify-center items-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'unaccepted' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="unaccepted" v-model="orderShow" class="hidden">
          未接單
        </label>
        <label
          :class="['md:w-1/6 flex gap-x-2 justify-center items-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'quoting' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="quoting" v-model="orderShow" class="hidden">
          報價中
        </label>
        <label
          :class="['md:w-1/6 flex gap-x-2 justify-center items-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'accepted' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="accepted" v-model="orderShow" class="hidden">
          已接單
        </label>
        <label
          :class="['md:w-1/6 flex gap-x-2 justify-center items-center px-2 py-4 rounded-xl cursor-pointer cus-btn cus-border',orderShow === 'historical' ? 'border-gray-400':'bg-white text-black']">
          <input type="radio" name="order" value="historical" v-model="orderShow" class="hidden">
          歷史訂單
        </label>
      </div>
    </div>
    <div v-for="order in filteredOrderList" :key="order.orderId"
         class="flex py-4 w-full text-color-secondary">
      <div class="flex flex-col w-full gap-5 space-y-4 md:space-y-0">
        <div
          @click="goToOrderDetail(order.orderId,order.orderType,order.orderListStatus)"
          class="bg-white border border-secondary rounded-xl shadow-lg hover-zoom duration-500 overflow-hidden cursor-pointer">
          <!--    未接單 Step1-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Unaccepted && order.orderType === OrderTypeEnum.Measure">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">空間丈量 (未接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">丈量地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">預約時間</p>
                <div class="flex flex-col space-y-0.5">
                  <p v-for="time in order.measureUnaccepted?.measureTimes" :key="time">{{ time }}</p>
                </div>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>{{ order.designUnaccepted?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    未接單 Step2-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Unaccepted && order.orderType === OrderTypeEnum.Design">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">室內設計 (未接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">室內坪數</p>
                <p>{{ order.designUnaccepted?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計預算</p>
                <p>{{ order.designUnaccepted?.designBudget }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢預算</p>
                <p>{{ order.designUnaccepted?.constructionBudget }}</p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>{{ order.designUnaccepted?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    未接單 Step3-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Unaccepted && order.orderType === OrderTypeEnum.Construction">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">裝潢施工 (未接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">房屋類型</p>
                <p>{{ order.constructionUnaccepted?.houseType }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢坪數</p>
                <p>{{ order.constructionUnaccepted?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢預算</p>
                <p>{{ order.constructionUnaccepted?.constructionBudget }}</p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>{{ order.constructionUnaccepted?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    報價中 Step2-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Quoting && order.orderType === OrderTypeEnum.Design">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">室內設計 (報價中)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">室內坪數</p>
                <p>{{ order.designQuoting?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計報價</p>
                <p>{{ order.designQuoting?.designBudget }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢報價</p>
                <p>{{ order.designQuoting?.constructionBudget }}</p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>{{ order.designQuoting?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    報價中 Step3-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Quoting && order.orderType === OrderTypeEnum.Construction">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">裝潢施工 (報價中)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">房屋類型</p>
                <p>{{ order.constructionQuoting?.houseType }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢坪數</p>
                <p>{{ order.constructionQuoting?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢預算</p>
                <p>{{ order.constructionQuoting?.constructionBudget }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢費用</p>
                <p v-if="!order.constructionQuoting?.isCustomerAccepted">
                  {{ order.constructionQuoting?.constructionEstimate }}</p>
                <p v-else-if="order.constructionQuoting?.constructionAmountNumber === -1">
                  {{ order.constructionQuoting?.constructionEstimate }}</p>
                <p v-else>{{ order.constructionQuoting?.constructionDiscountAmount }}
                  <span
                    v-if="order.constructionQuoting?.constructionAmount !== order.constructionQuoting?.constructionDiscountAmount">
                    (已折抵)
                  </span>
                </p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>狀態：</p>
                  <p>{{ order.constructionQuoting?.cardQuotingHint }}</p>
                </div>
                <div class="flex justify-start">
                  <p>{{ order.constructionQuoting?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    已接單 Step1-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Accepted && order.orderType === OrderTypeEnum.Measure">
            <h3 class="new-bg-color text-white px-6 py-4 max-md:p-3 text-center text-xl">空間丈量 (已接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">丈量地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">丈量時間</p>
                <p>{{ order.measureAccepted?.measureTime }}</p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>{{ order.measureAccepted?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    已接單 Step2-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Accepted && order.orderType === OrderTypeEnum.Design">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">室內設計 (已接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">室內坪數</p>
                <p>{{ order.designAccepted?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計報價</p>
                <p>{{ order.designAccepted?.designAmount }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢報價</p>
                <p>{{ order.designAccepted?.constructionBudget }}</p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start"
                     v-if="order.designAccepted && order.designAccepted.status === DesignOrderStatusEnum.Contracting">
                  <p>訂單：</p>
                  <p>{{ order.designAccepted?.orderAcceptedHint }}</p>
                </div>
                <div class="flex justify-start">
                  <p>狀態：</p>
                  <p>{{ order.designAccepted?.cardHint }}</p>
                </div>
                <div class="flex justify-start"
                     v-if="order.designAccepted && order.designAccepted.status === DesignOrderStatusEnum.Working">
                  <p>2D室內設計：</p>
                  <p>{{ order.designAccepted?.design2DHint }}</p>
                </div>
                <div class="flex justify-start"
                     v-if="order.designAccepted && order.designAccepted.status === DesignOrderStatusEnum.Working">
                  <p>3D模型設計：</p>
                  <p>{{ order.designAccepted?.design3DHint }}</p>
                </div>
                <div class="flex justify-start"
                     v-if="order.designAccepted && order.designAccepted.status === DesignOrderStatusEnum.Working">
                  <p>裝潢報價清單：</p>
                  <p>{{ order.designAccepted?.constructionAmountDocHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    已接單 Step3-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Accepted && order.orderType === OrderTypeEnum.Construction">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">裝潢施工 (已接單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>

              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">房屋類型</p>
                <p>{{ order.constructionAccepted?.houseType }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢坪數</p>
                <p>{{ order.constructionAccepted?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢費用</p>
                <p>{{ order.constructionAccepted?.constructionDiscountAmount }}
                  <span
                    v-if="order.constructionAccepted?.constructionAmount !== order.constructionAccepted?.constructionDiscountAmount">
                    (已折抵)
                  </span>
                </p>
              </div>
              <div class="flex-col mt-2 text-red-600 animate-pulse">
                <div class="flex justify-start">
                  <p>訂單：</p>
                  <p>{{ order.constructionAccepted?.orderAcceptedHint }}</p>
                </div>
                <div class="flex justify-start">
                  <p>狀態：</p>
                  <p>{{ order.constructionAccepted?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    歷史訂單 Step1-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Historical && order.orderType === OrderTypeEnum.Measure">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">空間丈量 (歷史訂單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">丈量地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">丈量時間</p>
                <p>{{ order.measureHistorical?.measureTime }}</p>
              </div>
              <div class="flex-col mt-2 text-green-600">
                <div class="flex justify-start">
                  <p>{{ order.measureHistorical?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    歷史訂單 Step2-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Historical && order.orderType === OrderTypeEnum.Design">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">室內設計 (歷史訂單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">室內坪數</p>
                <p>{{ order.designHistorical?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">設計預算</p>
                <p>{{ order.designHistorical?.designBudget }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢預算</p>
                <p>{{ order.designHistorical?.constructionBudget }}</p>
              </div>
              <div class="flex-col mt-2 text-green-600">
                <div class="flex justify-start">
                  <p>{{ order.designHistorical?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>

          <!--    歷史訂單 Step3-->
          <template
            v-if="order.orderListStatus === OrderListStatusEnum.Historical && order.orderType === OrderTypeEnum.Construction">
            <h3 class="new-bg-color text-white text-center px-6 py-4 max-md:p-3 text-xl">裝潢施工 (歷史訂單)</h3>
            <div class="md:p-5 p-3 text-lg max-md:text-base text-black flex-col">
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">屋主姓名</p>
                <p>{{ order.customerName }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢地址</p>
                <p>{{ order.address }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">房屋類型</p>
                <p>{{ order.constructionHistorical?.houseType }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢坪數</p>
                <p>{{ order.constructionHistorical?.areaPing }}</p>
              </div>
              <div class="flex space-x-1 items-start">
                <p class="font-semibold text-nowrap">裝潢費用</p>
                <p>{{ order.constructionHistorical?.constructionDiscountAmount }}
                  <span
                    v-if="order.constructionHistorical?.constructionAmount !== order.constructionHistorical?.constructionDiscountAmount">
                    (已折抵)
                  </span>
                </p>
              </div>
              <div class="flex-col mt-2 text-green-600">
                <div class="flex justify-start">
                  <p>{{ order.constructionHistorical?.cardHint }}</p>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>

  <DefaultModal :title="VerifyModalTitle" :click-outside-close="false" :showCloseButton="true"
                ref="goToVerifyModalRef" modal-width="max-w-md"
                @closeModal="goToVerifyModalRef?.closeModal()">
    <div class="flex flex-col gap-2 p-4 mx-auto">
      <p class="font-bold">尚未完成</p>
      <p v-if="!verifyStore.taiwanIdVerify" class="text-red-600 font-bold">身分驗證</p>
      <p v-if="!verifyStore.companyVerify" class="text-red-600 font-bold">公司驗證</p>
      <p v-if="!verifyStore.emailVerify" class="text-red-600 font-bold">email驗證</p>
      <button class="bg-color-button text-black px-4 py-2 rounded-md text-nowrap hover:bg-color-selected"
              @click="goToProfile()">
        前往驗證
      </button>
    </div>
  </DefaultModal>
</template>
