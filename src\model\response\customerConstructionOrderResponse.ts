import { BaseResponse } from '@/model/response/baseResponse.ts';
import { Budget } from '@/model/general/budget.ts';
import {
  AcceptanceProcessStatusEnum, AmountStatusEnum, AppendProcessStatusEnum,
  ConstructionOrderStatusEnum, ConstructionProcessStatusEnum,
  RemittanceStatusEnum, SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import { AddressItem, ConstructionAmountItem, FileUrlItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import {
  CustomerImagePdfCadContent,
  CustomerImageVideoContent, CustomerMeasureContent, CustomerMediaUrlItem
} from '@/model/response/customerMeasureOrderResponse.ts';

export interface CustomerGetOneConstructionOrderDetailResponse extends BaseResponse {
  result: CustomerOrderConstructionItem;
}

export interface CustomerGetManyConstructionOrderDetailResponse extends BaseResponse {
  result: CustomerBasicOrderConstructionItem[];
}

export interface CustomerBasicOrderConstructionItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: ConstructionOrderStatusEnum;
  contract: {
    signingStatus: SigningStatusEnum;
    acceptanceProcessStatus: AcceptanceProcessStatusEnum;
    remittanceStatus: RemittanceStatusEnum;
    constructionEstimate: Budget;
    constructionAmount: number;
    constructionDiscountAmount: number;
  };
  isDeleted: boolean;
  designerName: string;
  constructionContent: {
    process: {
      name: string;
      updateTime: string;
      updateCount: number;
    }[]
  };
  quotationCount: number;
  invoiceId: string;
}

export interface CustomerOrderConstructionItem {
  orderId: string;
  createTime: string;
  refreshTime: string;
  status: ConstructionOrderStatusEnum;
  contract: {
    signingStatus: SigningStatusEnum;
    acceptanceProcessStatus: AcceptanceProcessStatusEnum;
    remittanceStatus: RemittanceStatusEnum;
    constructionEstimate: Budget;
    constructionAmount: number;
    constructionDiscountAmount: number;
    amountStatus: AmountStatusEnum;
    constructionAmounts: ConstructionAmountItem[];
    remittedAmount: number;
    verifiedAmount: number;
    bigDataEstimate: Budget;
    appendProcess: AppendProcess;
  };
  isDeleted: boolean;
  designerId: string;
  designerName: string;
  designerAvatar: string;
  designerPhone: string;
  customerId: string;
  customerName: string;
  address: AddressItem;
  publishInfo: CustomerConstructionPublishInfo;
  measureContent: CustomerMeasureContent;
  designContent: {
    design2D: CustomerImagePdfCadContent;
    design3D: CustomerImageVideoContent;
  };
  constructionContent: {
    process: CustomerConstructionProcessContent;
  };
  quotationCount: number;
  chatRoomId: string;
  orderRatingId: string;
  invoiceId: string;
}

export interface AppendProcess {
  updateTime: string;
  status: AppendProcessStatusEnum;
  times: number; // 已完成新增幾次追加工程 客戶同意之後才會+1
  process: AppendProcessDetails;
}

export interface AppendProcessDetails {
  name: string;
  amount: number;
  amountDocUrl: string;
}

export interface CustomerConstructionPublishInfo {
  isContinuedDesigner: boolean;
  constructionBudget: Budget;
  constructionNote: string;
  spaceUsage: string;
  designStyle: string;
  designTheme: string;
  keyDesignDetail: string;
  referenceImages: FileUrlItem[];
}

export interface CustomerConstructionProcessContent {
  updateTime: string;
  updateCount: number;
  content: CustomerConstructionProcess[];
}

export interface CustomerConstructionProcess {
  updateTime: string;
  name: string;
  media: CustomerMediaUrlItem[];
  key: string;
  updateCount: number;
  status: ConstructionProcessStatusEnum;
  amount: number;
  isAppend: boolean; //是否為追加工程 是的話元件顯示文字要變
  amountDocUrl: string;
}
