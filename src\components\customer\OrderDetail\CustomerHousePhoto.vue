<script setup lang="ts">
import { CustomerImageVideoContent, CustomerImageVideoItem } from '@/model/response/customerMeasureOrderResponse.ts';
import { ref } from 'vue';
import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import CustomerMediaModal from '@/components/customer/CustomerMediaModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';

const housePhoto = defineModel<CustomerImageVideoContent>({ required: true });
const customerMediaModalRef = ref<InstanceType<typeof CustomerMediaModal> | null>(null);

const openMediaModal = (imageVideoItem: CustomerImageVideoItem) => {
  customerMediaModalRef.value?.openMediaModal(imageVideoItem);
};

</script>

<template>
  <div class="flex flex-col cus-border my-1 gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">房屋照片</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div v-if="housePhoto.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col" v-else>
        <div v-for="(imageVideoItem,index) in housePhoto.content" :key="index"
             class="flex justify-start text-lg text-color-primary mb-4">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold">{{ imageVideoItem.name }}</p>
            <div v-if="imageVideoItem.media.length === 0" class="flex">
              <p class="text-black font-bold">(丈量師未上傳資料)</p>
            </div>
            <div v-else class="flex">
              <button class="cus-btn button-padding text-base"
                      v-if="imageVideoItem.type === MediaTypeEnum.Video" @click="openMediaModal(imageVideoItem)">
                查看影片
              </button>
              <button class="cus-btn button-padding text-base"
                      v-else-if="imageVideoItem.type === MediaTypeEnum.Image" @click="openMediaModal(imageVideoItem)">
                查看圖片
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <CustomerMediaModal ref="customerMediaModalRef" />
</template>
