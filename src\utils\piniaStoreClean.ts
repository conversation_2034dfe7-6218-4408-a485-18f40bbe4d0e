import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useCustomerInfoStore } from '@/stores/customerGlobal.ts';

export const piniaStoreClean = (isDesigner: boolean) => {
  if (isDesigner) {
    const DesignerInfoStore = useDesignerInfoStore();
    DesignerInfoStore.clearLoginStore();
    DesignerInfoStore.clearGuestToken();
  } else {
    const CustomerInfoStore = useCustomerInfoStore();
    CustomerInfoStore.clearLoginStore();
    CustomerInfoStore.clearGuestToken();
  }
};

export const piniaPasswordClean = (isDesigner: boolean) => {
  if (isDesigner) {
    const DesignerInfoStore = useDesignerInfoStore();
    DesignerInfoStore.clearPassword();
  } else {
    const CustomerInfoStore = useCustomerInfoStore();
    CustomerInfoStore.clearPassword();
  }
};
