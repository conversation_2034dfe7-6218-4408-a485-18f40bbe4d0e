<script setup lang="ts">
import { useRoute } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import HezDivider from '@/components/General/HezDivider.vue';
import {
  AcceptanceProcessStatusEnum,
  AmountStatusEnum,
  AppendProcessStatusEnum,
  ConstructionOrderStatusEnum,
  SigningStatusEnum
} from '@/model/enum/orderStatus.ts';
import HouseNote from '@/components/Order/OrderDetail/SubComponets/HouseNote.vue';
import HousePhoto from '@/components/Order/OrderDetail/SubComponets/HousePhoto.vue';
import FloorPlan from '@/components/Order/OrderDetail/SubComponets/FloorPlan.vue';
import HouseCheck from '@/components/Order/OrderDetail/SubComponets/HouseCheck.vue';
import TextContent from '@/components/Order/OrderDetail/SubComponets/TextContent.vue';
import { OrderDetailService } from '@/api/designerOrder.ts';
import {
  ConstructionAmountItem,
  ConstructionOrderAcceptedDetailItem,
  ImagePdfCadKeyItem,
  ImageVideoKeyItem,
  ProcessKeyItem,
  TextKeyItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { budgetCombineLowToHigh, calculatePercentage, moneyAddCommas } from '@/utils/budgetFormat.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import DesignerSignModal from '@/components/Modal/DesignerSignModal.vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import Upload2D from '@/components/Order/OrderDetail/SubComponets/Upload2D.vue';
import Upload3D from '@/components/Order/OrderDetail/SubComponets/Upload3D.vue';
import DesignerAcceptanceProcessModal from '@/components/Modal/DesignerAcceptanceProcessModal.vue';
import ConstructionHouseInfo from '@/components/Order/OrderDetail/SubComponets/ConstructionHouseInfo.vue';
import CustomerAppDownload from '@/components/General/CustomerAppDownload.vue';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import WorkProcess from '@/components/Order/OrderDetail/SubComponets/WorkProcess.vue';
import { createMeet } from '@/utils/LiveKitService.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  VideoCameraIcon
} from '@heroicons/vue/24/outline';
import { useDesignerInfoStore } from '@/stores/designerGlobal.ts';
import { useChatHubStore } from '@/stores/global.ts';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import { formatFullDate } from '@/utils/timeFormat.ts';

const route = useRoute();
const designerInfoStore = useDesignerInfoStore();
const chatHubStore = useChatHubStore();
const orderId = route.params.id as string;
const signModalTitle = ref('合約簽署');
const acceptanceProcessModalTitle = ref('匯款狀態');
const signModal = ref<InstanceType<typeof DesignerSignModal>>();
const acceptanceProcessModal = ref<InstanceType<typeof DesignerAcceptanceProcessModal>>();
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal>>();
const appendProcessModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);

const orderData = ref<ConstructionOrderAcceptedDetailItem>({
  designerId: '',
  orderId: '',
  createTime: '',
  refreshTime: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  publishInfo: {
    spaceUsage: '',
    designStyle: '',
    designTheme: '',
    constructionBudget: {
      upper: 0,
      lower: 0
    },
    constructionNote: '',
    keyDesignDetail: '',
    referenceImages: [],
    isContinuedDesigner: false
  },
  measureContent: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: [] as TextKeyItem[]
    }
  },
  chatRoomId: '',
  customerId: '',
  customerAvatarUrl: '',
  customerPhone: '',
  contract: {
    constructionEstimate: {
      upper: 0,
      lower: 0
    },
    //是否為詳細報價階段
    isDetailQuoted: false,
    amountStatus: AmountStatusEnum.NotQuoted,
    //沒有延續前設計師才用這個
    constructionAmountDocs: [] as ConstructionAmountItem[],
    constructionAmount: -1,
    constructionDiscountAmount: -1,
    acceptanceProcessStatus: AcceptanceProcessStatusEnum.Init,
    signingStatus: SigningStatusEnum.Init,
    isRemittanceInformationSet: false,
    customerRemittanceAmount: -1,
    appendProcess: {
      updateTime: '',
      status: AppendProcessStatusEnum.InitOrCompleted,
      times: 0,
      process: {
        name: '',
        amount: 0,
        amountDocUrl: ''
      }
    },
    constructionDays: -1 ,// 工期
    constructionRemainingDays: 0,
    constructionWorkingEndDate: '',
    constructionPenaltyAmount: 0
  },
  status: ConstructionOrderStatusEnum.Contracting,
  designContent: {
    design2D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImagePdfCadKeyItem[]
    },
    design3D: {
      updateTime: '',
      updateCount: 0,
      content: [] as ImageVideoKeyItem[]
    },
    constructionAmountDocs: [] as ConstructionAmountItem[]
  },
  isDeleted: false,
  constructionContent: {
    process: {
      updateTime: '',
      updateCount: 0,
      content: [] as ProcessKeyItem[]
    }
  }
});

const orderDataDetail = computed(() => {
  return {
    status: orderData.value.status,
    designerId: orderData.value.designerId,
    orderId: orderData.value.orderId,
    customerName: orderData.value.customerName,
    address: orderData.value.address.fullName,
    publishInfo: orderData.value.publishInfo,
    measureContent: orderData.value.measureContent,
    contract: {
      signingStatus: orderData.value.contract.signingStatus,
      isRemittanceInformationSet: orderData.value.contract.isRemittanceInformationSet,
      constructionAmount: moneyAddCommas(orderData.value.contract.constructionAmount),
      customerRemittanceAmount: moneyAddCommas(orderData.value.contract.customerRemittanceAmount),
      constructionDiscountAmount: moneyAddCommas(orderData.value.contract.constructionDiscountAmount),
      appendProcess: {
        updateTime: formatFullDateTimeWithDay(orderData.value.contract.appendProcess.updateTime),
        status: orderData.value.contract.appendProcess.status,
        times: orderData.value.contract.appendProcess.times,
        process: {
          name: orderData.value.contract.appendProcess.process.name,
          amount: moneyAddCommas(orderData.value.contract.appendProcess.process.amount),
          amountDocUrl: orderData.value.contract.appendProcess.process.amountDocUrl
        }
      }
    },
    designContent: {
      design2D: orderData.value.designContent.design2D,
      design3D: orderData.value.designContent.design3D,
      constructionAmountDocs: orderData.value.designContent.constructionAmountDocs
    },
    constructionContent: orderData.value.constructionContent,
    chatRoomId: orderData.value.chatRoomId,
    nameAddress: {
      name: orderData.value.customerName,
      address: orderData.value.address.fullName
    },
    customerConstructionBudget: budgetCombineLowToHigh(orderData.value.publishInfo.constructionBudget),
    isDiscounted: orderData.value.contract.constructionAmount !== orderData.value.contract.constructionDiscountAmount,
    publishInfoForHouseInfo: orderData.value.publishInfo,
    workDataAmount: orderData.value.constructionContent.process.content.map((item) => {
      return `${moneyAddCommas(item.amount)} (${calculatePercentage(item.amount, orderData.value.contract.constructionAmount)})`;
    })
  };
});

const showSignOrAcceptanceProcessModal = (title?: string) => {
  if (orderDataDetail.value.contract.signingStatus !== SigningStatusEnum.Verified) {
    if (title) {
      signModalTitle.value = title;
    } else {
      signModalTitle.value = '合約簽署';
    }
    signModal.value?.openModal();
  } else {
    if (title) {
      acceptanceProcessModalTitle.value = title;
    } else {
      acceptanceProcessModalTitle.value = '確認驗收流程';
    }
    acceptanceProcessModal.value?.openModal();
  }
};

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      appUsePhoneModalRef.value?.openModal();
      break;
    case contactItemEnum.Meet:
      createMeet(orderDataDetail.value.chatRoomId, true, designerInfoStore.userId);
      break;
    case contactItemEnum.Chat:
      if (chatHubStore._worker === null) {
        sharedWorkerNotSupportModalRef.value?.openModal();
      } else {
        chatRoomRef.value?.showChatRoom();
      }
      break;
    case contactItemEnum.Note:
      noteBookRef.value?.openModal();
      break;
  }
};

const orderInfoClicked = () => {
  if (orderDataDetail.value.status === ConstructionOrderStatusEnum.Contracting) {
    showSignOrAcceptanceProcessModal();
  }
};

const appendProcessClicked = () => {
  appendProcessModalRef.value?.openModal();
};

const getOrderData = async () => {
  const res = await OrderDetailService.getConstructionAccepted({ orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    orderData.value = res.result;
  }
};

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

onMounted(async () => {
  await getOrderData();
});
</script>

<template>
  <div class="flex flex-col my-8 gap-y-8 w-full">
    <div class="cus-border text-lg">
      <h2 class="text-2xl text-center font-bold">裝潢施工</h2>
      <div class="flex p-4 flex-col gap-y-1">
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">屋主姓名</p>
          <p class="font-bold">{{ orderDataDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">裝潢地址</p>
          <p class="font-bold">{{ orderDataDetail.address }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="font-bold text-nowrap">裝潢施工費</p>
          <p class="font-bold">{{ orderDataDetail.contract.constructionAmount }}</p>
        </div>
        <div class="flex gap-x-2"
             v-if="orderDataDetail.isDiscounted">
          <p class="font-bold text-nowrap">優惠總金額</p>
          <p class="font-bold">{{ orderDataDetail.contract.constructionDiscountAmount }}</p>
        </div>
        <!--        開始工作才出現 -->
        <p class="font-bold text-blue-600" v-if="orderDataDetail.status >= ConstructionOrderStatusEnum.Working">
          如果需要使用分享桌面或傳送CAD檔案的功能，建議使用電腦的網頁上操作
        </p>
      </div>
      <HezDivider />
      <div class="flex justify-around">
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Phone)">
          <PhoneIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">免費語音</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Meet)">
          <VideoCameraIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">視訊會議</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Chat)">
          <ChatBubbleLeftEllipsisIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">聊天室</span>
        </button>
        <button class="flex flex-col items-center text-center hover-zoom"
                @click="contactItemClicked(contactItemEnum.Note)">
          <ClipboardDocumentListIcon class="mb-2 md:h-16 md:w-16 h-8 w-8" />
          <span class="font-bold max-md:text-base">紀錄本</span>
        </button>
      </div>
    </div>

    <div v-if="orderData.chatRoomId && chatHubStore._worker !== null"
         class="fixed flex flex-col justify-end bottom-0 right-0 w-80 z-40"
         :class="isChatRoomShow ? 'h-3/5' : 'h-auto' ">
      <ChatRoom :is-for-meet="false"
                :room-id="orderData.chatRoomId" :is-designer="true"
                @show-chat-room="changeChatRoomShow"
                ref="chatRoomRef" />
    </div>
    <div>
      <p class=" font-bold md:text-lg break-all text-black">訂單進度</p>
      <p class=" font-bold md:text-lg break-all text-red-600">(完工時間：{{ formatFullDate(orderData.contract.constructionWorkingEndDate) }}，延遲罰款：{{ orderData.contract.constructionPenaltyAmount }}元)</p>
    </div>
    <!--    固定客戶匯款資訊區同時也是簽約&驗收流程狀態區 -->
    <!-- CustomerInfo Block -->
    <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
         @click="orderInfoClicked()">
      <div class="flex items-center">
        <div
          class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
          1
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex gap-3 ">
            <p class="font-bold text-nowrap md:text-lg">客戶需支付裝潢費用</p>
            <p class="font-bold md:text-lg"> {{ orderDataDetail.contract.constructionDiscountAmount }}
              <span v-if="orderDataDetail.isDiscounted">(已折抵)</span></p>
          </div>
          <div class="flex gap-3" v-if="orderDataDetail.status === ConstructionOrderStatusEnum.Contracting">
            <p class=" font-bold md:text-lg text-nowrap">合約狀態</p>
            <p v-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Init"
               class=" font-bold md:text-lg text-red-600">未簽署</p>
            <p v-else-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Verifying"
               class=" font-bold md:text-lg text-blue-600">已簽署(審核中)</p>
            <p v-else-if="orderDataDetail.contract.signingStatus === SigningStatusEnum.Verified"
               class=" font-bold md:text-lg text-black">已簽署</p>
            <p v-else class=" font-bold md:text-lg text-red-600">已簽署(審核失敗)</p>
          </div>
          <div class="flex gap-3" v-if="orderDataDetail.status === ConstructionOrderStatusEnum.Contracting">
            <p class=" font-bold md:text-lg text-nowrap">流程狀態</p>
            <p v-if="orderDataDetail.contract.appendProcess.status < AcceptanceProcessStatusEnum.AgreementReached"
               class=" font-bold md:text-lg text-red-600">未確認</p>
            <p v-else class=" font-bold md:text-lg text-black">已確認</p>
          </div>
          <div class="flex gap-3 ">
            <p class=" font-bold md:text-lg text-nowrap">狀態</p>
            <p v-if="orderDataDetail.status === ConstructionOrderStatusEnum.Contracting"
               class=" font-bold md:text-lg text-red-600">客戶未匯款</p>
            <p
              v-else-if="orderDataDetail.contract.customerRemittanceAmount !== orderDataDetail.contract.constructionDiscountAmount"
              class=" font-bold md:text-lg text-red-600">客戶已匯款
              {{ orderDataDetail.contract.customerRemittanceAmount }}</p>
            <p v-else class=" font-bold md:text-lg text-black">客戶已匯款
              {{ orderDataDetail.contract.customerRemittanceAmount }}</p>
          </div>
          <div class="flex gap-3"
               v-if="orderData.contract.constructionDays !== -1 && orderData.status === ConstructionOrderStatusEnum.Working">
            <p class=" font-bold md:text-lg text-nowrap">工期</p>
            <p class=" font-bold md:text-lg break-all text-black">{{ orderData.contract.constructionDays }} 工作天</p>
            <p class=" font-bold md:text-lg break-all text-red-600">(剩 {{ orderData.contract.constructionRemainingDays }} 工作天)</p>
          </div>
        </div>
      </div>
    </div>


    <template v-if="orderDataDetail.status >= ConstructionOrderStatusEnum.Working">
      <!--    施工流程區-->
      <WorkProcess :order-id="orderId"
                   v-model:work-process="orderDataDetail.constructionContent.process.content"
                   v-model:work-data-amount="orderDataDetail.workDataAmount"
                   @refresh-data="getOrderData()" />

      <!--      追加工程 (如果有)-->
      <div class="cus-border p-4 md:py-8 md:px-24 rounded-lg flex items-center justify-between"
           v-if="orderDataDetail.contract.appendProcess.process.name !== '' && orderDataDetail.contract.appendProcess.status !== AppendProcessStatusEnum.CustomerDisagreed"
           @click="appendProcessClicked()">
        <div class="flex items-center">
          <div
            class="flex-shrink-0 w-10 h-10 md:w-14 md:h-14 md:text-xl rounded-full flex items-center justify-center mr-4 text-white new-bg-color">
            {{ (orderDataDetail.constructionContent.process.content.length) + 2 }}
          </div>
          <div class="flex flex-col gap-3">
            <div class="flex gap-3 ">
              <p class="font-bold md:text-lg text-nowrap">設計師完成</p>
              <p class=" font-bold md:text-lg text-black">
                {{ orderDataDetail.contract.appendProcess.process.name }}</p>
            </div>
            <div class="flex gap-3 ">
              <p class=" font-bold md:text-lg text-nowrap">工程款</p>
              <p class=" font-bold md:text-lg text-black">
                {{ orderDataDetail.contract.appendProcess.process.amount }}</p>
            </div>
            <div class="flex gap-3">
              <p class=" font-bold md:text-lg text-nowrap">狀態</p>
              <p class=" font-bold md:text-lg text-blue-600">客戶正在確認追加工程</p>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!--固定資訊區-->
    <Upload2D :order-id="orderId" :disabled="true"
              v-model:design2-d="orderDataDetail.designContent.design2D" />
    <Upload3D :order-id="orderId" :disabled="true"
              v-model:design3-d="orderDataDetail.designContent.design3D" />
    <ConstructionHouseInfo v-model:house-info="orderDataDetail.measureContent.houseInfo"
                           v-model:publish-info="orderDataDetail.publishInfoForHouseInfo"
                           v-model:name-address="orderDataDetail.nameAddress" />
    <HousePhoto v-model="orderDataDetail.measureContent.photos" :orderId="orderId" :disabled="true" />
    <HouseCheck v-model="orderDataDetail.measureContent.houseCheck" :orderId="orderId" :disabled="true" />
    <FloorPlan v-model="orderDataDetail.measureContent.floorPlan" :orderId="orderId" :disabled="true" />
    <TextContent v-model="orderDataDetail.measureContent.constructionRequest" :orderId="orderId" title="裝潢需求" contentName="constructionRequest" :canNotDeleteCount=16 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.waterQuality" :orderId="orderId" title="水質檢測" contentName="waterQuality" :canNotDeleteCount=6 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.airQuality" :orderId="orderId" title="空氣檢測" contentName="airQuality" :canNotDeleteCount=7 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.noise" :orderId="orderId" title="噪音檢測" subTitle="(安靜:50dB，吵雜:60dB)" contentName="noise" :canNotDeleteCount=1 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.humidity" :orderId="orderId" title="濕度檢測" subTitle="(正常:40~60%，潮濕:70%)" contentName="humidity" :canNotDeleteCount=1 :disabled="true" :canAddUnit="false"/>
    <TextContent v-model="orderDataDetail.measureContent.radiation" :orderId="orderId" title="電磁輻射" contentName="radiation" :canNotDeleteCount=4 :disabled="true" :canAddUnit="false"/>
    <HouseNote v-model="orderDataDetail.measureContent.note" :orderId="orderId" :disabled="true" />
  </div>

  <NoteBook :room-id="orderData.chatRoomId" :is-designer="true" ref="noteBookRef" />
  <AppUsePhoneModal :user-type="UserTypeEnum.Designer" ref="appUsePhoneModalRef" />
  <DesignerSignModal :title="signModalTitle" ref="signModal" />
  <DesignerAcceptanceProcessModal :title="acceptanceProcessModalTitle" ref="acceptanceProcessModal" />

  <DefaultModal title="追加工程" :show-close-button="true" :click-outside-close="true" modalWidth="max-w-xl"
                ref="appendProcessModalRef" @closeModal="appendProcessModalRef?.closeModal()">
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col p-2 items-start">
        <p class="font-bold md:text-lg">如果您需要確認追加工程</p>
        <p class="font-bold md:text-lg">請至App端操作</p>
      </div>
      <CustomerAppDownload />
    </div>
  </DefaultModal>

  <AndroidNotSupportSharedWorkerModal :is-designer="true" ref="sharedWorkerNotSupportModalRef" />
</template>
