import { MediaTypeEnum } from '@/model/enum/mediaType.ts';
import { SetI<PERSON>Builder, SetImagePdfCadBuilder } from '@/model/request/orderDetail/measureDataUpdateRequest.ts';

export interface Add2DItemRequest {
  orderId: string;
  names: string[];
}

export interface Add3DItemRequest {
  orderId: string;
  items: {
    name: string;
    type: MediaTypeEnum;
  }[];
}

export interface Update2DRequest {
  orderId: string;
  builders: SetImagePdfCadBuilder[];
}

export interface Update3DRequest {
  orderId: string;
  builders: SetImageBuilder[];
}

export interface FinishWorkRequest {
  orderId: string;
}

export interface UpdateConstructionFeeRequest {
  orderId: string;
  constructionAmount: number;
  constructionDocumentUrl: string;
}
