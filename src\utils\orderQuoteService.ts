const formatToWan = (amount: number) => Math.ceil(amount / 10000);

// 設計報價初始值->坪數 *0.5 無條件進位
export const initDesignAmountInput = (amount: number, areaPing: number) => {
  return amount === -1 ? Math.ceil(areaPing * 0.5) : formatToWan(amount);
};

// 裝潢報價(最高)初始值->坪數 *12 無條件進位
export const initConstructionUpperAmountInput = (amount: number, areaPing: number) => {
  return amount === -1 ? Math.ceil(areaPing * 12) : formatToWan(amount);
};

// 裝潢報價(最低)初始值->坪數 *9 無條件進位
export const initConstructionLowerAmountInput = (amount: number, areaPing: number) => {
  return amount === -1 ? Math.ceil(areaPing * 9) : formatToWan(amount);
};

// 裝潢報價(最高)下限->(坪數 *9 無條件進位) +1
export const constructionUpperAmountMin = (areaPing: number) => Math.ceil(areaPing * 9) + 1;

// 裝潢報價(最低)上限->裝潢報價(最高)
export const constructionLowerAmountMax = (Upper: number) => Upper - 1;

// 裝潢報價(最低)下限->裝潢報價(最高) *0.7 無條件進位
export const constructionLowerAmountMin = (amount: number) => Math.ceil(amount * 0.7);
