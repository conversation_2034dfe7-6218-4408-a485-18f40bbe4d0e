<script setup lang="ts">
import { ref } from 'vue';
import { ImageVideoKeyContent, ImageVideoKeyItem } from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import MediaModal from '@/components/General/MediaModal.vue';
import HouseCheckUpdateModal from '@/components/Order/OrderDetail/UpdateModal/HouseCheckUpdateModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';

const houseCheck = defineModel<ImageVideoKeyContent>({ required: true });
const updateModalRef = ref<InstanceType<typeof HouseCheckUpdateModal> | null>(null);
const mediaModalRef = ref<InstanceType<typeof MediaModal> | null>(null);

const props = defineProps<{
  orderId: string;
  disabled: boolean;
}>();

const openUpdateModal = () => {
  updateModalRef.value?.openModal();
};

const openMediaModal = (imageVideoItem: ImageVideoKeyItem) => {
  mediaModalRef.value?.openMediaModal(imageVideoItem);
};

</script>

<template>
  <div class="flex flex-col my-1 cus-border gap-4">
    <div class="flex justify-evenly text-2xl items-center">
      <p class=" font-bold text-color-primary text-center">屋況紀錄(非專業技師)</p>
    </div>
    <HezDivider />
    <div class="flex justify-start">
      <div v-if="houseCheck.content.length === 0">
        <p class="text-lg font-bold">未上傳</p>
      </div>
      <div class="flex flex-col gap-y-2" v-else>
        <div v-for="imageVideoItem in houseCheck.content" :key="imageVideoItem.key"
             class="flex justify-start text-lg text-color-primary">
          <div class="flex md:gap-x-2 gap-x-0.5 items-center">
            <p class="font-bold p-2">{{ imageVideoItem.name }}</p>
            <div v-if="imageVideoItem.media.length === 0" class="flex">
              <p class="">未上傳</p>
            </div>
            <div v-else class="flex">
              <button class="cus-btn button-padding text-base"
                      @click="openMediaModal(imageVideoItem)"> 查看圖片
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="w-full cus-btn button-padding text-xl"
            v-if="!disabled" @click="openUpdateModal()">
      資料上傳/修改
    </button>
  </div>
  <HouseCheckUpdateModal v-model="houseCheck" title="屋況紀錄(非專業技師)" :orderId="props.orderId" ref="updateModalRef" />
  <MediaModal ref="mediaModalRef" />
</template>
