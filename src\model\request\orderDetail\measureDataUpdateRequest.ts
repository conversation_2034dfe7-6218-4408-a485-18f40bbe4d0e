import { MediaTypeEnum } from '@/model/enum/mediaType.ts';

export interface AddTextItemRequest {
  orderId: string;
  names: string[];
}

export interface AddHousePhotosItemRequest {
  orderId: string;
  items: {
    name: string;
    type: MediaTypeEnum;
  }[];
}

export interface UpdateSetUnitRequest {
  orderId: string;
  key: string;
  setUnit: string;
}

export interface UpdateTextContentRequest {
  orderId: string;
  builders: {
    key: string;
    setName: string;
    setText: string;
  }[];
}

export interface UpdateImageVideoContentRequest {
  orderId: string;
  builders: SetImageBuilder[];
}

export interface UpdateImagePdfCadContentRequest {
  orderId: string;
  builders: SetImagePdfCadBuilder[];
}

export interface DeleteItemRequest {
  orderId: string;
  keys: string[];
}

export interface SetImageBuilder {
  key: string; // 項目key
  setName: string; // 項目名稱
  pushMedia: {
    url: string; //一個項目中 單個影片/照片的S3url
    description: string; //一個項目中 單個影片/照片的註解
  }[]; //新增影片/照片
  pullMedia: string[]; // 影片/照片自己的 key
  setMediaDescription: {
    key: string;  // 照片影片自己的key
    description: string; // 照片影片自己的註解
  }[]; //更新影片/照片的註解
}

export interface SetImagePdfCadBuilder {
  key: string; // 項目key
  setName: string; // 項目名稱
  pushImage: {
    url: string; //一個項目中 單個照片的S3url
    description: string; //一個項目中 單個照片的註解
  }[]; //新增照片
  pullImage: string[]; // 照片自己的 key
  setImageDescription: {
    key: string;  // 照片自己的key
    description: string; // 照片自己的註解
  }[]; //更新照片的註解
  setPdf: string; // Pdf S3 url
  setCad: string; // Cad S3 url
}
