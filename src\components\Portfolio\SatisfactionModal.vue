<script setup lang="ts">
import { onMounted, ref } from 'vue';
import HezDivider from '@/components/General/HezDivider.vue';
import { PortfolioService } from '@/api/portfolio.ts';
import { ratingDataFormat } from '@/utils/ratingFormat.ts';
import { RatingFormated } from '@/model/formatted/portolio.ts';
import { RatingTypeEnum } from '@/model/enum/ratingType.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { GetDesignerRateResponse } from '@/model/response/portfolioResponse.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { UserCircleIcon } from '@heroicons/vue/24/outline';

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const ratingData = ref<RatingFormated[]>();
const props = defineProps({
  designerId: {
    type: String,
    required: true
  },
  isDesigner: {
    type: Boolean,
    required: true
  }
});

const closeModal = () => {
  modalRef.value?.closeModal();
};

const openSatisfactionModal = () => {
  modalRef.value?.openModal();
};
const getRatingData = async (designerId: string) => {
  let res: GetDesignerRateResponse;
  if (props.isDesigner) {
    res = await PortfolioService.getDesignerRateByDesigner({ designerId: designerId });
  } else {
    res = await PortfolioService.getDesignerRateByCustomer({ designerId: designerId });
  }
  if (res.status === APIStatusCodeEnum.Success) {
    ratingData.value = ratingDataFormat(res.result);
  }
};

onMounted(() => {
  getRatingData(props.designerId);
});

defineExpose({ openSatisfactionModal });
</script>

<template>
  <DefaultModal title="用戶評價" :show-close-button="true" :click-outside-close="true" modal-width="md:max-w-xl"
                ref="modalRef"
                @closeModal="closeModal">
    <div v-for="rate in ratingData" :key="rate.orderId" class="flex flex-col space-y-4 p-4 md:text-lg">
      <div class="flex flex-row gap-x-3 max-md:flex-col items-center">
        <img v-if="rate.customerAvatar" :src="rate.customerAvatar" alt="customerAvatar"
             class="w-12 h-12 rounded-full" />
        <UserCircleIcon v-else class="w-12 h-12 rounded-full" />
        <p class="text-lg">{{ rate.customerName }}</p>
        <div class="cus-border items-center p-2">
          <p class="font-bold">{{ rate.ratingTitle }}</p>
        </div>
      </div>
      <div v-if="rate.ratingType === RatingTypeEnum.Measure"
           class="mt-1 md:mt-2 p-2 flex flex-col md:gap-x-10 max-md:text-sm">
        <div class="flex flex-col gap-2">
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">準時抵達</p>
            <img v-for="star in rate.measureRating.arrivalOnTime.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.measureRating.arrivalOnTime.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">服務態度</p>
            <img v-for="star in rate.measureRating.serviceAttitude.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.measureRating.serviceAttitude.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">專業品質</p>
            <img v-for="star in rate.measureRating.professionalQuality.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.measureRating.professionalQuality.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">完成時間</p>
            <img v-for="star in rate.measureRating.completionTime.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.measureRating.completionTime.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
        </div>
      </div>

      <div v-if="rate.ratingType === RatingTypeEnum.Design"
           class="mt-1 md:mt-2 p-2 flex flex-col md:gap-x-10 max-md:text-sm">
        <div class="flex flex-col gap-2">
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">作品質量</p>
            <img v-for="star in rate.designRating.workQuality.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.designRating.workQuality.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">態度熱忱</p>
            <img v-for="star in rate.designRating.enthusiasticAttitude.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.designRating.enthusiasticAttitude.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">設計效率</p>
            <img v-for="star in rate.designRating.designEfficiency.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.designRating.designEfficiency.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
        </div>
      </div>

      <div v-if="rate.ratingType === RatingTypeEnum.Construction"
           class="mt-1 md:mt-2 p-2 flex flex-col md:gap-x-10 max-md:text-sm">
        <div class="flex flex-col gap-2">
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">施工品質</p>
            <img v-for="star in rate.constructionRating.constructionQuality.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.constructionRating.constructionQuality.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">服務態度</p>
            <img v-for="star in rate.constructionRating.serviceAttitude.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.constructionRating.serviceAttitude.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
          <div class="flex gap-x-0.5  max-md:justify-center">
            <p class="mr-2">完工時間</p>
            <img v-for="star in rate.constructionRating.serviceAttitude.star" :key="star"
                 src="/vectors/general/star.svg" alt="star" class="w-6 h-6" />
            <img v-for="nonStar in rate.constructionRating.serviceAttitude.nonStar" :key="nonStar"
                 src="/vectors/general/nonStar.svg" alt="nonStar" class="w-6 h-6" />
          </div>
        </div>
      </div>
      <div class="p-2 gap-2 flex flex-col justify-center items-start md:gap-x-10 max-md:text-sm">
        <div class="flex gap-x-0.5  max-md:justify-center">
          <p class="text-nowrap mr-2">{{ rate.addressTitle }}</p>
          <p>{{ rate.address }}</p>
        </div>
        <div class="flex gap-x-0.5  max-md:justify-center">
          <p class="text-nowrap mr-2">{{ rate.housePingTitle }}</p>
          <p>{{ rate.housePing }}</p>
        </div>
        <div class="flex gap-x-0.5  max-md:justify-center">
          <p class="text-nowrap mr-2">{{ rate.amountTitle }}</p>
          <p>{{ rate.amount }}</p>
        </div>
        <div class="flex gap-x-0.5 text-start  max-md:justify-center">
          <p class="text-nowrap mr-2">{{ rate.durationTitle }}</p>
          <p class="break-all">{{ rate.duration }}</p>
        </div>

        <p class="text-base text-start">{{ rate.comment }}</p>

      </div>
      <HezDivider />
    </div>
  </DefaultModal>
</template>
