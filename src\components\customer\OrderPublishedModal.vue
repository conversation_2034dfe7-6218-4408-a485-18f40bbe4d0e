<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

const orderPublishedModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const router = useRouter();

const openOrderPublishedModal = () => {
  console.log('openOrderPublishedModal');
  orderPublishedModalRef.value?.openModal();
};

const goToCustomerDownloadApp = () => {
  router.push({
    name: 'customerappdownload'
  });
};

const goToMesureOrderList = () => {
  router.push({
    name: 'customerorderlist'
  });
};

defineExpose({ openOrderPublishedModal });
</script>

<template>
  <DefaultModal title="訂單已成立" :click-outside-close="false" :show-close-button="false" ref="orderPublishedModalRef">
    <div class="flex-col m-3 text-black">
      <p>恭喜，您的訂單已成立</p>
      <p>專人將會到府服務</p>
      <p>歡迎下載家易App</p>
      <p>讓您即時查看訂單的詳細資料</p>
      <p>您也可以留在網頁查看您刊登的訂單紀錄</p>
      <div
        class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button type="button"
                class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
                @click="goToCustomerDownloadApp()">前往下載App
        </button>
        <button type="button"
                class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
                @click="goToMesureOrderList()">網頁查看訂單
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
